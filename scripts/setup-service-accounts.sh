#!/bin/bash
# Integrated Event Service Accounts 和 WIF 設定腳本

set -e  # 遇到錯誤立即退出

# 設定變數
PROJECT_ID="tagtoo-tracking"
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
REPO="Tagtoo/integrated-event"

# Service Account 名稱
RUNNER_SA_NAME="integrated-event-runner"
RUNNER_SA_EMAIL="${RUNNER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
TERRAFORM_SA_EMAIL="integrated-event-cicd@${PROJECT_ID}.iam.gserviceaccount.com"

# WIF 設定
POOL_ID="github-actions"
PROVIDER_ID="github"

echo "🚀 開始設定 Integrated Event Service Accounts 和 WIF"
echo "📋 專案: $PROJECT_ID"
echo "📋 專案編號: $PROJECT_NUMBER"
echo "📋 Repository: $REPO"
echo ""

# ============================================================================
# 1. 建立應用執行 Service Account
# ============================================================================
echo "🔐 建立應用執行 Service Account..."

if gcloud iam service-accounts describe $RUNNER_SA_EMAIL --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "⚠️  Service Account $RUNNER_SA_EMAIL 已存在"
else
    gcloud iam service-accounts create $RUNNER_SA_NAME \
        --project=$PROJECT_ID \
        --display-name="Integrated Event Runner" \
        --description="用於 integrated-event 專案的應用執行和 CI 測試"
    echo "✅ 建立 Service Account: $RUNNER_SA_EMAIL"
fi

# ============================================================================
# 2. 設定應用執行 Service Account 權限
# ============================================================================
echo ""
echo "🔑 設定應用執行 Service Account 權限..."

# BigQuery 權限
echo "  設定 BigQuery 權限..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/bigquery.jobUser"

# Cloud Functions 權限（如果服務部署為 Cloud Function）
echo "  設定 Cloud Functions 執行權限..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/cloudfunctions.invoker"

# Cloud Run 權限（如果服務部署為 Cloud Run）
echo "  設定 Cloud Run 執行權限..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/run.invoker"

# 基本的監控權限
echo "  設定監控權限..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/monitoring.metricWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/logging.logWriter"

echo "✅ 應用執行 Service Account 權限設定完成"

# ============================================================================
# 3. 檢查並建立 Workload Identity Pool
# ============================================================================
echo ""
echo "🌐 設定 Workload Identity Federation..."

# 檢查 Workload Identity Pool 是否存在
if gcloud iam workload-identity-pools describe $POOL_ID \
    --project=$PROJECT_ID --location="global" >/dev/null 2>&1; then
    echo "⚠️  Workload Identity Pool '$POOL_ID' 已存在"
else
    echo "  建立 Workload Identity Pool..."
    gcloud iam workload-identity-pools create $POOL_ID \
        --project=$PROJECT_ID \
        --location="global" \
        --display-name="GitHub Actions Pool" \
        --description="用於 GitHub Actions 的 Workload Identity Pool"
    echo "✅ 建立 Workload Identity Pool: $POOL_ID"
fi

# ============================================================================
# 4. 檢查並建立 OIDC Provider
# ============================================================================
echo ""
echo "🔗 設定 OIDC Provider..."

if gcloud iam workload-identity-pools providers describe $PROVIDER_ID \
    --project=$PROJECT_ID --location="global" \
    --workload-identity-pool=$POOL_ID >/dev/null 2>&1; then
    echo "⚠️  OIDC Provider '$PROVIDER_ID' 已存在"
else
    echo "  建立 OIDC Provider..."
    gcloud iam workload-identity-pools providers create-oidc $PROVIDER_ID \
        --project=$PROJECT_ID \
        --location="global" \
        --workload-identity-pool=$POOL_ID \
        --display-name="GitHub Actions Provider" \
        --description="用於 GitHub Actions 的 OIDC Provider" \
        --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner" \
        --attribute-condition="assertion.repository_owner == 'Tagtoo'" \
        --issuer-uri="https://token.actions.githubusercontent.com"
    echo "✅ 建立 OIDC Provider: $PROVIDER_ID"
fi

# ============================================================================
# 5. 綁定 Service Accounts 到 Workload Identity
# ============================================================================
echo ""
echo "🔒 綁定 Service Accounts 到 Workload Identity..."

# 綁定應用執行 Service Account
echo "  綁定應用執行 Service Account..."
gcloud iam service-accounts add-iam-policy-binding $RUNNER_SA_EMAIL \
    --project=$PROJECT_ID \
    --role="roles/iam.workloadIdentityUser" \
    --member="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/attribute.repository/$REPO"

# 綁定 Terraform 部署 Service Account（如果還沒綁定）
echo "  綁定 Terraform 部署 Service Account..."
gcloud iam service-accounts add-iam-policy-binding $TERRAFORM_SA_EMAIL \
    --project=$PROJECT_ID \
    --role="roles/iam.workloadIdentityUser" \
    --member="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/attribute.repository/$REPO" || echo "⚠️  可能已經綁定過"

echo "✅ Service Account WIF 綁定完成"

# ============================================================================
# 6. 輸出設定資訊
# ============================================================================
echo ""
echo "📋 設定完成！請將以下資訊設定到 GitHub Repository:"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "GitHub Repository Settings:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📋 Variables (Settings → Secrets and variables → Actions → Variables):"
echo "WIF_PROVIDER=projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/providers/$PROVIDER_ID"
echo "INTEGRATED_EVENT_RUNNER_SA=$RUNNER_SA_EMAIL"
echo "TERRAFORM_USER_SA=$TERRAFORM_SA_EMAIL"
echo ""
echo "🔐 Secrets (Settings → Secrets and variables → Actions → Secrets):"
echo "# 註解：目前 legacy-event-sync 服務不需要額外的 secrets"
echo "# LEGACY_EVENT_SYNC_PROD_SECRET_KEY=your-secure-random-key-here"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# ============================================================================
# 7. 驗證設定
# ============================================================================
echo ""
echo "🔍 驗證設定..."

echo "  檢查應用執行 Service Account 權限..."
gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.role)" \
    --filter="bindings.members:$RUNNER_SA_EMAIL"

echo ""
echo "  檢查 WIF 設定..."
echo "  WIF Provider 路徑: projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/providers/$PROVIDER_ID"

echo ""
echo "✅ 設定完成！"
echo ""
echo "📝 下一步:"
echo "1. 將上述 GitHub Secrets 設定到 Repository"
echo "2. 更新 CI/CD workflow 配置"
echo "3. 測試 CI/CD 管道"
