#!/bin/bash

# =============================================================================
# 版本發佈核心邏輯腳本
# =============================================================================
#
# 用途：處理版本號驗證、更新、git 操作等核心功能
# 呼叫：由 release.sh 呼叫，不應直接執行
#
# =============================================================================

set -euo pipefail

# 腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 顏色輸出（繼承自主腳本）
info() { echo -e "\033[0;34m[INFO]\033[0m $1"; }
success() { echo -e "\033[0;32m[SUCCESS]\033[0m $1"; }
warning() { echo -e "\033[1;33m[WARNING]\033[0m $1"; }
error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }

# 全域變數
CURRENT_VERSION=""
NEW_VERSION=""
TARGET_VERSION="$1"
# 是否建立本地 Git tag（由 release.sh 傳入 true/false）
CREATE_TAG="${2-false}"  # 預設 false

# 獲取當前版本號
get_current_version() {
    if [[ ! -f "${VERSION_FILE}" ]]; then
        error "版本檔案不存在：${VERSION_FILE}"
        exit 1
    fi

    CURRENT_VERSION=$(grep -oP "${VERSION_PATTERN}" "${VERSION_FILE}" | head -1)

    if [[ -z "${CURRENT_VERSION}" ]]; then
        error "無法從 ${VERSION_FILE} 中擷取版本號"
        error "請檢查版本號格式是否正確"
        exit 1
    fi

    info "當前版本：${CURRENT_VERSION}"
}

# 版本號驗證
validate_semver() {
    local version="$1"
    local semver_regex="^(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)(-[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?(\+[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?$"

    if [[ ! "${version}" =~ $semver_regex ]]; then
        error "版本號格式不符合 SemVer 規範：${version}"
        error "正確格式：MAJOR.MINOR.PATCH (例如：1.2.3)"
        return 1
    fi

    return 0
}

# 版本號比較
version_compare() {
    local version1="$1"
    local version2="$2"

    # 使用 python 進行版本比較
    python3 -c "
from packaging import version
import sys

v1 = version.parse('$version1')
v2 = version.parse('$version2')

if v1 < v2:
    sys.exit(1)  # v1 < v2
elif v1 > v2:
    sys.exit(2)  # v1 > v2
else:
    sys.exit(0)  # v1 = v2
"

    return $?
}

# 自動計算下一個版本號
calculate_next_version() {
    local increment_type="$1"
    local current="$CURRENT_VERSION"

    # 解析版本號
    local major=$(echo "$current" | cut -d. -f1)
    local minor=$(echo "$current" | cut -d. -f2)
    local patch=$(echo "$current" | cut -d. -f3)

    case "$increment_type" in
        "patch")
            patch=$((patch + 1))
            ;;
        "minor")
            minor=$((minor + 1))
            patch=0
            ;;
        "major")
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        *)
            error "不支援的遞增類型：$increment_type"
            error "支援的類型：patch, minor, major"
            return 1
            ;;
    esac

    echo "${major}.${minor}.${patch}"
}

# 處理目標版本號
process_target_version() {
    case "$TARGET_VERSION" in
        "patch"|"minor"|"major")
            NEW_VERSION=$(calculate_next_version "$TARGET_VERSION")
            info "自動計算版本號：${CURRENT_VERSION} → ${NEW_VERSION} (${TARGET_VERSION})"
            ;;
        *)
            if validate_semver "$TARGET_VERSION"; then
                NEW_VERSION="$TARGET_VERSION"
                info "使用指定版本號：${NEW_VERSION}"
            else
                exit 1
            fi
            ;;
    esac
}

# 檢查版本號是否已存在
check_version_exists() {
    if git tag | grep -q "^${SERVICE_NAME}-${NEW_VERSION}$"; then
        error "版本 ${NEW_VERSION} 的 Git tag 已存在"
        error "請使用不同的版本號或刪除現有 tag："
        error "  git tag -d ${SERVICE_NAME}-${NEW_VERSION}"
        error "  git push origin :refs/tags/${SERVICE_NAME}-${NEW_VERSION}"
        exit 1
    fi
}

# 檢查版本號遞增
check_version_increment() {
    version_compare "$CURRENT_VERSION" "$NEW_VERSION"
    local result=$?

    case $result in
        0)
            error "新版本號與當前版本相同：${NEW_VERSION}"
            exit 1
            ;;
        2)
            success "版本號遞增驗證通過：${CURRENT_VERSION} → ${NEW_VERSION}"
            ;;
        1)
            error "新版本號不能低於當前版本"
            error "當前版本：${CURRENT_VERSION}"
            error "新版本：${NEW_VERSION}"
            exit 1
            ;;
    esac
}

# 檢查工作目錄狀態
check_working_directory() {
    if [[ "$REQUIRE_CLEAN_WORKING_DIR" == "true" ]]; then
        if [[ -n "$(git status --porcelain)" ]]; then
            error "工作目錄有未提交的變更"
            warning "請先提交或暫存所有變更："
            git status --short
            exit 1
        fi
        success "工作目錄狀態乾淨"
    fi
}

# 檢查 Git 分支
check_git_branch() {
    local current_branch=$(git rev-parse --abbrev-ref HEAD)

    if [[ "$current_branch" != "$DEFAULT_BRANCH" ]]; then
        warning "當前不在預設分支 '${DEFAULT_BRANCH}' 上"
        warning "當前分支：${current_branch}"
        echo
        read -p "確定要繼續嗎？ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "已取消執行"
            exit 0
        fi
    fi
}

# 檢查 CHANGELOG
check_changelog() {
    if [[ "$REQUIRE_CHANGELOG" == "true" ]]; then
        if [[ -f "CHANGELOG.md" ]]; then
            if grep -q "## \[${NEW_VERSION}\]" CHANGELOG.md; then
                success "CHANGELOG 已包含版本 ${NEW_VERSION}"
            else
                error "CHANGELOG.md 中找不到版本 ${NEW_VERSION} 的記錄"
                error "請在 CHANGELOG.md 中新增："
                error "## [${NEW_VERSION}] - $(date +%Y-%m-%d)"
                exit 1
            fi
        else
            warning "未找到 CHANGELOG.md 檔案"
        fi
    fi
}

# 執行客製化驗證
run_custom_validations() {
    if [[ ${#CUSTOM_PRE_RELEASE_VALIDATIONS[@]} -gt 0 ]]; then
        info "執行客製化驗證..."

        for validation in "${CUSTOM_PRE_RELEASE_VALIDATIONS[@]}"; do
            info "執行：${validation}"
            if ! eval "$validation"; then
                error "客製化驗證失敗：${validation}"
                exit 1
            fi
        done

        success "所有客製化驗證通過"
    fi
}

# 更新版本號檔案
update_version_file() {
    info "更新版本號檔案：${VERSION_FILE}"

    # 建立備份
    cp "${VERSION_FILE}" "${VERSION_FILE}.backup"

    # 更新版本號
    if sed -i.tmp "s/version = \"${CURRENT_VERSION}\"/version = \"${NEW_VERSION}\"/g" "${VERSION_FILE}"; then
        rm -f "${VERSION_FILE}.tmp"
        success "版本號已更新：${CURRENT_VERSION} → ${NEW_VERSION}"
    else
        # 恢復備份
        mv "${VERSION_FILE}.backup" "${VERSION_FILE}"
        error "更新版本號失敗"
        exit 1
    fi

    # 清理備份
    rm -f "${VERSION_FILE}.backup"
}

# Git 提交（可選擇是否建立本地 tag）
git_commit() {
    info "建立 Git commit..."

    # 添加變更
    git add "${VERSION_FILE}"

    # 如果有 CHANGELOG，也一併添加
    if [[ -f "CHANGELOG.md" ]]; then
        git add "CHANGELOG.md"
    fi

    # 提交變更
    local commit_message="[release] bump version to ${NEW_VERSION}"
    git commit -m "$commit_message"

    if [[ "$CREATE_TAG" == "true" ]]; then
        # 建立 tag
        git tag "${SERVICE_NAME}-${NEW_VERSION}" -m "Release ${SERVICE_NAME} version ${NEW_VERSION}"
        success "Git commit + tag 已建立：${SERVICE_NAME}-${NEW_VERSION}"
    else
        success "Git commit 已建立 (no local tag)"
    fi

    # 顯示 Git 歷史
    info "最近的 commits："
    git log --oneline -3
}

# 執行客製化後續動作
run_custom_post_actions() {
    if [[ ${#CUSTOM_POST_RELEASE_ACTIONS[@]} -gt 0 ]]; then
        info "執行客製化後續動作..."

        for action in "${CUSTOM_POST_RELEASE_ACTIONS[@]}"; do
            info "執行：${action}"
            if ! eval "$action"; then
                warning "客製化動作失敗（不影響發版）：${action}"
            fi
        done
    fi
}

# 顯示下一步指令
show_next_steps() {
    echo
    success "=== 版本發佈準備完成 ==="
    echo
    info "已完成的動作："
    info "  ✓ 版本號已更新：${CURRENT_VERSION} → ${NEW_VERSION}"
    info "  ✓ Git commit 已建立"
    echo
    warning "⚠️  下一步需要手動執行："
    echo
    echo "  1. 檢查變更內容："
    echo "     git show --stat"
    echo
    if [[ "$CREATE_TAG" == "true" ]]; then
        echo "  2. 推送 tag 觸發部署："
        echo "     git push ${GIT_REMOTE} ${SERVICE_NAME}-${NEW_VERSION}"
        echo
        echo "  3. 推送 commit（可選）："
        echo "     git push ${GIT_REMOTE} $(git rev-parse --abbrev-ref HEAD)"
    else
        echo "  2. 推送 commit（將由 CI 自動建立 Tag）："
        echo "     git push ${GIT_REMOTE} $(git rev-parse --abbrev-ref HEAD)"
        echo
        info "推送 commit 後將由 CI 建立 Tag 並自動觸發 CI/CD 部署到生產環境"
    fi
    echo
}

# 主要執行流程
main() {
    info "開始版本發佈流程..."

    # 1. 獲取當前版本
    get_current_version

    # 2. 處理目標版本
    process_target_version

    # 3. 版本號驗證
    check_version_exists
    check_version_increment

    # 4. 環境檢查
    check_working_directory
    check_git_branch
    check_changelog

    # 5. 執行客製化驗證
    run_custom_validations

    # 6. 更新版本號
    update_version_file

    # 7. Git 操作
    git_commit

    # 8. 執行後續動作
    run_custom_post_actions

    # 9. 顯示下一步
    show_next_steps

    success "核心發版流程完成"
}

# 檢查是否有必要的 python 套件
check_python_dependencies() {
    if ! python3 -c "import packaging.version" 2>/dev/null; then
        error "缺少 Python 套件：packaging"
        error "請安裝：pip install packaging"
        exit 1
    fi
}

# 初始化檢查
check_python_dependencies

# 執行主流程
main
