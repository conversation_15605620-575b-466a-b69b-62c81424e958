#!/bin/bash

# =============================================================================
# 版本發佈統一入口腳本
# =============================================================================
#
# 用途：統一的版本發佈腳本，支援自動配置偵測和彈性客製化
# 使用：cd apps/[service] && ../../scripts/release.sh [version|patch|minor|major]
#
# =============================================================================

set -euo pipefail

# 腳本目錄和專案根目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 輸出函數
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 顯示使用說明
show_usage() {
    cat << EOF
版本發佈腳本 - 使用說明

用途：自動化版本號管理和發佈流程

使用方法：
  cd apps/[service-name]
  ../../scripts/release.sh [VERSION] [--with-tag]

參數說明：
  VERSION     目標版本號或遞增類型
              - 明確版本號：1.2.3, 2.0.0
              - 自動遞增：patch, minor, major

範例：
  ../../scripts/release.sh 1.2.3            # 發佈版本 1.2.3
  ../../scripts/release.sh patch            # 自動遞增 patch 版本
  ../../scripts/release.sh 1.2.3 --with-tag # 同時建立本地 tag
  ../../scripts/release.sh minor    # 自動遞增 minor 版本
  ../../scripts/release.sh major    # 自動遞增 major 版本

檔案結構：
  apps/[service]/
  ├── pyproject.toml        # 版本號來源（必須）
  ├── release-config.sh     # 服務配置（可選）
  └── CHANGELOG.md          # 變更記錄（可選）

更多資訊請參考：docs/VERSION_MANAGEMENT.md
EOF
}

# 檢查是否在服務目錄中
check_service_directory() {
    if [[ ! -f "pyproject.toml" ]]; then
        error "找不到 pyproject.toml 檔案"
        error "請確認您在服務目錄中執行此腳本"
        echo
        info "正確的使用方式："
        info "  cd apps/[service-name]"
        info "  ../../scripts/release.sh [version]"
        exit 1
    fi

    local current_dir=$(basename "$(pwd)")
    local parent_dir=$(basename "$(dirname "$(pwd)")")

    if [[ "${parent_dir}" != "apps" ]]; then
        warning "當前目錄似乎不在 apps/ 下"
        warning "當前路徑：$(pwd)"
        echo
        read -p "確定要繼續嗎？ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "已取消執行"
            exit 0
        fi
    fi
}

# 載入預設配置
load_default_config() {
    # 預設配置值
    SERVICE_NAME=$(basename "$(pwd)")
    VERSION_FILE="pyproject.toml"
    VERSION_PATTERN='version = "([^"]+)"'
    DOCKER_REGISTRY="gcr.io/tagtoo-tracking"

    # 驗證設定
    REQUIRE_CHANGELOG=false
    REQUIRE_TESTS=false
    REQUIRE_CLEAN_WORKING_DIR=true

    # Git 設定
    GIT_REMOTE="origin"
    DEFAULT_BRANCH="main"

    # Terraform 設定
    TERRAFORM_DIR="terraform"
    TERRAFORM_VARS_FILE=""

    # 客製化鉤子
    CUSTOM_PRE_RELEASE_VALIDATIONS=()
    CUSTOM_POST_RELEASE_ACTIONS=()

    info "使用預設配置：SERVICE_NAME=${SERVICE_NAME}"
}

# 載入服務配置
load_service_config() {
    local config_file="./release-config.sh"

    if [[ -f "${config_file}" ]]; then
        info "載入服務配置：${config_file}"
        source "${config_file}"
        success "已載入服務專屬配置"
    else
        info "未找到 release-config.sh，使用預設配置"
    fi
}

# 驗證必要工具
check_dependencies() {
    local missing_tools=()

    command -v git >/dev/null 2>&1 || missing_tools+=("git")
    command -v python3 >/dev/null 2>&1 || missing_tools+=("python3")

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "缺少必要工具："
        for tool in "${missing_tools[@]}"; do
            error "  - ${tool}"
        done
        exit 1
    fi
}

# 主要入口函數
main() {
    echo
    info "=== 版本發佈腳本 ==="
    echo

    # 參數檢查
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi

    local target_version=""
    local with_tag_flag="false"

    # 解析參數（最多兩個：版本 + --with-tag）
    for arg in "$@"; do
        case "$arg" in
            --with-tag)
                with_tag_flag="true"
                ;;
            *)
                # 第一個非 --with-tag 視為版本參數
                if [[ -z "$target_version" ]]; then
                    target_version="$arg"
                fi
                ;;
        esac
    done

    if [[ -z "$target_version" ]]; then
        error "缺少 VERSION 參數"
        show_usage
        exit 1
    fi

    # 基本檢查
    check_service_directory
    check_dependencies

    # 載入配置
    load_default_config
    load_service_config

    # 顯示配置資訊
    info "服務名稱：${SERVICE_NAME}"
    info "版本檔案：${VERSION_FILE}"
    info "目標版本：${target_version}"
    if [[ "$with_tag_flag" == "true" ]]; then
        info "本地標籤：啟用 (--with-tag)"
    else
        info "本地標籤：停用（交由 CI 自動建立）"
    fi
    echo

    # 呼叫核心邏輯
    if ! "${SCRIPT_DIR}/release-core.sh" "${target_version}" "${with_tag_flag}"; then
        error "版本發佈失敗"
        exit 1
    fi

    echo
    success "=== 版本發佈腳本執行完成 ==="
}

# 執行主函數
main "$@"
