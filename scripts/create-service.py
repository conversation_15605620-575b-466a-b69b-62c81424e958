#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integrated Event Platform - 新服務建立腳本

此腳本用於從模板自動建立新的服務，包括：
1. 複製模板檔案
2. 替換模板變數
3. 更新相關配置檔案

使用方式:
    python scripts/create-service.py --config service-config.yml

或透過 Makefile:
    make create-service SERVICE_NAME=shopify-webhook DESCRIPTION="Shopify webhook integration"
"""

import os
import sys
import shutil
import yaml
import argparse
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, Any


def to_kebab_case(text: str) -> str:
    """轉換為 kebab-case"""
    # 移除特殊字元，保留英文字母、數字和空白
    text = re.sub(r'[^\w\s-]', '', text)
    # 將空白和底線轉換為連字符
    text = re.sub(r'[\s_]+', '-', text)
    # 轉換為小寫
    return text.lower().strip('-')


def to_pascal_case(text: str) -> str:
    """轉換為 PascalCase"""
    # 移除特殊字元
    text = re.sub(r'[^\w\s]', '', text)
    # 分割單字並首字母大寫
    words = re.split(r'[\s_-]+', text)
    return ''.join(word.capitalize() for word in words if word)


def to_snake_case(text: str) -> str:
    """轉換為 snake_case"""
    # 移除特殊字元
    text = re.sub(r'[^\w\s-]', '', text)
    # 將空白和連字符轉換為底線
    text = re.sub(r'[\s-]+', '_', text)
    return text.lower().strip('_')


class ServiceGenerator:
    """服務產生器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 取得部署類型
        self.deployment_type = config['service'].get('deployment_type', 'cloud-run')

        # 驗證部署類型
        supported_types = ['cloud-run', 'cloud-function', 'bigquery-only']
        if self.deployment_type not in supported_types:
            raise ValueError(f"不支援的部署類型: {self.deployment_type}。支援的類型: {', '.join(supported_types)}")

        # 設定模板目錄路徑
        self.template_base_dir = Path("apps/_template")
        self.common_template_dir = self.template_base_dir / "common"
        self.specific_template_dir = self.template_base_dir / self.deployment_type.replace('-', '_')
        self.project_root = Path.cwd()

        # 驗證模板目錄存在
        if not self.template_base_dir.exists():
            raise FileNotFoundError(f"模板基礎目錄不存在: {self.template_base_dir}")
        if not self.common_template_dir.exists():
            raise FileNotFoundError(f"共用模板目錄不存在: {self.common_template_dir}")
        if not self.specific_template_dir.exists():
            raise FileNotFoundError(f"特定模板目錄不存在: {self.specific_template_dir}")

        # 處理服務名稱格式
        service_name = config['service']['name']
        self.variables = {
            'SERVICE_NAME': service_name,
            'SERVICE_NAME_KEBAB': to_kebab_case(service_name),
            'SERVICE_NAME_PASCAL': to_pascal_case(service_name),
            'SERVICE_NAME_SNAKE': to_snake_case(service_name),
            'DESCRIPTION': config['service']['description'],
            'AUTHOR_NAME': config['service'].get('author', 'Tagtoo Data Team'),
            'AUTHOR_EMAIL': config['service'].get('email', '<EMAIL>'),
            'DEPLOYMENT_TYPE': self.deployment_type,
            'DATE': datetime.now().strftime('%Y-%m-%d'),
            'YEAR': datetime.now().strftime('%Y'),
        }

        self.service_dir = Path(f"apps/{self.variables['SERVICE_NAME_KEBAB']}")

    def validate_config(self) -> None:
        """驗證配置檔案"""
        required_fields = [
            'service.name',
            'service.description'
        ]

        for field in required_fields:
            keys = field.split('.')
            value = self.config

            try:
                for key in keys:
                    value = value[key]
                if not value:
                    raise ValueError(f"設定欄位 '{field}' 不能為空")
            except KeyError:
                raise ValueError(f"缺少必要設定欄位: {field}")

        # 檢查服務名稱格式
        service_name = self.variables['SERVICE_NAME_KEBAB']
        if not re.match(r'^[a-z][a-z0-9-]*[a-z0-9]$', service_name):
            raise ValueError(f"服務名稱格式不正確: {service_name}。必須使用 kebab-case 格式")

        # 檢查服務目錄是否已存在
        if self.service_dir.exists():
            raise ValueError(f"服務目錄已存在: {self.service_dir}")

    def copy_template_files(self) -> None:
        """複製模板檔案"""
        print(f"📁 複製模板檔案到 {self.service_dir}")

        # 建立服務目錄
        self.service_dir.mkdir(parents=True, exist_ok=True)

        # 複製共用檔案
        print("  📋 複製共用檔案...")
        for item in self.common_template_dir.iterdir():
            if item.is_dir():
                shutil.copytree(item, self.service_dir / item.name)
            else:
                shutil.copy2(item, self.service_dir)

        # 複製特定類型檔案
        print(f"  🎯 複製 {self.deployment_type} 特定檔案...")
        for item in self.specific_template_dir.iterdir():
            target_path = self.service_dir / item.name
            if item.is_dir():
                if target_path.exists():
                    # 如果目錄已存在，合併內容
                    shutil.copytree(item, target_path, dirs_exist_ok=True)
                else:
                    shutil.copytree(item, target_path)
            else:
                shutil.copy2(item, self.service_dir)

        # 移除不需要的檔案
        files_to_remove = [
            self.service_dir / ".github-template",  # GitHub Actions 會單獨處理
        ]

        for file_path in files_to_remove:
            if file_path.exists():
                if file_path.is_dir():
                    shutil.rmtree(file_path)
                else:
                    file_path.unlink()

        print(f"✅ 模板檔案複製完成")

    def replace_variables(self) -> None:
        """替換模板變數"""
        print("🔄 替換模板變數")

        # 需要處理的檔案類型
        text_extensions = {'.py', '.yml', '.yaml', '.md', '.txt', '.tf', '.ini', '.toml', '.env', '.flake8'}

        for root, dirs, files in os.walk(self.service_dir):
            for file in files:
                file_path = Path(root) / file

                # 跳過二進位檔案
                if file_path.suffix not in text_extensions:
                    continue

                try:
                    # 讀取檔案內容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 替換變數
                    original_content = content
                    for var_name, var_value in self.variables.items():
                        content = content.replace(f'{{{{{var_name}}}}}', var_value)

                    # 只有在內容有變化時才寫入
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        print(f"  ✅ {file_path.relative_to(self.service_dir)}")

                except Exception as e:
                    print(f"  ❌ 處理檔案失敗 {file_path}: {e}")

        print("✅ 變數替換完成")

    def update_root_makefile(self) -> None:
        """更新根目錄 Makefile"""
        print("📝 更新根目錄 Makefile")

        makefile_path = self.project_root / "Makefile"
        service_name = self.variables['SERVICE_NAME_KEBAB']

        if makefile_path.exists():
            # 讀取現有內容
            with open(makefile_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 檢查是否已經有此服務的配置
            if f"# {service_name} specific commands" not in content:
                # 在檔案末尾添加新服務的命令
                additional_commands = f"""
# {service_name} specific commands
test-{service_name}:
\t@echo "Testing {service_name} inside container..."
\t@docker-compose -f apps/{service_name}/docker-compose.yml exec {service_name} pytest tests/ -v

deploy-{service_name}-dev:
\t@echo "Deploying {service_name} to development..."
\t@cd apps/{service_name}/terraform && terraform apply -var="environment=dev"

deploy-{service_name}-prod:
\t@echo "Deploying {service_name} to production..."
\t@cd apps/{service_name}/terraform && terraform apply -var="environment=prod"
"""

                with open(makefile_path, 'a', encoding='utf-8') as f:
                    f.write(additional_commands)

                print(f"  ✅ 新增 {service_name} 相關命令")

        print("✅ Makefile 更新完成")

    def create_service_summary(self) -> None:
        """建立服務摘要"""
        deployment_specific_info = ""

        if self.deployment_type == "cloud-run":
            deployment_specific_info = f"""
📂 建立的檔案:
  ✅ 應用程式碼: {self.service_dir}/src/
  ✅ 測試程式碼: {self.service_dir}/tests/
  ✅ Docker 配置: {self.service_dir}/Dockerfile
  ✅ Terraform 配置: {self.service_dir}/terraform/

🚀 下一步 (Cloud Run 開發):
  1. 進入服務目錄: cd {self.service_dir}
  2. 建立環境變數檔案: cp .env.example .env (並視需要修改)
  3. 啟動開發環境: docker-compose up -d --build
  4. 實作業務邏輯: 編輯 src/main.py, tests/test_main.py
  5. 在容器內運行測試: make test-{self.variables['SERVICE_NAME_KEBAB']}
  6. 查看服務日誌: docker-compose logs -f
  7. 部署開發環境: make deploy-{self.variables['SERVICE_NAME_KEBAB']}-dev"""

        elif self.deployment_type == "cloud-function":
            deployment_specific_info = f"""
📂 建立的檔案:
  ✅ Function 程式碼: {self.service_dir}/main.py
  ✅ 需求檔案: {self.service_dir}/requirements.txt
  ✅ Terraform 配置: {self.service_dir}/terraform/

🚀 下一步 (Cloud Functions 開發):
  1. 進入服務目錄: cd {self.service_dir}
  2. 安裝依賴: pip install -r requirements.txt
  3. 本地測試: functions-framework --target=main --debug
  4. 實作業務邏輯: 編輯 main.py
  5. 部署開發環境: make deploy-{self.variables['SERVICE_NAME_KEBAB']}-dev"""

        elif self.deployment_type == "bigquery-only":
            deployment_specific_info = f"""
📂 建立的檔案:
  ✅ SQL 查詢: {self.service_dir}/sql/
  ✅ 腳本檔案: {self.service_dir}/scripts/
  ✅ Terraform 配置: {self.service_dir}/terraform/

🚀 下一步 (BigQuery 開發):
  1. 進入服務目錄: cd {self.service_dir}
  2. 編輯 SQL 查詢: sql/daily_aggregation.sql
  3. 測試 SQL: 使用 BigQuery Console 或 bq 命令列工具
  4. 部署排程: make deploy-{self.variables['SERVICE_NAME_KEBAB']}-dev"""

        summary = f"""
🎉 新服務建立完成！

📋 服務資訊:
  名稱: {self.variables['SERVICE_NAME']}
  識別碼: {self.variables['SERVICE_NAME_KEBAB']}
  部署類型: {self.deployment_type}
  描述: {self.variables['DESCRIPTION']}
  資料夾: {self.service_dir}
{deployment_specific_info}

🔄 CI/CD 整合:
  ✅ 服務會自動被 GitHub Actions 偵測並納入 CI/CD 流程
  ✅ 推送到 main 分支會自動部署到生產環境
  ✅ 推送到其他分支會觸發測試和開發環境部署

📚 詳細說明請參考: {self.service_dir}/README.md
"""
        print(summary)

    def generate(self) -> None:
        """產生新服務"""
        try:
            print(f"🚀 開始建立新服務: {self.variables['SERVICE_NAME']}")

            # 驗證配置
            self.validate_config()

            # 複製模板檔案
            self.copy_template_files()

            # 替換變數
            self.replace_variables()

            # 更新 Makefile
            self.update_root_makefile()

            # 顯示摘要
            self.create_service_summary()

        except Exception as e:
            print(f"❌ 建立服務失敗: {e}")

            # 清理已建立的檔案
            if self.service_dir.exists():
                shutil.rmtree(self.service_dir)
                print(f"🧹 已清理部分建立的檔案")

            sys.exit(1)


def load_config_from_args(args) -> Dict[str, Any]:
    """從命令列參數載入配置"""
    return {
        'service': {
            'name': args.name,
            'description': args.description,
            'author': args.author,
            'email': args.email,
        }
    }


def main():
    parser = argparse.ArgumentParser(description='建立新的整合事件服務')

    # 配置檔案方式
    parser.add_argument('--config', '-c', type=str,
                      help='服務配置檔案路徑 (YAML 格式)')

    # 命令列參數方式
    parser.add_argument('--name', '-n', type=str,
                      help='服務名稱')
    parser.add_argument('--description', '-d', type=str,
                      help='服務描述')
    parser.add_argument('--author', type=str, default='Tagtoo Data Team',
                      help='作者名稱')
    parser.add_argument('--email', type=str, default='<EMAIL>',
                      help='作者信箱')

    args = parser.parse_args()

    # 載入配置
    if args.config:
        # 從配置檔案載入
        config_path = Path(args.config)
        if not config_path.exists():
            print(f"❌ 配置檔案不存在: {config_path}")
            sys.exit(1)

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

    elif args.name and args.description:
        # 從命令列參數載入
        config = load_config_from_args(args)

    else:
        print("❌ 請提供配置檔案 (--config) 或服務名稱和描述 (--name, --description)")
        parser.print_help()
        sys.exit(1)

    # 建立服務
    generator = ServiceGenerator(config)
    generator.generate()


if __name__ == '__main__':
    main()
