#!/bin/bash

# =============================================================================
# 生產環境回滾腳本
# =============================================================================
#
# 用途：安全地回滾服務到指定版本
# 使用：cd apps/[service] && ../../scripts/rollback.sh [version]
#
# =============================================================================

set -euo pipefail

# 腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 輸出函數
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 顯示使用說明
show_usage() {
    cat << EOF
生產環境回滾腳本 - 使用說明

用途：安全地回滾服務到指定版本

使用方法：
  cd apps/[service-name]
  ../../scripts/rollback.sh [VERSION]

參數說明：
  VERSION     目標回滾版本號（必須是已存在的 Git tag）

範例：
  ../../scripts/rollback.sh 1.1.9    # 回滾到版本 1.1.9
  ../../scripts/rollback.sh 1.0.0    # 回滾到版本 1.0.0

注意事項：
  - 只能回滾到已存在的 Git tag 版本
  - 回滾會更新 Terraform deployment_version
  - 回滾不會修改本地 pyproject.toml 版本號
  - 建議在回滾前先備份當前狀態

檔案結構：
  apps/[service]/
  ├── terraform/           # Terraform 配置目錄
  └── pyproject.toml       # 當前版本資訊

更多資訊請參考：docs/VERSION_MANAGEMENT.md
EOF
}

# 檢查是否在服務目錄中
check_service_directory() {
    if [[ ! -f "pyproject.toml" ]]; then
        error "找不到 pyproject.toml 檔案"
        error "請確認您在服務目錄中執行此腳本"
        echo
        info "正確的使用方式："
        info "  cd apps/[service-name]"
        info "  ../../scripts/rollback.sh [version]"
        exit 1
    fi

    if [[ ! -d "terraform" ]]; then
        error "找不到 terraform 目錄"
        error "此服務似乎沒有 Terraform 配置"
        exit 1
    fi
}

# 載入配置（復用 release 配置）
load_config() {
    # 預設配置
    SERVICE_NAME=$(basename "$(pwd)")
    TERRAFORM_DIR="terraform"
    TERRAFORM_VARS_FILE=""

    # 載入服務配置（如果存在）
    if [[ -f "./release-config.sh" ]]; then
        info "載入服務配置：./release-config.sh"
        source "./release-config.sh"
    fi

    info "服務名稱：${SERVICE_NAME}"
    info "Terraform 目錄：${TERRAFORM_DIR}"
}

# 驗證目標版本
validate_target_version() {
    local target_version="$1"

    # 檢查版本號格式
    local semver_regex="^(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)(-[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?(\+[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?$"

    if [[ ! "${target_version}" =~ $semver_regex ]]; then
        error "版本號格式不符合 SemVer 規範：${target_version}"
        error "正確格式：MAJOR.MINOR.PATCH (例如：1.2.3)"
        exit 1
    fi

    # 檢查 Git tag 是否存在
    if ! git tag | grep -q "^${target_version}$"; then
        error "Git tag ${target_version} 不存在"
        echo
        info "可用的版本標籤："
        git tag --sort=-version:refname | head -10
        exit 1
    fi

    success "目標版本驗證通過：${target_version}"
}

# 獲取當前部署版本
get_current_deployment_version() {
    local current_version=""

    # 嘗試從 Terraform state 獲取
    if command -v terraform >/dev/null 2>&1; then
        cd "${TERRAFORM_DIR}"
        if [[ -f ".terraform/terraform.tfstate" ]] || [[ -f "terraform.tfstate" ]]; then
            current_version=$(terraform output -raw deployment_version 2>/dev/null || echo "")
        fi
        cd ..
    fi

    # 如果無法從 Terraform 獲取，嘗試從 git describe 獲取
    if [[ -z "$current_version" ]]; then
        current_version=$(git describe --tags --abbrev=0 2>/dev/null || echo "unknown")
    fi

    echo "$current_version"
}

# 確認回滾操作
confirm_rollback() {
    local target_version="$1"
    local current_version="$2"

    echo
    warning "=== 回滾確認 ==="
    warning "服務名稱：${SERVICE_NAME}"
    warning "當前版本：${current_version}"
    warning "目標版本：${target_version}"
    echo
    warning "⚠️  此操作將回滾生產環境到指定版本"
    warning "⚠️  請確認您了解此操作的影響"
    echo

    read -p "確定要執行回滾嗎？ (yes/no): " -r
    echo

    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        info "已取消回滾操作"
        exit 0
    fi
}

# 檢查 Terraform 狀態
check_terraform_status() {
    info "檢查 Terraform 狀態..."

    cd "${TERRAFORM_DIR}"

    # 檢查 Terraform 是否已初始化
    if [[ ! -d ".terraform" ]]; then
        warning "Terraform 尚未初始化"
        info "執行 terraform init..."
        terraform init
    fi

    # 檢查是否有待處理的變更
    if ! terraform plan -detailed-exitcode -var="deployment_version=$1" >/dev/null 2>&1; then
        local exit_code=$?
        if [[ $exit_code -eq 2 ]]; then
            info "偵測到 Terraform 需要套用變更"
        else
            error "Terraform plan 執行失敗"
            cd ..
            exit 1
        fi
    else
        info "Terraform 狀態檢查完成"
    fi

    cd ..
}

# 執行 Terraform 回滾
execute_terraform_rollback() {
    local target_version="$1"

    info "執行 Terraform 回滾..."

    cd "${TERRAFORM_DIR}"

    # 建立 plan 檔案
    local plan_file="rollback-${target_version}.tfplan"

    # 執行 plan
    info "生成 Terraform plan..."
    local terraform_vars=""
    if [[ -n "$TERRAFORM_VARS_FILE" && -f "$TERRAFORM_VARS_FILE" ]]; then
        terraform_vars="-var-file=$TERRAFORM_VARS_FILE"
    fi

    if terraform plan \
        -var="deployment_version=${target_version}" \
        $terraform_vars \
        -out="$plan_file"; then

        success "Terraform plan 生成完成"
    else
        error "Terraform plan 失敗"
        cd ..
        exit 1
    fi

    # 顯示 plan 內容
    echo
    info "Terraform 變更預覽："
    terraform show "$plan_file"
    echo

    # 最終確認
    read -p "確定要套用這些變更嗎？ (yes/no): " -r
    echo

    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        rm -f "$plan_file"
        cd ..
        info "已取消 Terraform 套用"
        exit 0
    fi

    # 執行 apply
    info "執行 Terraform apply..."
    if terraform apply "$plan_file"; then
        success "Terraform 回滾完成"
        rm -f "$plan_file"
    else
        error "Terraform apply 失敗"
        rm -f "$plan_file"
        cd ..
        exit 1
    fi

    cd ..
}

# 驗證回滾結果
verify_rollback() {
    local target_version="$1"

    info "驗證回滾結果..."

    # 檢查 Terraform output
    cd "${TERRAFORM_DIR}"
    local deployed_version=$(terraform output -raw deployment_version 2>/dev/null || echo "")
    cd ..

    if [[ "$deployed_version" == "$target_version" ]]; then
        success "回滾驗證通過：部署版本為 ${deployed_version}"
    else
        warning "部署版本可能不正確：${deployed_version} (預期：${target_version})"
    fi

    # TODO: 可以加入服務健康檢查
    # check_service_health "$target_version"
}

# 顯示回滾完成資訊
show_rollback_summary() {
    local target_version="$1"
    local start_time="$2"
    local end_time=$(date)

    echo
    success "=== 回滾操作完成 ==="
    echo
    info "服務名稱：${SERVICE_NAME}"
    info "回滾版本：${target_version}"
    info "開始時間：${start_time}"
    info "結束時間：${end_time}"
    echo
    info "後續建議動作："
    info "  1. 驗證服務功能正常"
    info "  2. 監控服務運行狀況"
    info "  3. 檢查相關 metrics 和 logs"
    echo
    warning "如需再次更新，請修復問題後重新發版"
    echo
}

# 主要函數
main() {
    local start_time=$(date)

    echo
    info "=== 生產環境回滾腳本 ==="
    echo

    # 參數檢查
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi

    local target_version="$1"

    # 基本檢查
    check_service_directory
    load_config

    # 版本驗證
    validate_target_version "$target_version"

    # 獲取當前版本
    local current_version=$(get_current_deployment_version)
    info "當前部署版本：${current_version}"

    # 確認回滾
    confirm_rollback "$target_version" "$current_version"

    # 檢查 Terraform
    check_terraform_status "$target_version"

    # 執行回滾
    execute_terraform_rollback "$target_version"

    # 驗證結果
    verify_rollback "$target_version"

    # 顯示總結
    show_rollback_summary "$target_version" "$start_time"
}

# 檢查必要工具
check_dependencies() {
    local missing_tools=()

    command -v git >/dev/null 2>&1 || missing_tools+=("git")
    command -v terraform >/dev/null 2>&1 || missing_tools+=("terraform")

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error "缺少必要工具："
        for tool in "${missing_tools[@]}"; do
            error "  - ${tool}"
        done
        exit 1
    fi
}

# 檢查工具並執行主函數
check_dependencies
main "$@"
