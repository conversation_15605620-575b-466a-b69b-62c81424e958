# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
*.tfvars
!*.tfvars.example
!infrastructure/terraform/shared/dev.tfvars
!infrastructure/terraform/shared/prod.tfvars
tfplan*
*.tfplan

# Docker
.dockerignore
docker-compose.override.yml

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
.nyc_output

# GCP
*.json
!*-example.json
.gcp/

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env.local
.env.*.local

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Generated files
generated/

# AI
CLAUDE.md
