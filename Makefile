# Integrated Event Platform - Makefile
# 常用命令快捷方式

# 專案設定
PROJECT_NAME := integrated-event
GCLOUD_CONFIG := tagtoo-tracking
GCLOUD_PROJECT := tagtoo-tracking
REGION := asia-east1

.PHONY: help dev-setup dev-up dev-down dev-clean test lint build deploy-dev deploy-staging deploy-prod check-gcloud-config

# 檢查並切換到正確的 gcloud configuration
check-gcloud-config:
	@echo "🔍 檢查 gcloud configuration..."
	@CURRENT_CONFIG=$$(gcloud config configurations list --filter="is_active:true" --format="value(name)" 2>/dev/null || echo "none"); \
	CURRENT_PROJECT=$$(gcloud config get-value project 2>/dev/null || echo "none"); \
	echo "📋 當前 configuration: $$CURRENT_CONFIG"; \
	echo "📋 當前 project: $$CURRENT_PROJECT"; \
	if [ "$$CURRENT_CONFIG" != "$(GCLOUD_CONFIG)" ]; then \
		echo "⚠️  需要切換到 $(GCLOUD_CONFIG) configuration"; \
		echo "🔄 正在切換 gcloud configuration..."; \
		gcloud config configurations activate $(GCLOUD_CONFIG) || \
		(echo "❌ 無法切換到 $(GCLOUD_CONFIG)，請確認 configuration 存在" && \
		 echo "💡 可用的 configurations:" && \
		 gcloud config configurations list && exit 1); \
		echo "✅ 已切換到 $(GCLOUD_CONFIG)"; \
		CURRENT_PROJECT=$$(gcloud config get-value project 2>/dev/null || echo "none"); \
		echo "📋 切換後的 project: $$CURRENT_PROJECT"; \
	else \
		echo "✅ 當前已使用正確的 configuration: $(GCLOUD_CONFIG)"; \
	fi; \
	if [ "$$CURRENT_PROJECT" != "$(GCLOUD_PROJECT)" ]; then \
		echo "⚠️  Project 不匹配，當前: $$CURRENT_PROJECT，預期: $(GCLOUD_PROJECT)"; \
		echo "❌ 請檢查 gcloud configuration 設定"; \
		exit 1; \
	else \
		echo "✅ Project 配置正確: $(GCLOUD_PROJECT)"; \
	fi

# 顯示幫助資訊
help:
	@echo "Integrated Event Platform - Available Commands:"
	@echo ""
	@echo "🔧 Configuration:"
	@echo "  check-gcloud-config - 檢查並切換到正確的 gcloud configuration (tagtoo-tracking)"
	@echo ""
	@echo "Development:"
	@echo "  dev-setup     - 設定開發環境"
	@echo "  dev-up        - 啟動本地服務"
	@echo "  dev-down      - 停止本地服務"
	@echo "  dev-clean     - 清理本地環境"
	@echo ""
	@echo "Testing:"
	@echo "  test          - 運行所有測試"
	@echo "  test-unit     - 運行單元測試"
	@echo "  test-integration - 運行整合測試"
	@echo "  test-e2e      - 運行端到端測試"
	@echo "  lint          - 代碼檢查"
	@echo ""
	@echo "Build & Deploy: (自動檢查 gcloud configuration)"
	@echo "  build         - 建置所有服務"
	@echo "  deploy-dev    - 部署到開發環境"
	@echo "  deploy-staging - 部署到測試環境"
	@echo "  deploy-prod   - 部署到生產環境"
	@echo ""
	@echo "Schema Management: (自動檢查 gcloud configuration)"
	@echo "  schema-status       - 檢視 schema 狀態"
	@echo "  schema-update-dev   - 更新開發環境 schema"
	@echo "  schema-update-prod  - 更新生產環境 schema"
	@echo "  schema-diff-dev     - 比較本地與開發環境 schema"
	@echo "  schema-diff-prod    - 比較本地與生產環境 schema"
	@echo "  schema-validate     - 驗證 schema 格式"
	@echo ""
	@echo "Service Management:"
	@echo "  create-service SERVICE_NAME='Name' DESCRIPTION='Desc' - 建立新服務"
	@echo "  create-service-from-config CONFIG=config.yml         - 從配置檔建立服務"
	@echo "  list-services                                        - 列出所有服務"
	@echo "  Example: make create-service SERVICE_NAME='Shopify Webhook' DESCRIPTION='Shopify integration'"
	@echo ""
	@echo "Operations: (自動檢查 gcloud configuration)"
	@echo "  logs          - 查看服務日誌"
	@echo "  status        - 檢查服務狀態"
	@echo "  monitor       - 開啟監控 Dashboard"
	@echo "  backup        - 資料備份"

# 開發環境設定
dev-setup:
	@echo "Setting up development environment..."
	@python3 -m venv venv
	@source venv/bin/activate && pip install -r requirements-dev.txt
	@docker --version || (echo "Please install Docker Desktop" && exit 1)
	@gcloud --version || (echo "Please install Google Cloud CLI" && exit 1)
	@terraform --version || (echo "Please install Terraform" && exit 1)
	@echo "Development environment setup complete!"

# 啟動本地服務
dev-up:
	@echo "Starting local development services..."
	@docker-compose up -d
	@echo "Services started! Check with 'docker-compose ps'"

# 停止本地服務
dev-down:
	@echo "Stopping local development services..."
	@docker-compose down
	@echo "Services stopped!"

# 清理本地環境
dev-clean:
	@echo "Cleaning up local environment..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "Local environment cleaned!"

# 測試
test:
	@echo "Running all tests..."
	@python -m pytest tests/ -v --cov=apps --cov=libs

test-unit:
	@echo "Running unit tests..."
	@python -m pytest tests/unit/ -v

test-integration:
	@echo "Running integration tests..."
	@python -m pytest tests/integration/ -v

test-e2e:
	@echo "Running end-to-end tests..."
	@python -m pytest tests/e2e/ -v

# 代碼檢查
lint:
	@echo "Running code quality checks..."
	@black --check apps/ libs/
	@flake8 apps/ libs/
	@mypy apps/ libs/

# 格式化代碼
format:
	@echo "Formatting code..."
	@black apps/ libs/
	@isort apps/ libs/

# 建置
build: check-gcloud-config
	@echo "Building all services..."
	@./pipelines/scripts/build-and-push.sh

# 部署到開發環境
deploy-dev: check-gcloud-config
	@echo "Deploying to development environment..."
	@./pipelines/scripts/deploy.sh dev

# 部署到測試環境
deploy-staging: check-gcloud-config
	@echo "Deploying to staging environment..."
	@./pipelines/scripts/deploy.sh staging

# 部署到生產環境
deploy-prod: check-gcloud-config
	@echo "Deploying to production environment..."
	@./pipelines/scripts/deploy.sh prod

# 查看日誌
logs: check-gcloud-config
	@echo "Showing service logs..."
	@gcloud run services logs tail --region=asia-east1

# 檢查服務狀態
status: check-gcloud-config
	@echo "Checking service status..."
	@gcloud run services list --region=asia-east1

# 開啟監控 Dashboard
monitor:
	@echo "Opening monitoring dashboard..."
	@open "https://console.cloud.google.com/monitoring/dashboards"

# 資料備份
backup:
	@echo "Creating data backup..."
	@# 這裡會實作資料備份邏輯
	@echo "Backup completed!"

# Terraform 操作
terraform-init:
	@cd infrastructure/terraform && terraform init

terraform-plan:
	@cd infrastructure/terraform && terraform plan

terraform-apply:
	@cd infrastructure/terraform && terraform apply

terraform-destroy:
	@cd infrastructure/terraform && terraform destroy

# 安全掃描
security-scan:
	@echo "Running security scans..."
	@# 這裡會實作安全掃描邏輯
	@echo "Security scan completed!"

# 服務管理
create-service:
	@echo "Creating new service..."
	@if [ -z "$(SERVICE_NAME)" ] || [ -z "$(DESCRIPTION)" ]; then \
		echo "Usage: make create-service SERVICE_NAME=your-service DESCRIPTION='Your description'"; \
		echo "Example: make create-service SERVICE_NAME=shopify-webhook DESCRIPTION='Shopify webhook integration'"; \
		exit 1; \
	fi
	@python3 scripts/create-service.py --name "$(SERVICE_NAME)" --description "$(DESCRIPTION)"

create-service-from-config:
	@echo "Creating new service from config file..."
	@if [ -z "$(CONFIG)" ]; then \
		echo "Usage: make create-service-from-config CONFIG=path/to/config.yml"; \
		echo "Example: make create-service-from-config CONFIG=shopify-webhook-config.yml"; \
		exit 1; \
	fi
	@python3 scripts/create-service.py --config "$(CONFIG)"

list-services:
	@echo "Available services:"
	@find apps/ -maxdepth 1 -type d ! -name "_template" ! -name "apps" | sed 's|apps/|  - |' | sort

# reurl specific commands
test-reurl:
	@echo "Testing reurl..."
	@cd apps/reurl && make test

deploy-reurl-dev: check-gcloud-config
	@echo "Deploying reurl to development..."
	@cd apps/reurl && make deploy-dev

deploy-reurl-prod: check-gcloud-config
	@echo "Deploying reurl to production..."
	@cd apps/reurl && make deploy-prod

# ======== Schema Management ========

# 檢視 schema 狀態
schema-status: check-gcloud-config
	@echo "📊 BigQuery Schema 狀態"
	@echo ""
	@echo "🔵 Development Environment:"
	@bq show --schema tagtoo-tracking:event_test.integrated_event | head -5 || echo "  ❌ 表格不存在或無權限"
	@echo ""
	@echo "🟢 Production Environment:"
	@bq show --schema tagtoo-tracking:event_prod.integrated_event | head -5 || echo "  ❌ 表格不存在或無權限"
	@echo ""
	@echo "📁 Local Schema Files:"
	@ls -la infrastructure/terraform/schema/

# 更新開發環境 schema
schema-update-dev: check-gcloud-config
	@echo "🔄 更新開發環境 BigQuery Schema..."
	@if [ ! -f infrastructure/terraform/schema/integrated_event.json ]; then \
		echo "❌ Schema 檔案不存在: infrastructure/terraform/schema/integrated_event.json"; \
		exit 1; \
	fi
	@echo "  📊 目標: tagtoo-tracking.event_test.integrated_event"
	@bq update tagtoo-tracking:event_test.integrated_event infrastructure/terraform/schema/integrated_event.json
	@echo "✅ 開發環境 schema 更新完成"

# 更新生產環境 schema
schema-update-prod: check-gcloud-config
	@echo "🔄 更新生產環境 BigQuery Schema..."
	@echo "⚠️  注意: 這將影響生產環境，請確認變更內容！"
	@echo "🛑 請按 Enter 繼續，或 Ctrl+C 取消..."
	@read
	@if [ ! -f infrastructure/terraform/schema/integrated_event.json ]; then \
		echo "❌ Schema 檔案不存在: infrastructure/terraform/schema/integrated_event.json"; \
		exit 1; \
	fi
	@echo "  📊 目標: tagtoo-tracking.event_prod.integrated_event"
	@bq update tagtoo-tracking:event_prod.integrated_event infrastructure/terraform/schema/integrated_event.json
	@echo "✅ 生產環境 schema 更新完成"

# 比較本地與開發環境 schema
schema-diff-dev: check-gcloud-config
	@echo "🔍 比較本地 schema 與開發環境..."
	@echo "📁 Local schema:"
	@cat infrastructure/terraform/schema/integrated_event.json
	@echo ""
	@echo "🔵 Development environment schema:"
	@bq show --schema --format=prettyjson tagtoo-tracking:event_test.integrated_event
	@echo ""
	@echo "💡 提示: 使用 'make schema-update-dev' 來同步"

# 比較本地與生產環境 schema
schema-diff-prod: check-gcloud-config
	@echo "🔍 比較本地 schema 與生產環境..."
	@echo "📁 Local schema:"
	@cat infrastructure/terraform/schema/integrated_event.json
	@echo ""
	@echo "🟢 Production environment schema:"
	@bq show --schema --format=prettyjson tagtoo-tracking:event_prod.integrated_event
	@echo ""
	@echo "💡 提示: 使用 'make schema-update-prod' 來同步"

# 驗證 schema 格式
schema-validate:
	@echo "✅ 驗證 schema 檔案格式..."
	@echo "📁 檢查 integrated_event.json:"
	@python3 -m json.tool infrastructure/terraform/schema/integrated_event.json > /dev/null && echo "  ✅ JSON 格式正確" || echo "  ❌ JSON 格式錯誤"
	@echo "📁 檢查 tagtoo_event.json:"
	@python3 -m json.tool infrastructure/terraform/schema/tagtoo_event.json > /dev/null && echo "  ✅ JSON 格式正確" || echo "  ❌ JSON 格式錯誤"
	@echo ""
	@echo "🔍 Schema 欄位檢查:"
	@python3 -c "import json; schema = json.load(open('infrastructure/terraform/schema/integrated_event.json')); required_fields = [field for field in schema if field.get('mode') == 'REQUIRED']; print(f'  📌 必填欄位數量: {len(required_fields)}'); [print(f'    - {field[\"name\"]} ({field[\"type\"]})') for field in required_fields]"

# 測試 schema 相容性
test-schema-compatibility:
	@echo "🧪 測試 schema 變更相容性..."
	@cd apps/legacy-event-sync && make test
	@echo "✅ Schema 相容性測試通過"

# ======== End Schema Management ========
