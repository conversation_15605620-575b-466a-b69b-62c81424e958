# 版本管理策略文檔

## 📋 **概覽**

本專案採用**混合方案**的版本管理策略，結合全域核心邏輯和本地配置檔案，實現統一但具備彈性的版本管理機制。

## 🏗️ **架構設計**

### **核心理念**

- **統一入口**：所有服務使用相同的發版腳本
- **彈性配置**：每個服務可自訂特殊需求
- **版本一致性**：`pyproject.toml` 為單一真相來源
- **手動確認**：最終推送前的人工檢查點

### **目錄結構**

```
integrated-event/
├── scripts/
│   ├── release.sh              # 統一入口腳本（自動偵測配置）
│   ├── release-core.sh         # 核心邏輯（版本處理、git 操作）
│   ├── rollback.sh             # 生產環境回滾腳本
│   └── utils/
│       ├── version-check.sh    # 版本號驗證工具
│       └── changelog-check.sh  # CHANGELOG 完整性檢查
│
├── apps/[service-name]/
│   ├── release-config.sh       # 服務專屬配置（可選）
│   ├── pyproject.toml          # 版本號來源
│   └── CHANGELOG.md            # 變更記錄
│
└── apps/_template/*/
    └── release-config.sh       # 新服務配置模板
```

## 🎯 **版本號策略**

### **SemVer 標準**

- 格式：`MAJOR.MINOR.PATCH`
- 範例：`1.2.3`
- 遵循 [Semantic Versioning 2.0.0](https://semver.org/) 規範

### **版本遞增規則**

| 異動類型                  | 版本遞增 | 範例          | 說明                  |
| ------------------------- | -------- | ------------- | --------------------- |
| `BREAKING CHANGE:`        | MAJOR    | 1.2.3 → 2.0.0 | 不向後相容的 API 變更 |
| `feat:`                   | MINOR    | 1.2.3 → 1.3.0 | 新功能新增            |
| `fix:`                    | PATCH    | 1.2.3 → 1.2.4 | 錯誤修復              |
| `docs:` `chore:` `style:` | PATCH    | 1.2.3 → 1.2.4 | 維護性更新            |

### **標籤命名策略（2025-07-10 起生效）**

| 產物               | 命名格式                               | 範例                                             |
| ------------------ | -------------------------------------- | ------------------------------------------------ |
| **Git Tag**        | `<service-name>-<version>`             | `legacy-event-sync-1.2.3`                        |
| **GitHub Release** | 同 Git Tag（自動建立）                 | `legacy-event-sync-1.2.3`                        |
| **Docker Image**   | `gcr.io/<project>/<service>:<version>` | `gcr.io/tagtoo-tracking/legacy-event-sync:1.2.3` |
| **Terraform**      | `deployment_version = "<version>"`     | `deployment_version = "1.2.3"`                   |

> **說明**：
>
> 1. 版本號仍遵循 SemVer；僅在 Git Tag 前加上服務名稱前綴，避免 mono-repo 內多服務 Tag 衝突。
> 2. 推送 commit 後，由 GitHub Actions `auto-tag.yml` 偵測版本遞增並自動建立 Git Tag。若需手動標籤，可在執行 `release.sh <version> --with-tag` 時同步建立本地 Tag。
> 3. 現有 `release.yml` 仍以 `<service>-<version>` Tag 觸發發版流程，向後相容。

## 🔧 **使用方法**

### **基本發版流程**

```bash
# 1. 進入服務目錄
cd apps/legacy-event-sync

# 2. 執行發版腳本（會自動載入配置）
../../scripts/release.sh 1.2.3

# 3. 檢查腳本輸出的資訊
# 4. 推送 commit（CI 會自動建立 Tag）
git push origin $(git rev-parse --abbrev-ref HEAD)

# （可選）若想手動建立並推送 Tag，可：
# ../../scripts/release.sh 1.2.3 --with-tag
# git push origin legacy-event-sync-1.2.3
```

### **自動版本遞增**

```bash
# 根據 conventional commits 自動計算下一版本
../../scripts/release.sh patch   # 1.2.3 → 1.2.4
../../scripts/release.sh minor   # 1.2.3 → 1.3.0
../../scripts/release.sh major   # 1.2.3 → 2.0.0
```

### **生產環境回滾**

```bash
# 回滾到指定版本
../../scripts/rollback.sh 1.1.9

# 腳本會：
# 1. 驗證目標版本存在
# 2. 更新 Terraform deployment_version
# 3. 執行 terraform apply
# 4. 確認回滾成功
```

## ⚙️ **配置檔案說明**

### **配置檔案位置**

- 路徑：`apps/[service-name]/release-config.sh`
- 狀態：**可選**（未設定時使用預設值）
- 作用：自訂服務專屬的發版行為

### **完整配置範例**

```bash
# apps/legacy-event-sync/release-config.sh

# === 基本設定 ===
SERVICE_NAME="legacy-event-sync"
VERSION_FILE="pyproject.toml"                    # 版本號來源檔案
VERSION_PATTERN='version = "([^"]+)"'            # 版本號擷取規則
DOCKER_REGISTRY="gcr.io/tagtoo-tracking"         # Docker Registry

# === 驗證設定 ===
REQUIRE_CHANGELOG=true                           # 是否檢查 CHANGELOG 更新
REQUIRE_TESTS=true                               # 是否要求測試通過
REQUIRE_CLEAN_WORKING_DIR=true                   # 是否要求工作目錄乾淨

# === 客製化鉤子 ===
CUSTOM_PRE_RELEASE_VALIDATIONS=(                # 發版前額外檢查
    "make test"                                  # 執行測試
    "make lint"                                  # 程式碼檢查
    "terraform fmt -check"                       # Terraform 格式檢查
)

CUSTOM_POST_RELEASE_ACTIONS=(                   # 發版後額外動作
    "echo 'Notifying team...'"                  # 團隊通知
    # "slack-notify.sh 'Released v${NEW_VERSION}'"  # Slack 通知（範例）
)

# === Git 設定 ===
GIT_REMOTE="origin"                             # Git remote 名稱
DEFAULT_BRANCH="main"                           # 預設分支

# === Terraform 設定 ===
TERRAFORM_DIR="terraform"                       # Terraform 目錄位置
TERRAFORM_VARS_FILE="terraform.tfvars"          # 變數檔案（可選）
```

### **預設值說明**

當 `release-config.sh` 不存在時，腳本使用以下預設值：

```bash
SERVICE_NAME=$(basename $(pwd))                 # 目錄名稱
VERSION_FILE="pyproject.toml"
VERSION_PATTERN='version = "([^"]+)"'
REQUIRE_CHANGELOG=false
REQUIRE_TESTS=false
REQUIRE_CLEAN_WORKING_DIR=true
GIT_REMOTE="origin"
DEFAULT_BRANCH="main"
```

## 🔍 **腳本執行流程**

### **release.sh 詳細流程**

```mermaid
graph TD
    A[執行 release.sh] --> B{偵測配置檔案}
    B -->|存在| C[載入 release-config.sh]
    B -->|不存在| D[使用預設配置]
    C --> E[解析目標版本號]
    D --> E
    E --> F[版本號驗證]
    F --> G[執行 pre-release 檢查]
    G --> H[更新 pyproject.toml]
    H --> I[git add & commit]
    I --> J[建立 git tag]
    J --> K[執行 post-release 動作]
    K --> L[顯示手動 push 指令]
```

### **驗證項目清單**

1. **版本號檢查**：

   - 格式是否符合 SemVer
   - 新版本是否大於當前版本
   - Git tag 是否已存在

2. **工作目錄檢查**：

   - 是否有未提交的變更
   - 是否在正確的分支上

3. **CHANGELOG 檢查**（可選）：

   - 是否包含新版本的記錄
   - 格式是否正確

4. **自訂驗證**（可選）：
   - 測試是否通過
   - 程式碼風格檢查
   - Terraform 格式驗證

## 📚 **最佳實踐**

### **發版前準備**

1. **確認異動內容**：

   ```bash
   git log --oneline $(git describe --tags --abbrev=0)..HEAD
   ```

2. **更新 CHANGELOG**：

   ```bash
   # 在 CHANGELOG.md 中新增版本記錄
   ## [1.2.3] - 2025-01-09
   ### 🚀 Features
   - feat: add new authentication system
   ```

3. **執行測試**：
   ```bash
   make test
   ```

### **發版後確認**

1. **驗證 GitHub Release**：

   - 檢查 GitHub Releases 頁面
   - 確認 CHANGELOG 內容正確

2. **監控部署狀態**：

   - GitHub Actions 執行狀況
   - 生產環境服務狀態

3. **驗證功能**：
   - 新功能是否正常運作
   - 關鍵流程是否受影響

### **團隊協作規範**

1. **發版權限**：

   - 只有專案維護者可以發版
   - 發版前需要 Code Review 通過

2. **溝通流程**：

   - 發版前在團隊群組告知
   - 重大版本更新需要提前規劃

3. **文檔維護**：
   - 版本發佈後更新相關文檔
   - API 變更需更新文檔

## 🚨 **故障排除**

### **常見問題**

**Q: 版本號驗證失敗**

```bash
# 檢查當前版本號
grep version pyproject.toml

# 檢查 git tags
git tag --sort=-version:refname | head -5
```

**Q: Git tag 已存在**

```bash
# 刪除錯誤的 tag
git tag -d 1.2.3
git push origin :refs/tags/1.2.3
```

**Q: 測試失敗**

```bash
# 檢查測試結果
make test

# 暫時跳過測試（不建議）
REQUIRE_TESTS=false ../../scripts/release.sh 1.2.3
```

### **緊急回滾程序**

1. **立即回滾**：

   ```bash
   ../../scripts/rollback.sh [previous_version]
   ```

2. **確認回滾狀態**：

   ```bash
   # 檢查 Terraform 狀態
   cd terraform && terraform show

   # 檢查服務狀態
   gcloud run services describe [service-name] --region=asia-east1
   ```

3. **修復問題後重新發版**：
   ```bash
   # 修復問題 → 測試 → 發版
   ../../scripts/release.sh 1.2.4
   ```

## 🔗 **相關資源**

- [Semantic Versioning 官方文檔](https://semver.org/)
- [Conventional Commits 規範](https://www.conventionalcommits.org/)
- [專案 CI/CD 文檔](../DEVELOPMENT.md#cicd-pipeline)
- [Terraform 部署指南](../infrastructure/terraform/shared/README.md)

---

**最後更新**：2025-07-10
**維護者**：DevOps Team
**版本**：v1.0.0
