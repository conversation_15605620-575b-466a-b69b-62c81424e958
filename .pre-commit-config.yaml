# Integrated Event Monorepo Pre-commit Hooks
# 根目錄配置原則：
# 1. 主要負責共用基礎設施 (infrastructure/terraform) 的完整檢查
# 2. 全域 terraform 格式檢查，確保與 GitHub Actions CI 一致
# 3. 高級別安全檢查，防止不安全配置
# 4. 各服務的詳細 terraform 檢查交由服務自己的 .pre-commit-config.yaml 處理

repos:
  # 通用 pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        exclude: ^apps/_template/
      - id: check-json
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-case-conflict

  # Terraform 相關檢查 (主要針對共用基礎設施)
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.83.2
    hooks:
      # 全域 Terraform 格式化 - 確保與 GitHub Actions CI 一致
      - id: terraform_fmt
        files: ^(infrastructure/terraform/.*\.tf|apps/.*/terraform/.*\.tf)$
        args:
          - --args=-recursive

      # 共用基礎設施的詳細驗證
      - id: terraform_validate
        files: ^infrastructure/terraform/.*\.tf$
        args:
          - --hook-config=--retry-once-with-cleanup=true
          - --hook-config=--tf-init-args=-backend=false

      # 共用基礎設施文檔生成
      - id: terraform_docs
        files: ^infrastructure/terraform/shared/.*\.tf$
        args:
          - --hook-config=--path-to-file=README.md
          - --hook-config=--add-to-existing-file=true
          - --hook-config=--create-file-if-not-exist=true

      # 全域安全檢查 - 確保沒有不安全的配置
      - id: terraform_tfsec
        files: ^(infrastructure/terraform/.*\.tf|apps/.*/terraform/.*\.tf)$
        args:
          - --args=--minimum-severity=CRITICAL
          - --args=--exclude=google-iam-no-privileged-service-accounts # CI/CD Service Account 需要管理權限

  # JSON/YAML 格式檢查
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: \.(json|yaml|yml|md)$
        exclude: ^(.*terraform/tfplan-.*|.*\.terraform/.*|.*\.tfstate.*|apps/_template/.*template.*|.*{{.*}}.*)

  # 自訂檢查
  - repo: local
    hooks:
      # TODO: 檢查有 terraform 的服務是否有正確的 pre-commit 配置
      # 目前跳過，因為 reurl 等服務還未配置 pre-commit
      # - id: service-precommit-check

      # 檢查 Terraform 模板中的佔位符
      - id: template-placeholder-check
        name: Template Placeholder Check
        entry: bash
        language: system
        files: ^apps/_template/.*\.tf$
        args:
          - -c
          - |
            echo "🔍 檢查模板佔位符..."
            if grep -r "{{.*}}" apps/_template/terraform/ 2>/dev/null; then
              echo "✅ 模板佔位符正常存在"
            else
              echo "⚠️  模板中沒有找到佔位符，請確認模板完整性"
            fi

      # 檢查共用基礎設施與服務配置的一致性
      - id: infrastructure-consistency-check
        name: Infrastructure Consistency Check
        entry: bash
        language: system
        files: ^(infrastructure/terraform/shared/.*\.tf|apps/.*/terraform/.*\.tf)$
        args:
          - -c
          - |
            echo "🔍 檢查基礎設施一致性..."
            # 檢查是否有服務使用了已棄用的配置，排除 .terraform 目錄
            if find apps/*/terraform/ -name "*.tf" -exec grep -l "allUsers\|unauthenticated" {} \; 2>/dev/null | grep -v ".terraform"; then
              echo "❌ 發現不安全的公開存取配置！"
              exit 1
            fi
            echo "✅ 基礎設施安全檢查通過"

# Pre-commit 配置
default_stages: [pre-commit, pre-push]
minimum_pre_commit_version: "3.0.0"

# CI 配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ""
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit autoupdate"
  autoupdate_schedule: weekly
  skip: []
  submodules: false
