# Integrated Event Platform - 專案索引

> 📋 此文件記錄專案的關鍵資訊，供 AI Agent 快速參考

## 🏗️ 專案基本資訊

- **專案名稱**: Integrated Event Platform
- **GCP 專案**: tagtoo-tracking
- **主要區域**: asia-east1
- **專案狀態**: 生產運行中 🚀
- **最後分析**: 2025-07-23

## 📊 核心服務狀態

### Legacy Event Sync ✅ 生產運行
- **服務名稱**: `legacy-event-sync-prod`
- **功能**: 同步 tagtoo_event → integrated_event
- **資料量**: 2.8 億筆記錄
- **排程**: 每 4 小時執行 (Cloud Scheduler)
- **架構**: Cloud Run + Cloud Tasks
- **狀態**: 穩定運行，已完成基礎同步

### ReURL 整合 ✅ 穩定運行
- **資料量**: 2,318 萬筆記錄
- **同步方式**: BigQuery Data Transfer Service
- **最新同步**: 2025-07-08 19:00:02
- **狀態**: 每日自動同步正常

### Shopify Webhook ✅ 已部署
- **功能**: Shopify 訂單資料整合
- **架構**: Cloud Run + Firestore + BigQuery
- **狀態**: 生產環境運行

## 🗄️ 資料庫 Schema

### BigQuery Datasets
- **生產環境**: `tagtoo-tracking.event_prod`
- **開發環境**: `tagtoo-tracking.event_test`

### 核心表格
- **integrated_event**: 統一事件資料表
- **tagtoo_event**: 原始事件資料 (來源)

### Schema 管理
- **位置**: `infrastructure/terraform/schema/`
- **主要檔案**: 
  - `integrated_event.json` - 整合事件表格 schema
  - `tagtoo_event.json` - 原始表格 schema 參考

## 🔧 開發環境

### 必要工具
- Docker Desktop
- Google Cloud CLI (gcloud)
- Terraform >= 1.7.0
- Python 3.11+
- Node.js 18+

### 重要指令
```bash
# 檢查 gcloud 配置
make check-gcloud-config

# Schema 管理
make schema-status
make schema-update-dev
make schema-diff-dev

# 服務管理
make list-services
make create-service SERVICE_NAME="Name" DESCRIPTION="Desc"

# 部署
make deploy-dev
make deploy-prod
```

## 📁 專案結構

### 核心目錄
- `apps/` - 應用服務
  - `legacy-event-sync/` - 歷史資料同步
  - `reurl/` - ReURL 資料整合
  - `shopify-webhook/` - Shopify 整合
  - `_template/` - 服務模板
- `infrastructure/terraform/` - 基礎設施
  - `shared/` - 共用資源
  - `schema/` - BigQuery schema
  - `modules/` - 可重用模組
- `libs/` - 共用函式庫
- `scripts/` - 自動化腳本

### 重要檔案
- `Makefile` - 主要操作指令
- `README.md` - 完整專案文件
- `DEVELOPMENT.md` - 開發指南
- `service-config.yml.example` - 服務配置範例

## 🚀 CI/CD 流程

### GitHub Actions
- **主要工作流**: `.github/workflows/apps-ci-cd.yml`
- **基礎設施**: `.github/workflows/infra-shared.yml`
- **自動標籤**: `.github/workflows/auto-tag.yml`

### 部署觸發
- **推送到 main**: 自動部署生產環境
- **Pull Request**: 執行測試
- **手動觸發**: 支援指定服務和環境

## 💰 成本監控

### BigQuery 成本追踪
- **功能**: 已實作成本追踪系統
- **監控**: 查詢費用和資料處理量
- **位置**: Legacy Event Sync 服務內建

## 🔍 監控和日誌

### 服務監控
```bash
# 檢查服務狀態
gcloud run services list --region=asia-east1

# 查看日誌
gcloud run services logs tail --region=asia-east1

# 監控 Dashboard
make monitor
```

### 健康檢查端點
- Legacy Event Sync: `/health`, `/sync-status`
- Shopify Webhook: `/health`
- ReURL: BigQuery Data Transfer 自動監控

## 🎯 開發規範

### Git Commit 格式
```
[type](scope): description

例如：
[feat](legacy-sync): 新增增量同步功能
[fix](shopify-webhook): 修復資料驗證問題
[docs](readme): 更新部署說明
```

### 分支策略
- `main` - 生產環境
- `develop` - 開發整合
- `feature/*` - 功能開發
- `hotfix/*` - 緊急修復

## 📋 待辦事項

### Phase 2 剩餘項目
- [ ] 資料品質驗證強化
- [ ] 端到端測試完善
- [ ] 監控 Dashboard 建立

### Phase 3 規劃
- [ ] 發票資料爬蟲 (`apps/invoice-crawler/`)
- [ ] CRM 連接器 (`apps/crm-connector/`)
- [ ] 合作夥伴資料同步 (`apps/partner-data-sync/`)

## 🔗 重要連結

- **GCP Console**: https://console.cloud.google.com/
- **BigQuery**: https://console.cloud.google.com/bigquery
- **Cloud Run**: https://console.cloud.google.com/run
- **監控**: https://console.cloud.google.com/monitoring

## 📞 聯絡資訊

- **技術問題**: 建立 GitHub Issue
- **緊急事件**: 檢查 Cloud Monitoring 告警
- **文件更新**: 直接編輯此檔案

---
**最後更新**: 2025-07-23
**維護者**: Tagtoo Data Team
