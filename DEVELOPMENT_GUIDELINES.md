# 🎯 Integrated Event 開發守則

## ⚠️ 重要守則

### 1. gcloud Configuration 檢查

**永遠記住：在執行任何 gcloud 指令前，務必先檢查當前的 active configuration！**

```bash
# 每次操作前的必要檢查
gcloud config configurations list
gcloud config get-value project

# 確保在正確的 project
gcloud config configurations activate tagtoo-tracking  # 生產環境
gcloud config configurations activate tagtoo-staging   # 測試環境
```

**避免的問題**：

- 在錯誤的 project 執行操作
- 看到錯誤的資源狀態
- 部署到錯誤的環境
- 修改錯誤環境的配置

### 2. 記憶體和效能優化守則

#### BigQuery 批次處理

- **批次大小**: 不超過 10,000 筆記錄
- **記憶體監控**: 定期檢查 Cloud Run 記憶體使用
- **SSL 穩定性**: 大批次容易導致 SSL 連線中斷

#### Cloud Run 配置

- **Worker timeout**: 至少 300 秒處理大量資料
- **記憶體分配**: 根據資料量動態調整
- **CPU 配置**: 考慮 I/O 密集型 vs CPU 密集型

### 3. 問題診斷流程

#### 服務異常時的檢查順序

1. **配置確認**: `gcloud config get-value project`
2. **服務狀態**: `gcloud run services list`
3. **任務狀態**: `gcloud tasks list --queue=[queue-name]`
4. **日誌分析**: `gcloud logging read` 檢查錯誤
5. **資料驗證**: 確認是否有資料成功寫入

### 4. AI 助手協作守則

#### 語言規範

- **Commit 訊息**: 務必使用**繁體中文**
- **文檔撰寫**: 使用繁體中文
- **變數命名**: 英文，但註解使用繁體中文

#### 每次對話開始時提醒

```
請遵循 .ai-assistant-config.yaml 中的設定：
- 使用繁體中文撰寫 commit 訊息
- 執行 gcloud 指令前檢查 configuration
- 部署前先 terraform refresh
```

## 🛠️ 技術最佳實踐

### Terraform

- 先 `terraform refresh` 確保狀態同步
- 部署前確認正確的 workspace 和變數

### Docker

- 使用固定版本標籤避免不可預測建置
- CI 環境加入重試機制處理網路問題

### 監控和警報

- 設定適當的健康檢查
- 監控記憶體使用和錯誤率
- 建立 SLA 指標和警報

### 2.5 Makefile 自動化配置檢查

**已整合自動檢查功能**

為了避免在錯誤的 GCP project 環境執行操作，所有與 GCP 相關的 Makefile 任務都會自動執行配置檢查：

```bash
# 這些命令會自動檢查並切換到 tagtoo-tracking configuration
make build          # 建置服務
make deploy-dev      # 部署到開發環境
make deploy-prod     # 部署到生產環境
make status          # 檢查服務狀態
make logs            # 查看服務日誌
make schema-status   # 檢查 BigQuery schema 狀態
```

**手動檢查命令**

```bash
# 可隨時手動檢查配置
make check-gcloud-config
make help           # 查看所有可用命令
```

**自動化保護機制**

- ✅ 自動檢測當前 gcloud configuration
- ✅ 自動切換到 `tagtoo-tracking` configuration
- ✅ 驗證 project 是否正確
- ✅ 顯示詳細的配置狀態資訊
- ❌ 配置不匹配時自動終止執行

---

_最後更新: 2025-07-11_
