# BigQuery Schema 管理

> 🎯 **簡化設計**: 適合 4 人小團隊的 schema 管理方案

## 📁 檔案說明

- `integrated_event.json`: 整合事件表格的 schema 定義
- `tagtoo_event.json`: 原始 tagtoo_event 表格的 schema 參考

## 🔄 Schema 變更流程

### 1. **開發階段**

```bash
# 1. 修改 schema 檔案
vim terraform/schema/integrated_event.json

# 2. 更新開發環境 BigQuery 表格
make schema-update-dev

# 3. 測試變更
make test-legacy-event-sync

# 4. 提交變更 (重要: 加上 [SCHEMA] 標籤)
git add terraform/schema/integrated_event.json
git commit -m "[SCHEMA] feat: 新增 location 和 raw_json 欄位"
```

### 2. **團隊協作**

```bash
# 當其他同事看到 [SCHEMA] commit 時
git pull
make schema-update-dev  # 同步本地開發環境
```

### 3. **部署生產環境**

```bash
# 合併到 main branch 後
make schema-update-prod  # 部署到生產環境
```

## 🛠️ 可用指令

### 基本 Schema 管理

```bash
# 檢查本地 schema 與 BigQuery 的差異
make schema-diff-dev
make schema-diff-prod

# 更新 BigQuery 表格 schema
make schema-update-dev
make schema-update-prod

# 檢視目前的 schema 狀態
make schema-status
```

### 開發測試

```bash
# 驗證 schema 格式
make schema-validate

# 測試 schema 變更對資料同步的影響
make test-schema-compatibility
```

## ⚠️ 注意事項

### Schema 變更規則

1. **向後相容**: 新增欄位使用 `mode: "NULLABLE"`
2. **欄位重新命名**: 先新增新欄位，再棄用舊欄位
3. **資料型別變更**: 需要資料遷移計畫

### Git 協作規範

- 🔥 **必須**: Schema 變更的 commit 加上 `[SCHEMA]` 標籤
- 📝 **建議**: 在 commit message 中說明變更原因
- 🔍 **檢查**: 其他同事看到 `[SCHEMA]` 標籤時需要 `make schema-update-dev`

### 環境對應

- **Dev**: `tagtoo-tracking.event_test.integrated_event`
- **Prod**: `tagtoo-tracking.event_prod.integrated_event`

## 🚨 故障排除

### 常見問題

1. **Schema 更新失敗**

   ```bash
   # 檢查權限
   gcloud auth list

   # 檢查表格是否存在
   bq show tagtoo-tracking:event_test.integrated_event
   ```

2. **Schema 衝突**

   ```bash
   # 檢查差異
   make schema-diff-dev

   # 手動比對
   bq show --schema tagtoo-tracking:event_test.integrated_event
   ```

3. **本地環境不同步**
   ```bash
   # 重新同步
   git pull
   make schema-update-dev
   ```

## 📚 參考資源

- [BigQuery Schema 最佳實踐](https://cloud.google.com/bigquery/docs/schemas)
- [專案開發守則](../README.md#開發規範和守則)
- [Legacy Event Sync 服務文檔](../apps/legacy-event-sync/README.md)
