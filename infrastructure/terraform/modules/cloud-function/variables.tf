# Cloud Function Module Variables

variable "function_name" {
  description = "Name of the Cloud Function"
  type        = string
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
}

variable "runtime" {
  description = "Runtime for the function"
  type        = string
  default     = "python311"
}

variable "entry_point" {
  description = "Entry point for the function"
  type        = string
  default     = "main"
}

variable "source_bucket" {
  description = "GCS bucket containing the source code"
  type        = string
}

variable "source_object" {
  description = "GCS object path for the source code"
  type        = string
}

variable "memory" {
  description = "Memory allocation for the function"
  type        = string
  default     = "256Mi"
}

variable "cpu" {
  description = "CPU allocation for the function"
  type        = string
  default     = "1"
}

variable "timeout" {
  description = "Timeout in seconds"
  type        = number
  default     = 60
}

variable "max_instances" {
  description = "Maximum number of instances"
  type        = number
  default     = 100
}

variable "min_instances" {
  description = "Minimum number of instances"
  type        = number
  default     = 0
}

variable "environment_variables" {
  description = "Environment variables for the function"
  type        = map(string)
  default     = {}
}

variable "secret_environment_variables" {
  description = "Secret environment variables from Secret Manager"
  type = map(object({
    secret_id = string
    version   = string
  }))
  default = {}
}

variable "service_account_email" {
  description = "Service account email"
  type        = string
}

variable "event_type" {
  description = "Event type for trigger"
  type        = string
  default     = null
}

variable "pubsub_topic" {
  description = "Pub/Sub topic for trigger"
  type        = string
  default     = null
}

variable "retry_policy" {
  description = "Retry policy for event trigger"
  type        = string
  default     = "RETRY_POLICY_RETRY"
}

variable "vpc_connector" {
  description = "VPC connector"
  type        = string
  default     = null
}

variable "vpc_connector_egress_settings" {
  description = "VPC connector egress settings"
  type        = string
  default     = "PRIVATE_RANGES_ONLY"
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
