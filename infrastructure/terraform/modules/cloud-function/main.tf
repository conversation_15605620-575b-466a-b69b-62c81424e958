# Reusable Cloud Function Module

resource "google_cloudfunctions2_function" "function" {
  name     = var.function_name
  location = var.region

  build_config {
    runtime     = var.runtime
    entry_point = var.entry_point

    source {
      storage_source {
        bucket = var.source_bucket
        object = var.source_object
      }
    }
  }

  service_config {
    max_instance_count = var.max_instances
    min_instance_count = var.min_instances

    available_memory = var.memory
    available_cpu    = var.cpu
    timeout_seconds  = var.timeout

    environment_variables = var.environment_variables

    # 支援 Secret Manager 的敏感環境變數
    dynamic "secret_environment_variables" {
      for_each = var.secret_environment_variables
      content {
        key        = secret_environment_variables.key
        secret     = secret_environment_variables.value.secret_id
        version    = secret_environment_variables.value.version
        project_id = var.project_id
      }
    }

    service_account_email = var.service_account_email

    vpc_connector                 = var.vpc_connector
    vpc_connector_egress_settings = var.vpc_connector_egress_settings
  }

  event_trigger {
    trigger_region = var.region
    event_type     = var.event_type
    pubsub_topic   = var.pubsub_topic
    retry_policy   = var.retry_policy
  }

  labels = var.labels

  depends_on = [
    google_project_service.required_apis
  ]
}

resource "google_project_service" "required_apis" {
  for_each = toset([
    "cloudfunctions.googleapis.com",
    "cloudbuild.googleapis.com",
    "artifactregistry.googleapis.com",
  ])

  service = each.value
  project = var.project_id

  disable_dependent_services = false
}
