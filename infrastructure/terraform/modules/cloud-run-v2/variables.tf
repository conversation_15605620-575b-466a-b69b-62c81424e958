# Cloud Run v2 服務模組變數

variable "service_name" {
  description = "服務名稱"
  type        = string
}

variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "environment" {
  description = "環境名稱"
  type        = string
}

variable "container_image" {
  description = "容器映像"
  type        = string
}

variable "service_account_email" {
  description = "服務帳號 email"
  type        = string
  default     = ""
}

variable "labels" {
  description = "標籤"
  type        = map(string)
  default     = {}
}

variable "env_vars" {
  description = "環境變數"
  type        = map(string)
  default     = {}
}

# 資源配置
variable "cpu_limit" {
  description = "CPU 限制"
  type        = string
  default     = "1000m"
}

variable "memory_limit" {
  description = "記憶體限制"
  type        = string
  default     = "512Mi"
}

variable "min_instances" {
  description = "最小實例數"
  type        = number
  default     = 0
}

variable "max_instances" {
  description = "最大實例數"
  type        = number
  default     = 10
}

variable "timeout" {
  description = "請求逾時時間 (秒)"
  type        = number
  default     = 300
}

variable "port" {
  description = "容器端口"
  type        = number
  default     = 8080
}

variable "health_check_path" {
  description = "健康檢查路徑"
  type        = string
  default     = "/health"
}

# Cloud Tasks 配置
variable "enable_cloud_tasks" {
  description = "是否啟用 Cloud Tasks"
  type        = bool
  default     = false
}

variable "cloud_tasks_max_concurrent" {
  description = "Cloud Tasks 最大並發數"
  type        = number
  default     = 10
}

variable "cloud_tasks_max_rate" {
  description = "Cloud Tasks 最大執行率"
  type        = number
  default     = 5
}

variable "cloud_tasks_max_retries" {
  description = "Cloud Tasks 最大重試次數"
  type        = number
  default     = 3
}

# Cloud Scheduler 配置
variable "enable_scheduler" {
  description = "是否啟用 Cloud Scheduler"
  type        = bool
  default     = false
}

variable "schedule_expression" {
  description = "排程表達式"
  type        = string
  default     = "0 */6 * * *"
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

variable "schedule_endpoint" {
  description = "排程觸發端點"
  type        = string
  default     = "/sync"
}

# 監控配置
variable "enable_monitoring" {
  description = "是否啟用監控"
  type        = bool
  default     = true
}

variable "error_threshold" {
  description = "錯誤閾值"
  type        = number
  default     = 0.1
}

variable "notification_channels" {
  description = "通知頻道"
  type        = list(string)
  default     = []
}

variable "depends_on_resources" {
  description = "依賴的資源"
  type        = list(any)
  default     = []
}
