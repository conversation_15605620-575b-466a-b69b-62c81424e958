# Cloud Run v2 服務模組輸出

output "service_name" {
  description = "Cloud Run 服務名稱"
  value       = google_cloud_run_v2_service.main.name
}

output "service_uri" {
  description = "Cloud Run 服務 URI"
  value       = google_cloud_run_v2_service.main.uri
}

output "service_id" {
  description = "Cloud Run 服務 ID"
  value       = google_cloud_run_v2_service.main.id
}

output "cloud_tasks_queue_name" {
  description = "Cloud Tasks 隊列名稱"
  value       = var.enable_cloud_tasks ? google_cloud_tasks_queue.main[0].name : null
}

output "cloud_scheduler_job_name" {
  description = "Cloud Scheduler 任務名稱"
  value       = var.enable_scheduler ? google_cloud_scheduler_job.main[0].name : null
}

output "security_status" {
  description = "安全狀態檢查"
  value = {
    service_account_configured = var.service_account_email != ""
    public_access_enabled      = var.environment == "dev"
    monitoring_enabled         = var.enable_monitoring
    health_checks_enabled      = var.health_check_path != ""
  }
}
