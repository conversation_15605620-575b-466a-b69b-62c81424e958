# Cloud Run v2 服務模組
# 標準化的 Cloud Run v2 服務部署

# Cloud Run v2 服務
resource "google_cloud_run_v2_service" "main" {
  name     = var.service_name
  location = var.region
  project  = var.project_id

  labels = var.labels

  template {
    labels = var.labels

    scaling {
      min_instance_count = var.min_instances
      max_instance_count = var.max_instances
    }

    timeout = "${var.timeout}s"

    containers {
      image = var.container_image

      resources {
        limits = {
          cpu    = var.cpu_limit
          memory = var.memory_limit
        }
      }

      dynamic "env" {
        for_each = var.env_vars
        content {
          name  = env.key
          value = env.value
        }
      }

      # 健康檢查
      dynamic "liveness_probe" {
        for_each = var.health_check_path != "" ? [1] : []
        content {
          http_get {
            path = var.health_check_path
            port = var.port
          }
          initial_delay_seconds = 30
          period_seconds        = 30
          timeout_seconds       = 10
          failure_threshold     = 3
        }
      }

      dynamic "startup_probe" {
        for_each = var.health_check_path != "" ? [1] : []
        content {
          http_get {
            path = var.health_check_path
            port = var.port
          }
          initial_delay_seconds = 10
          period_seconds        = 10
          timeout_seconds       = 5
          failure_threshold     = 10
        }
      }

      ports {
        container_port = var.port
      }
    }

    service_account = var.service_account_email
  }

  depends_on = [
    var.depends_on_resources
  ]
}

# Service Account 權限
resource "google_cloud_run_v2_service_iam_member" "service_account_invoker" {
  count = var.service_account_email != "" ? 1 : 0

  project  = var.project_id
  location = var.region
  name     = google_cloud_run_v2_service.main.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${var.service_account_email}"
}

# Cloud Tasks 隊列 (如果需要)
resource "google_cloud_tasks_queue" "main" {
  count = var.enable_cloud_tasks ? 1 : 0

  name     = "${var.service_name}-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_concurrent_dispatches = var.cloud_tasks_max_concurrent
    max_dispatches_per_second = var.cloud_tasks_max_rate
  }

  retry_config {
    max_attempts       = var.cloud_tasks_max_retries
    max_backoff        = "3600s"
    min_backoff        = "1s"
    max_doublings      = 16
    max_retry_duration = "0s"
  }

  stackdriver_logging_config {
    sampling_ratio = 0.1
  }
}

# Cloud Scheduler 任務 (如果需要)
resource "google_cloud_scheduler_job" "main" {
  count = var.enable_scheduler ? 1 : 0

  name      = "${var.service_name}-${var.environment}"
  region    = var.region
  project   = var.project_id
  schedule  = var.schedule_expression
  time_zone = var.schedule_timezone

  http_target {
    http_method = "POST"
    uri         = "${google_cloud_run_v2_service.main.uri}${var.schedule_endpoint}"

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      trigger     = "scheduler"
      environment = var.environment
    }))

    oidc_token {
      service_account_email = var.service_account_email
      audience              = google_cloud_run_v2_service.main.uri
    }
  }

  depends_on = [
    google_cloud_run_v2_service_iam_member.service_account_invoker
  ]
}

# 監控警報
resource "google_monitoring_alert_policy" "error_rate" {
  count = var.enable_monitoring ? 1 : 0

  display_name = "${var.service_name} Cloud Run Errors"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "Cloud Run error rate"

    condition_threshold {
      filter          = "metric.type=\"run.googleapis.com/request_count\" resource.type=\"cloud_run_revision\" resource.labels.service_name=\"${google_cloud_run_v2_service.main.name}\" metric.labels.response_code_class=\"5xx\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.error_threshold

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  alert_strategy {
    auto_close = "604800s"
  }

  notification_channels = var.notification_channels
}
