# BigQuery Scheduler 模組輸出

output "data_transfer_config_name" {
  description = "BigQuery Data Transfer 配置名稱"
  value       = var.enable_data_transfer ? google_bigquery_data_transfer_config.main[0].name : null
}

output "scheduler_job_name" {
  description = "Cloud Scheduler 任務名稱"
  value       = var.enable_scheduler ? google_cloud_scheduler_job.main[0].name : null
}

output "dataset_id" {
  description = "BigQuery 資料集 ID"
  value       = var.create_dataset ? google_bigquery_dataset.main[0].dataset_id : var.destination_dataset
}

output "table_id" {
  description = "BigQuery 表格 ID"
  value       = var.create_table ? google_bigquery_table.main[0].table_id : var.destination_table
}

output "execution_mode" {
  description = "執行模式"
  value = {
    data_transfer_enabled = var.enable_data_transfer
    scheduler_enabled     = var.enable_scheduler
  }
}
