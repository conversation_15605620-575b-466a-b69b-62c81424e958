# BigQuery Scheduler 模組變數

variable "service_name" {
  description = "服務名稱"
  type        = string
}

variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "environment" {
  description = "環境名稱"
  type        = string
}

variable "service_account_email" {
  description = "服務帳號 email"
  type        = string
}

variable "labels" {
  description = "標籤"
  type        = map(string)
  default     = {}
}

# 排程配置
variable "schedule_expression" {
  description = "排程表達式"
  type        = string
  default     = "0 */6 * * *"
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}

# BigQuery 配置
variable "query" {
  description = "要執行的 BigQuery 查詢"
  type        = string
}

variable "destination_dataset" {
  description = "目標資料集"
  type        = string
  default     = "integrated_event"
}

variable "destination_table" {
  description = "目標表格"
  type        = string
}

variable "destination_table_template" {
  description = "目標表格模板 (用於 Data Transfer)"
  type        = string
  default     = ""
}

variable "write_disposition" {
  description = "寫入模式"
  type        = string
  default     = "WRITE_TRUNCATE"
  validation {
    condition = contains([
      "WRITE_TRUNCATE",
      "WRITE_APPEND",
      "WRITE_EMPTY"
    ], var.write_disposition)
    error_message = "write_disposition 必須是 WRITE_TRUNCATE, WRITE_APPEND, 或 WRITE_EMPTY"
  }
}

# 分割配置
variable "partitioning_field" {
  description = "分割欄位"
  type        = string
  default     = ""
}

variable "partitioning_type" {
  description = "分割類型"
  type        = string
  default     = "DAY"
}

variable "clustering_fields" {
  description = "叢集化欄位"
  type        = list(string)
  default     = []
}

# 資料集配置
variable "create_dataset" {
  description = "是否建立資料集"
  type        = bool
  default     = false
}

variable "create_table" {
  description = "是否建立表格"
  type        = bool
  default     = false
}

variable "table_schema" {
  description = "表格結構 (JSON 字串)"
  type        = string
  default     = "[]"
}

variable "default_table_expiration_ms" {
  description = "預設表格過期時間 (毫秒)"
  type        = number
  default     = null
}

variable "dataset_access" {
  description = "資料集存取權限"
  type = list(object({
    role          = string
    user_by_email = string
  }))
  default = []
}

variable "kms_key_name" {
  description = "KMS 金鑰名稱"
  type        = string
  default     = ""
}

# 執行模式
variable "enable_data_transfer" {
  description = "是否使用 BigQuery Data Transfer"
  type        = bool
  default     = true
}

variable "enable_scheduler" {
  description = "是否使用 Cloud Scheduler"
  type        = bool
  default     = false
}

variable "trigger_uri" {
  description = "觸發 URI (用於 Cloud Scheduler)"
  type        = string
  default     = ""
}

# 監控配置
variable "enable_monitoring" {
  description = "是否啟用監控"
  type        = bool
  default     = true
}

variable "notification_channels" {
  description = "通知頻道"
  type        = list(string)
  default     = []
}
