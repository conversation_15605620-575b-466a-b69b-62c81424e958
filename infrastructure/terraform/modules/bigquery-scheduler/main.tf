# BigQuery Scheduler 模組
# 定期執行 BigQuery 查詢

# Data Transfer 配置 (用於定期執行 BigQuery 查詢)
resource "google_bigquery_data_transfer_config" "main" {
  count = var.enable_data_transfer ? 1 : 0

  display_name           = "${var.service_name}-${var.environment}"
  data_source_id         = "scheduled_query"
  schedule               = var.schedule_expression
  destination_dataset_id = var.destination_dataset
  location               = var.region
  project                = var.project_id

  params = {
    query                           = var.query
    destination_table_name_template = var.destination_table_template
    write_disposition               = var.write_disposition
    partitioning_field              = var.partitioning_field
  }

  service_account_name = var.service_account_email
}

# Cloud Scheduler 任務 (用於觸發自定義 BigQuery 任務)
resource "google_cloud_scheduler_job" "main" {
  count = var.enable_scheduler ? 1 : 0

  name      = "${var.service_name}-${var.environment}"
  region    = var.region
  project   = var.project_id
  schedule  = var.schedule_expression
  time_zone = var.schedule_timezone

  http_target {
    http_method = "POST"
    uri         = var.trigger_uri

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      query             = var.query
      destination_table = var.destination_table
      write_disposition = var.write_disposition
      environment       = var.environment
      service_name      = var.service_name
    }))

    oidc_token {
      service_account_email = var.service_account_email
      audience              = var.trigger_uri
    }
  }
}

# BigQuery 資料集 (如果需要建立)
resource "google_bigquery_dataset" "main" {
  count = var.create_dataset ? 1 : 0

  dataset_id    = "${var.service_name}_${var.environment}"
  project       = var.project_id
  friendly_name = "${var.service_name} ${var.environment} Dataset"
  description   = "Dataset for ${var.service_name} service in ${var.environment} environment"
  location      = var.region

  labels = var.labels

  # 資料保留設定
  default_table_expiration_ms = var.default_table_expiration_ms

  # 存取控制
  dynamic "access" {
    for_each = var.dataset_access
    content {
      role          = access.value.role
      user_by_email = access.value.user_by_email
    }
  }

  # 預設加密
  default_encryption_configuration {
    kms_key_name = var.kms_key_name
  }
}

# BigQuery 表格 (如果需要建立)
resource "google_bigquery_table" "main" {
  count = var.create_table ? 1 : 0

  dataset_id = var.create_dataset ? google_bigquery_dataset.main[0].dataset_id : var.destination_dataset
  table_id   = var.destination_table
  project    = var.project_id

  description = "Table for ${var.service_name} service data"
  labels      = var.labels

  # 時間分割
  dynamic "time_partitioning" {
    for_each = var.partitioning_field != "" ? [1] : []
    content {
      type  = var.partitioning_type
      field = var.partitioning_field
    }
  }

  # 叢集化
  clustering = length(var.clustering_fields) > 0 ? var.clustering_fields : null

  # 表格結構
  schema = var.table_schema

  depends_on = [
    google_bigquery_dataset.main
  ]
}

# 監控警報
resource "google_monitoring_alert_policy" "job_failure" {
  count = var.enable_monitoring ? 1 : 0

  display_name = "${var.service_name} BigQuery Job Failure"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "BigQuery job failure rate"

    condition_threshold {
      filter          = "metric.type=\"bigquery.googleapis.com/job/num_failed_jobs\" resource.type=\"bigquery_project\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = 0

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  alert_strategy {
    auto_close = "604800s"
  }

  notification_channels = var.notification_channels
}
