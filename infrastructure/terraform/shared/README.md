# shared

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->

## Requirements

| Name                                                                           | Version   |
| ------------------------------------------------------------------------------ | --------- |
| <a name="requirement_terraform"></a> [terraform](#requirement_terraform)       | >= 1.12.0 |
| <a name="requirement_google"></a> [google](#requirement_google)                | ~> 5.0    |
| <a name="requirement_google-beta"></a> [google-beta](#requirement_google-beta) | ~> 5.0    |

## Providers

| Name                                                                     | Version |
| ------------------------------------------------------------------------ | ------- |
| <a name="provider_google"></a> [google](#provider_google)                | 5.45.2  |
| <a name="provider_google-beta"></a> [google-beta](#provider_google-beta) | 5.45.2  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                                       | Type        |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------- |
| [google-beta_google_artifact_registry_repository.apps_repository](https://registry.terraform.io/providers/hashicorp/google-beta/latest/docs/resources/google_artifact_registry_repository) | resource    |
| [google_bigquery_dataset.integrated_event](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset)                                                | resource    |
| [google_bigquery_dataset_access.owner_team_data](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_access)                                   | resource    |
| [google_bigquery_dataset_access.writer_bq_transfer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_access)                                | resource    |
| [google_bigquery_dataset_access.writer_kubeflow_user](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_access)                              | resource    |
| [google_bigquery_dataset_access.writer_runtime_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_access)                                 | resource    |
| [google_bigquery_table.integrated_event](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_table)                                                    | resource    |
| [google_firestore_database.integrated_event](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/firestore_database)                                            | resource    |
| [google_iam_workload_identity_pool.shared_github_pool](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/iam_workload_identity_pool)                          | resource    |
| [google_iam_workload_identity_pool_provider.shared_github_provider](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/iam_workload_identity_pool_provider)    | resource    |
| [google_monitoring_notification_channel.email](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/monitoring_notification_channel)                             | resource    |
| [google_project_iam_member.cicd_artifactregistry_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                 | resource    |
| [google_project_iam_member.cicd_bigquery_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                         | resource    |
| [google_project_iam_member.cicd_cloudbuild_editor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                      | resource    |
| [google_project_iam_member.cicd_cloudfunctions_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                   | resource    |
| [google_project_iam_member.cicd_cloudrun_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                         | resource    |
| [google_project_iam_member.cicd_cloudscheduler_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                   | resource    |
| [google_project_iam_member.cicd_cloudtasks_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                       | resource    |
| [google_project_iam_member.cicd_firestore_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                        | resource    |
| [google_project_iam_member.cicd_iam_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                              | resource    |
| [google_project_iam_member.cicd_monitoring_editor](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                      | resource    |
| [google_project_iam_member.cicd_pubsub_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                           | resource    |
| [google_project_iam_member.cicd_sa_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                               | resource    |
| [google_project_iam_member.cicd_sa_token_creator](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                       | resource    |
| [google_project_iam_member.cicd_secretmanager_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                    | resource    |
| [google_project_iam_member.cicd_serviceusage_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                     | resource    |
| [google_project_iam_member.cicd_storage_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                          | resource    |
| [google_project_iam_member.cicd_wif_pool_admin](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                         | resource    |
| [google_project_iam_member.integrated_event_sa_additional_roles](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                        | resource    |
| [google_project_iam_member.runner_sa_firestore_user](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_member)                                    | resource    |
| [google_project_service.firestore](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_service)                                                         | resource    |
| [google_project_service.required_apis](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_service)                                                     | resource    |
| [google_pubsub_topic.data_processing_events](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic)                                                  | resource    |
| [google_pubsub_topic.error_notifications](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/pubsub_topic)                                                     | resource    |
| [google_secret_manager_secret.database_url](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/secret_manager_secret)                                          | resource    |
| [google_service_account.cicd_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account)                                                           | resource    |
| [google_service_account.integrated_event_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account)                                               | resource    |
| [google_service_account_iam_member.cicd_sa_can_impersonate_runtime_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account_iam_member)          | resource    |
| [google_service_account_iam_member.cicd_workload_identity_binding](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account_iam_member)              | resource    |
| [google_storage_bucket.staging_data](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket)                                                        | resource    |
| [google_storage_bucket_iam_member.staging_data_reader](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_iam_member)                           | resource    |
| [google_storage_bucket_iam_member.staging_data_writer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket_iam_member)                           | resource    |
| [google_project.current](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project)                                                                        | data source |
| [google_service_account.cicd_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account)                                                        | data source |
| [google_service_account.runner_sa](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/service_account)                                                      | data source |

## Inputs

| Name                                                                                          | Description                                    | Type     | Default                     | Required |
| --------------------------------------------------------------------------------------------- | ---------------------------------------------- | -------- | --------------------------- | :------: |
| <a name="input_bigquery_admin_email"></a> [bigquery_admin_email](#input_bigquery_admin_email) | Email of BigQuery administrator                | `string` | `"<EMAIL>"`    |    no    |
| <a name="input_bigquery_dataset"></a> [bigquery_dataset](#input_bigquery_dataset)             | BigQuery dataset name                          | `string` | `"event_test"`              |    no    |
| <a name="input_bigquery_location"></a> [bigquery_location](#input_bigquery_location)          | BigQuery location                              | `string` | `"asia-east1"`              |    no    |
| <a name="input_environment"></a> [environment](#input_environment)                            | Environment (dev, staging, prod)               | `string` | n/a                         |   yes    |
| <a name="input_firestore_location"></a> [firestore_location](#input_firestore_location)       | Firestore location                             | `string` | `"asia-east1"`              |    no    |
| <a name="input_github_repository"></a> [github_repository](#input_github_repository)          | GitHub repository for CI/CD (format: org/repo) | `string` | `"Tagtoo/integrated-event"` |    no    |
| <a name="input_monitoring_email"></a> [monitoring_email](#input_monitoring_email)             | Email for monitoring notifications             | `string` | `"<EMAIL>"`    |    no    |
| <a name="input_project_id"></a> [project_id](#input_project_id)                               | GCP Project ID                                 | `string` | `"tagtoo-tracking"`         |    no    |
| <a name="input_region"></a> [region](#input_region)                                           | GCP Region                                     | `string` | `"asia-east1"`              |    no    |
| <a name="input_table_expiration_ms"></a> [table_expiration_ms](#input_table_expiration_ms)    | Default table expiration in milliseconds       | `number` | `null`                      |    no    |

## Outputs

| Name                                                                                                                                | Description                                                    |
| ----------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------- |
| <a name="output_artifact_registry_repository_url"></a> [artifact_registry_repository_url](#output_artifact_registry_repository_url) | URL of the Artifact Registry repository for application images |
| <a name="output_bigquery_dataset_id"></a> [bigquery_dataset_id](#output_bigquery_dataset_id)                                        | BigQuery dataset ID                                            |
| <a name="output_bigquery_dataset_location"></a> [bigquery_dataset_location](#output_bigquery_dataset_location)                      | BigQuery dataset location                                      |
| <a name="output_cicd_service_account_email"></a> [cicd_service_account_email](#output_cicd_service_account_email)                   | CI/CD Service Account email (共用單一 SA)                      |
| <a name="output_data_processing_topic"></a> [data_processing_topic](#output_data_processing_topic)                                  | Pub/Sub topic for data processing events                       |
| <a name="output_environment"></a> [environment](#output_environment)                                                                | Environment                                                    |
| <a name="output_error_notifications_topic"></a> [error_notifications_topic](#output_error_notifications_topic)                      | Pub/Sub topic for error notifications                          |
| <a name="output_firestore_database_name"></a> [firestore_database_name](#output_firestore_database_name)                            | Firestore database name                                        |
| <a name="output_notification_channel_id"></a> [notification_channel_id](#output_notification_channel_id)                            | Monitoring notification channel ID                             |
| <a name="output_project_id"></a> [project_id](#output_project_id)                                                                   | GCP Project ID                                                 |
| <a name="output_region"></a> [region](#output_region)                                                                               | GCP Region                                                     |
| <a name="output_service_account_email"></a> [service_account_email](#output_service_account_email)                                  | Service account email for integrated event services            |
| <a name="output_service_account_name"></a> [service_account_name](#output_service_account_name)                                     | Service account name for integrated event services             |
| <a name="output_staging_bucket_name"></a> [staging_bucket_name](#output_staging_bucket_name)                                        | Staging data bucket name                                       |
| <a name="output_workload_identity_provider"></a> [workload_identity_provider](#output_workload_identity_provider)                   | Workload Identity Provider for CI/CD (使用共用 Pool)           |

<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
