# Workload Identity Federation for CI/CD
# 用於安全地從 GitHub Actions 或其他 CI/CD 系統部署

# ====== 共用的 Workload Identity Pool ======
# 管理組織層級的共用 WIF Pool，供所有專案使用
# 只在 prod workspace 中管理，避免重複建立

data "google_project" "current" {}

# ====== 共用 GitHub Actions Pool ======
resource "google_iam_workload_identity_pool" "shared_github_pool" {
  # 只在 prod workspace 建立和管理共用 Pool
  count                     = var.environment == "prod" ? 1 : 0
  workload_identity_pool_id = "github-actions"
  display_name              = "GitHub Actions Pool"
  description               = "用於 GitHub Actions 的 Workload Identity Pool"
  project                   = var.project_id
}

# 非 prod workspace 讀取現有的 Pool（如果需要的話）
# 注意：由於 Google provider 不支援 WIF Pool 的 data source，
# 我們直接使用 locals 來引用

locals {
  # 使用條件邏輯：如果是 prod 就用 resource，否則用硬編碼路徑
  shared_wif_pool_name     = var.environment == "prod" ? google_iam_workload_identity_pool.shared_github_pool[0].name : "projects/${data.google_project.current.number}/locations/global/workloadIdentityPools/github-actions"
  shared_wif_provider_name = "${local.shared_wif_pool_name}/providers/github"
}

# ====== GitHub Actions Provider ======
resource "google_iam_workload_identity_pool_provider" "shared_github_provider" {
  # 只在 prod workspace 建立和管理
  count                              = var.environment == "prod" ? 1 : 0
  workload_identity_pool_id          = google_iam_workload_identity_pool.shared_github_pool[0].workload_identity_pool_id
  workload_identity_pool_provider_id = "github"
  display_name                       = "GitHub Actions Provider"
  description                        = "用於 GitHub Actions 的 OIDC Provider"
  project                            = var.project_id

  # 屬性對應
  attribute_mapping = {
    "google.subject"             = "assertion.sub"
    "attribute.actor"            = "assertion.actor"
    "attribute.repository"       = "assertion.repository"
    "attribute.repository_owner" = "assertion.repository_owner"
  }

  # OIDC 設定
  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }

  # 存取條件：只允許 Tagtoo 組織的 repositories
  attribute_condition = "assertion.repository_owner == 'Tagtoo'"
}

# ====== CI/CD Service Account ======
resource "google_service_account" "cicd_sa" {
  # 只在 prod workspace 建立；dev 等其他 workspace會讀取既有 SA
  count        = var.environment == "prod" ? 1 : 0
  account_id   = "integrated-event-cicd"
  display_name = "Integrated Event CI/CD Service Account"
  description  = "Service account for CI/CD deployments"
}

###############################################################
#  共用 CI/CD Service Account 讀取與共用變數
###############################################################

# 讓非 prod workspace 讀取已存在的 SA
data "google_service_account" "cicd_sa" {
  count      = var.environment == "prod" ? 0 : 1
  account_id = "integrated-event-cicd"
  project    = var.project_id
}

locals {
  cicd_sa_name  = var.environment == "prod" ? google_service_account.cicd_sa[0].name : data.google_service_account.cicd_sa[0].name
  cicd_sa_email = var.environment == "prod" ? google_service_account.cicd_sa[0].email : data.google_service_account.cicd_sa[0].email
}

# ====== Bind GitHub repository to Service Account ======
# 使用現有的共用 Pool，綁定特定的 repository
resource "google_service_account_iam_member" "cicd_workload_identity_binding" {
  count              = 1 # 單一 SA 啟用
  service_account_id = local.cicd_sa_name
  role               = "roles/iam.workloadIdentityUser"
  member             = "principalSet://iam.googleapis.com/${local.shared_wif_pool_name}/attribute.repository/${var.github_repository}"
}

# ====== CI/CD Permissions ======
# Cloud Function 部署權限
resource "google_project_iam_member" "cicd_cloudfunctions_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/cloudfunctions.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# Cloud Storage 權限 (上傳原始碼)
resource "google_project_iam_member" "cicd_storage_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# Cloud Build 權限
resource "google_project_iam_member" "cicd_cloudbuild_editor" {
  count   = 1
  project = var.project_id
  role    = "roles/cloudbuild.builds.editor"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# Pub/Sub 權限
resource "google_project_iam_member" "cicd_pubsub_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/pubsub.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# Cloud Scheduler 權限
resource "google_project_iam_member" "cicd_cloudscheduler_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/cloudscheduler.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# Secret Manager 權限 (管理部署 secrets)
resource "google_project_iam_member" "cicd_secretmanager_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/secretmanager.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# BigQuery 權限 (table schema 管理)
resource "google_project_iam_member" "cicd_bigquery_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/bigquery.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# ====== Artifact Registry 權限 ======
resource "google_project_iam_member" "cicd_artifactregistry_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/artifactregistry.repoAdmin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# ====== Cloud Tasks 權限 ======
resource "google_project_iam_member" "cicd_cloudtasks_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/cloudtasks.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# ====== Cloud Run 權限 ======
resource "google_project_iam_member" "cicd_cloudrun_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/run.admin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# ====== 其他缺少但 Plan 需要的角色 ======

resource "google_project_iam_member" "cicd_serviceusage_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/serviceusage.serviceUsageAdmin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

resource "google_project_iam_member" "cicd_sa_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/iam.serviceAccountAdmin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# IAM 權限管理 - 需要能夠設定專案層級的 IAM 政策
resource "google_project_iam_member" "cicd_iam_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/resourcemanager.projectIamAdmin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

resource "google_project_iam_member" "cicd_firestore_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/datastore.owner"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

resource "google_project_iam_member" "cicd_monitoring_editor" {
  count   = 1
  project = var.project_id
  role    = "roles/monitoring.editor"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

resource "google_project_iam_member" "cicd_wif_pool_admin" {
  count   = 1
  project = var.project_id
  role    = "roles/iam.workloadIdentityPoolAdmin"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

resource "google_project_iam_member" "cicd_sa_token_creator" {
  project = var.project_id
  role    = "roles/iam.serviceAccountTokenCreator"
  member  = "serviceAccount:${local.cicd_sa_email}"
}

# ====== Service Account Impersonation Permissions ======
# 讓 integrated-event-cicd SA 有權限假冒 runtime service accounts
# 這是為了讓 Terraform 能在 Cloud Run 服務中指定 service_account
resource "google_service_account_iam_member" "cicd_sa_can_impersonate_runtime_sa" {
  service_account_id = google_service_account.integrated_event_sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.cicd_sa_email}"

  depends_on = [google_service_account.integrated_event_sa]
}

# ====== 輸出重要資訊供 CI/CD 使用 ======
output "workload_identity_provider" {
  description = "Workload Identity Provider for CI/CD (使用共用 Pool)"
  value       = local.shared_wif_provider_name
}

output "cicd_service_account_email" {
  description = "CI/CD Service Account email (共用單一 SA)"
  value       = local.cicd_sa_email
}
