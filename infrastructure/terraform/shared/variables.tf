# Shared Infrastructure Variables

variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "tagtoo-tracking"
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "asia-east1"
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be dev, staging, or prod."
  }
}

variable "bigquery_dataset" {
  description = "BigQuery dataset name"
  type        = string
  default     = "event_test"
}

variable "bigquery_location" {
  description = "BigQuery location"
  type        = string
  default     = "asia-east1"
}

variable "table_expiration_ms" {
  description = "Default table expiration in milliseconds"
  type        = number
  default     = null # No expiration by default
}

variable "firestore_location" {
  description = "Firestore location"
  type        = string
  default     = "asia-east1"
}

variable "bigquery_admin_email" {
  description = "Email of BigQuery administrator"
  type        = string
  default     = "<EMAIL>"
}

variable "monitoring_email" {
  description = "Email for monitoring notifications"
  type        = string
  default     = "<EMAIL>"
}

variable "github_repository" {
  description = "GitHub repository for CI/CD (format: org/repo)"
  type        = string
  default     = "Tagtoo/integrated-event"
}
