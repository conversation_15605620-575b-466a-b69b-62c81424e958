#!/bin/bash
set -e

# Shared Infrastructure Deployment Script
# 部署共用基礎設施

ENVIRONMENT=${1:-prod}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🚀 部署 Shared Infrastructure (環境: $ENVIRONMENT)"
echo "================================================"

# 切換到 terraform 目錄
cd "$SCRIPT_DIR"

# 檢查必要工具
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform 未安裝"
    exit 1
fi

if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI 未安裝"
    exit 1
fi

# 檢查認證
echo "🔍 檢查 GCP 認證..."
if ! gcloud auth application-default print-access-token > /dev/null 2>&1; then
    echo "❌ GCP 認證失敗，請執行: gcloud auth application-default login"
    exit 1
fi
echo "✅ GCP 認證正常"

# 初始化 Terraform
echo "🔧 初始化 Terraform..."
terraform init

# 選擇或創建 workspace
echo "🔄 設定 Terraform workspace: $ENVIRONMENT"
terraform workspace select "$ENVIRONMENT" 2>/dev/null || terraform workspace new "$ENVIRONMENT"

# 執行計畫
echo "📋 執行 Terraform plan..."
terraform plan -var="environment=$ENVIRONMENT" -out="tfplan-$ENVIRONMENT"

# 確認部署
if [ "$ENVIRONMENT" = "prod" ]; then
    echo ""
    echo "⚠️  即將部署到生產環境！"
    echo "請仔細檢查上述計畫，確認無誤後按 Enter 繼續，或 Ctrl+C 取消："
    read -r
fi

# 執行部署
echo "🚀 執行 Terraform apply..."
terraform apply "tfplan-$ENVIRONMENT"

echo ""
echo "✅ Shared Infrastructure 部署完成！"
echo ""
echo "📊 部署摘要:"
echo "  環境: $ENVIRONMENT"
echo "  專案: $(terraform output -raw project_id 2>/dev/null || echo 'N/A')"
echo "  地區: $(terraform output -raw region 2>/dev/null || echo 'N/A')"
echo ""
echo "🔗 接下來可以部署應用服務:"
echo "  cd ../../apps/legacy-event-sync"
echo "  make deploy-$ENVIRONMENT"
