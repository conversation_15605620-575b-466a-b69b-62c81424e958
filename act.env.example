# Act 測試環境變數範本
# 複製此檔案為 act.env 並修改為你的測試環境設定

# GitHub 相關 (測試用)
GITHUB_TOKEN=your-test-token-here
GITHUB_REPOSITORY=tagtoo/integrated-event
GITHUB_REF=refs/heads/main
GITHUB_SHA=test-sha

# GCP 相關 (使用你的測試專案)
GOOGLE_CLOUD_PROJECT=your-test-project
GCP_WORKLOAD_IDENTITY_PROVIDER=your-workload-identity-provider
GCP_SERVICE_ACCOUNT=<EMAIL>
CLOUD_STORAGE_BUCKET=your-test-bucket

# 服務 Secrets (測試用假值)
SECRET_KEY_LEGACY_EVENT_SYNC=test-secret-key
API_KEY_REURL=test-api-key
WEBHOOK_SECRET_SHOPIFY=test-webhook-secret
