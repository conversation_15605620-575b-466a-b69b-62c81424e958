# Integrated Event Platform — DEVELOPMENT.md

## 開發工具鏈設計 (2025-07-09 更新)

### Terraform 與 Pre-commit 工具關係

**核心觀念**: Terraform 和 Pre-commit 是**兩個獨立的工具**，各自負責不同的職責：

#### 🔧 Terraform (基礎設施管理核心)

```bash
# 主要工具鏈 - 推薦優先使用
terraform fmt -recursive          # 格式化所有 .tf 檔案
terraform validate                # 語法和配置驗證
terraform plan                   # 生成執行計劃
terraform apply                  # 部署基礎設施

# 團隊標準化包裝
make tf-fmt                      # 格式化
make tf-validate                 # 驗證
make tf-plan-dev                 # 開發環境計劃
make tf-apply-dev                # 開發環境部署
```

#### ⚡ Pre-commit (自動化檢查框架)

- **本質**: Git hooks 自動化框架
- **目的**: 在 commit 前自動執行 terraform 等工具
- **關係**: 呼叫 terraform 命令，而**不是替代**它們

**monorepo 責任分離設計**:

- **根目錄**: 共用基礎設施檢查 + 全域一致性保證
- **各服務**: 服務特定的詳細檢查和驗證

#### 開發者可選擇的工作流

**選項 1: 純手動** (完全控制)

```bash
cd infrastructure/terraform/shared
terraform fmt && terraform validate && terraform plan
git add . && git commit -m "feat(infra): update shared resources"
```

**選項 2: Makefile** (團隊標準)

```bash
make tf-fmt && make tf-validate
git add . && git commit -m "feat(infra): update shared resources"
```

**選項 3: Pre-commit 自動化** (新手友好)

```bash
git add . && git commit -m "feat(infra): update shared resources"
# 自動執行 terraform fmt, validate 等檢查
```

**CI/CD 永遠使用原生命令** 確保一致性:

```bash
terraform fmt -check -recursive
terraform validate
terraform plan
```

# Integrated Event Platform — DEVELOPMENT.md

## Context

2025-07-04：GitHub Actions 在執行 `terraform apply` 時因 `artifactregistry.repositories.create` 權限不足（403 IAM_PERMISSION_DENIED）而失敗。

2025-07-04（後續）：整合測試需要對 Firestore 進行 CRUD，`integrated-event-runner` Service Account 權限不足，導致測試 403。

## Requirements

1. 為 CI/CD Service Account `integrated-event-cicd` 新增 `roles/artifactregistry.repoAdmin` 角色，允許建立 Artifact Registry Repository。
2. 為 Runtime Service Account `integrated-event-{environment}`（Cloud Run Runtime）新增 **Firestore 寫入權限** `roles/datastore.user`，確保整合測試期間能讀寫 `legacy_sync_metadata` collection。
3. 為 Testing/Runner Service Account `integrated-event-runner`（GitHub Actions 測試使用）同樣新增 **Firestore 寫入權限** `roles/datastore.user`。

## Tech Stack

- Terraform ≥ 1.5
- Google Cloud Artifact Registry
- Google Cloud Firestore
- Workload Identity Federation (GitHub Actions → GCP)

## Milestones

1. 更新 `infrastructure/terraform/shared/workload-identity.tf`，新增 Artifact Registry IAM 角色。
2. 更新 `infrastructure/terraform/shared/main.tf`，在 `integrated_event_sa_additional_roles` 加入 `roles/datastore.user`（Runtime SA）。
3. **新增 `runner-sa.tf`**：
   - 使用 `data "google_service_account" "runner_sa"` 導入既有 `integrated-event-runner` SA。
   - 為其建立 `google_project_iam_member.runner_sa_firestore_user`，角色 `roles/datastore.user`。
4. 執行 `terraform plan -out=tfplan-prod`，確認僅有 IAM 角色變更。
5. 執行 `terraform apply -auto-approve -input=false tfplan-prod`，成功建立/更新所需 IAM。
6. 重新執行 GitHub Actions Workflow（`apps-ci-cd.yml`），確認整合測試通過。

## 2025-07-04 — GitHub Actions detect-changes 步驟修正

### Context

GitHub Actions `detect-changes` 步驟在同時偵測多個服務時，將多行 `services` 直接寫入 `$GITHUB_OUTPUT`，導致格式錯誤（`Invalid format 'reurl'`）。

### Requirements

- 將 `SERVICES` 轉換為單行（空格分隔）後再寫入 `$GITHUB_OUTPUT`，避免解析失敗。

### Tech Stack

- GitHub Actions YAML

### Milestones

1. 修改 `.github/workflows/apps-ci-cd.yml` 中 `Detect changed services` script：
   - 新增 `SERVICES_SINGLE=$(echo "$SERVICES" | tr '\n' ' ' | xargs)`
   - 將 `echo "services=$SERVICES"` 改為 `echo "services=$SERVICES_SINGLE"`
2. （可選）使用 `act` 本地執行 workflow `detect-changes` job，驗證 matrix 正確包含多個服務。
3. 提交 Git commit，遵循 Conventional Commits 格式。

## 🚀 CI/CD 工作流程

### 📋 工作流程職責分工

- **`infra-shared.yml`**: 負責 shared infrastructure 的部署和管理
- **`apps-ci-cd.yml`**: 負責各應用服務的測試和部署
  - 包含智慧型 `ensure-shared-infrastructure` 步驟，避免衝突

### ⚡ 智慧型衝突避免機制（純讀取模式）

新的 CI/CD 設計會自動避免 Terraform state lock 衝突，採用**純讀取模式**：

1. **檢測變更類型**：

   - 如果同時修改 `infrastructure/terraform/shared/` 和 `apps/`
   - `apps-ci-cd.yml` 會檢測到 shared infrastructure 變更
   - 自動跳過 `ensure-shared-infrastructure` 步驟，讓 `infra-shared.yml` 處理

2. **純讀取驗證**（關鍵改進）：

   - `ensure-shared-infrastructure` **完全不執行** `terraform apply` 操作
   - 只執行 `terraform show` 和 `terraform output` 讀取已部署資源
   - 如果 shared infrastructure 不存在，直接報錯並提供明確指引
   - **零衝突風險**：不會與 `infra-shared.yml` 產生 Terraform state lock 競爭

3. **執行順序**：
   - `infra-shared.yml` 先執行（shared infrastructure 變更）
   - `apps-ci-cd.yml` 後執行，但只讀取 shared outputs

### 🔄 部署情境處理

#### 情境 1: 只修改應用服務

```bash
# 修改 apps/legacy-event-sync/
git commit -m "feat: 新功能"
# ✅ apps-ci-cd.yml 執行
# ✅ ensure-shared-infrastructure 純讀取驗證（不執行 apply）
# ✅ 部署應用服務
```

#### 情境 2: 只修改 Shared Infrastructure

```bash
# 修改 infrastructure/terraform/shared/
git commit -m "feat: 新增資源"
# ✅ infra-shared.yml 執行
# ✅ 部署 shared infrastructure
```

#### 情境 3: 同時修改兩者（智慧型處理，零衝突）

```bash
# 同時修改 infrastructure/terraform/shared/ 和 apps/
git commit -m "feat: 新功能和基礎設施"
# ✅ infra-shared.yml 執行（處理 shared infrastructure）
# ✅ apps-ci-cd.yml 執行，但跳過 shared infrastructure 驗證
# ✅ 兩個 workflow 並行，零衝突風險（純讀取模式）
```

### 🔐 權限說明

- **CI 測試階段**: 使用 `integrated-event-runner` Service Account
- **CD 部署階段**: 使用 `integrated-event-cicd` Service Account
- **Shared Infrastructure**: 統一使用 `integrated-event-cicd` Service Account
- **衝突避免**: 智慧型檢測，自動協調執行順序
