# Integrated Event Platform — DEVELOPMENT.md

## 開發工具鏈設計 (2025-07-09 更新)

### Terraform 與 Pre-commit 工具關係

**核心觀念**: Terraform 和 Pre-commit 是**兩個獨立的工具**，各自負責不同的職責：

#### 🔧 Terraform (基礎設施管理核心)

```bash
# 主要工具鏈 - 推薦優先使用
terraform fmt -recursive          # 格式化所有 .tf 檔案
terraform validate                # 語法和配置驗證
terraform plan                   # 生成執行計劃
terraform apply                  # 部署基礎設施

# 團隊標準化包裝
make tf-fmt                      # 格式化
make tf-validate                 # 驗證
make tf-plan-dev                 # 開發環境計劃
make tf-apply-dev                # 開發環境部署
```

#### ⚡ Pre-commit (自動化檢查框架)

- **本質**: Git hooks 自動化框架
- **目的**: 在 commit 前自動執行 terraform 等工具
- **關係**: 呼叫 terraform 命令，而**不是替代**它們

**monorepo 責任分離設計**:

- **根目錄**: 共用基礎設施檢查 + 全域一致性保證
- **各服務**: 服務特定的詳細檢查和驗證

#### 開發者可選擇的工作流

**選項 1: 純手動** (完全控制)

```bash
cd infrastructure/terraform/shared
terraform fmt && terraform validate && terraform plan
git add . && git commit -m "feat(infra): update shared resources"
```

**選項 2: Makefile** (團隊標準)

```bash
make tf-fmt && make tf-validate
git add . && git commit -m "feat(infra): update shared resources"
```

**選項 3: Pre-commit 自動化** (新手友好)

```bash
git add . && git commit -m "feat(infra): update shared resources"
# 自動執行 terraform fmt, validate 等檢查
```

**CI/CD 永遠使用原生命令** 確保一致性:

```bash
terraform fmt -check -recursive
terraform validate
terraform plan
```

# Integrated Event Platform — DEVELOPMENT.md

## Context

2025-07-04：GitHub Actions 在執行 `terraform apply` 時因 `artifactregistry.repositories.create` 權限不足（403 IAM_PERMISSION_DENIED）而失敗。

2025-07-04（後續）：整合測試需要對 Firestore 進行 CRUD，`integrated-event-runner` Service Account 權限不足，導致測試 403。

## Requirements

1. 為 CI/CD Service Account `integrated-event-cicd` 新增 `roles/artifactregistry.repoAdmin` 角色，允許建立 Artifact Registry Repository。
2. 為 Runtime Service Account `integrated-event-{environment}`（Cloud Run Runtime）新增 **Firestore 寫入權限** `roles/datastore.user`，確保整合測試期間能讀寫 `legacy_sync_metadata` collection。
3. 為 Testing/Runner Service Account `integrated-event-runner`（GitHub Actions 測試使用）同樣新增 **Firestore 寫入權限** `roles/datastore.user`。

## Tech Stack

- Terraform ≥ 1.5
- Google Cloud Artifact Registry
- Google Cloud Firestore
- Workload Identity Federation (GitHub Actions → GCP)

## Milestones

1. 更新 `infrastructure/terraform/shared/workload-identity.tf`，新增 Artifact Registry IAM 角色。
2. 更新 `infrastructure/terraform/shared/main.tf`，在 `integrated_event_sa_additional_roles` 加入 `roles/datastore.user`（Runtime SA）。
3. **新增 `runner-sa.tf`**：
   - 使用 `data "google_service_account" "runner_sa"` 導入既有 `integrated-event-runner` SA。
   - 為其建立 `google_project_iam_member.runner_sa_firestore_user`，角色 `roles/datastore.user`。
4. 執行 `terraform plan -out=tfplan-prod`，確認僅有 IAM 角色變更。
5. 執行 `terraform apply -auto-approve -input=false tfplan-prod`，成功建立/更新所需 IAM。
6. 重新執行 GitHub Actions Workflow（`apps-ci-cd.yml`），確認整合測試通過。

## 2025-07-04 — GitHub Actions detect-changes 步驟修正

### Context

GitHub Actions `detect-changes` 步驟在同時偵測多個服務時，將多行 `services` 直接寫入 `$GITHUB_OUTPUT`，導致格式錯誤（`Invalid format 'reurl'`）。

### Requirements

- 將 `SERVICES` 轉換為單行（空格分隔）後再寫入 `$GITHUB_OUTPUT`，避免解析失敗。

### Tech Stack

- GitHub Actions YAML

### Milestones

1. 修改 `.github/workflows/apps-ci-cd.yml` 中 `Detect changed services` script：
   - 新增 `SERVICES_SINGLE=$(echo "$SERVICES" | tr '\n' ' ' | xargs)`
   - 將 `echo "services=$SERVICES"` 改為 `echo "services=$SERVICES_SINGLE"`
2. （可選）使用 `act` 本地執行 workflow `detect-changes` job，驗證 matrix 正確包含多個服務。
3. 提交 Git commit，遵循 Conventional Commits 格式。

## 🚀 CI/CD 工作流程

### 📋 工作流程職責分工

- **`infra-shared.yml`**: 負責 shared infrastructure 的部署和管理
- **`apps-ci-cd.yml`**: 負責各應用服務的測試和部署
  - 包含智慧型 `ensure-shared-infrastructure` 步驟，避免衝突

### ⚡ 智慧型衝突避免機制（純讀取模式）

新的 CI/CD 設計會自動避免 Terraform state lock 衝突，採用**純讀取模式**：

1. **檢測變更類型**：

   - 如果同時修改 `infrastructure/terraform/shared/` 和 `apps/`
   - `apps-ci-cd.yml` 會檢測到 shared infrastructure 變更
   - 自動跳過 `ensure-shared-infrastructure` 步驟，讓 `infra-shared.yml` 處理

2. **純讀取驗證**（關鍵改進）：

   - `ensure-shared-infrastructure` **完全不執行** `terraform apply` 操作
   - 只執行 `terraform show` 和 `terraform output` 讀取已部署資源
   - 如果 shared infrastructure 不存在，直接報錯並提供明確指引
   - **零衝突風險**：不會與 `infra-shared.yml` 產生 Terraform state lock 競爭

3. **執行順序**：
   - `infra-shared.yml` 先執行（shared infrastructure 變更）
   - `apps-ci-cd.yml` 後執行，但只讀取 shared outputs

### 🔄 部署情境處理

#### 情境 1: 只修改應用服務

```bash
# 修改 apps/legacy-event-sync/
git commit -m "feat: 新功能"
# ✅ apps-ci-cd.yml 執行
# ✅ ensure-shared-infrastructure 純讀取驗證（不執行 apply）
# ✅ 部署應用服務
```

#### 情境 2: 只修改 Shared Infrastructure

```bash
# 修改 infrastructure/terraform/shared/
git commit -m "feat: 新增資源"
# ✅ infra-shared.yml 執行
# ✅ 部署 shared infrastructure
```

#### 情境 3: 同時修改兩者（智慧型處理，零衝突）

```bash
# 同時修改 infrastructure/terraform/shared/ 和 apps/
git commit -m "feat: 新功能和基礎設施"
# ✅ infra-shared.yml 執行（處理 shared infrastructure）
# ✅ apps-ci-cd.yml 執行，但跳過 shared infrastructure 驗證
# ✅ 兩個 workflow 並行，零衝突風險（純讀取模式）
```

### 🔐 權限說明

- **CI 測試階段**: 使用 `integrated-event-runner` Service Account
- **CD 部署階段**: 使用 `integrated-event-cicd` Service Account
- **Shared Infrastructure**: 統一使用 `integrated-event-cicd` Service Account
- **衝突避免**: 智慧型檢測，自動協調執行順序

## 📦 版本管理系統

### 🎯 **版本策略概覽**

專案採用**混合版本管理方案**，結合 **SemVer 標準**和 **Git Tag 觸發機制**：

- **開發階段**: 使用 `github.sha` 進行持續部署
- **正式發版**: 使用 **Git Tags** 觸發自動化發版流程
- **雙軌並行**: 同時支援 SHA 和 SemVer，向後相容

### 🚀 **快速發版指南**

```bash
# 1. 進入服務目錄
cd apps/legacy-event-sync

# 2. 執行發版腳本
../../scripts/release.sh 1.2.3

# 3. 檢查並推送 commit（CI 會自動建立 Tag）
git push origin $(git rev-parse --abbrev-ref HEAD)

# （可選）若想在本地同步建立 Tag：
# ../../scripts/release.sh 1.2.3 --with-tag
# git push origin legacy-event-sync-1.2.3
```

**發版腳本會自動**：

- ✅ 版本號驗證（SemVer 格式、遞增檢查）
- ✅ 執行測試和程式碼檢查
- ✅ 更新 `pyproject.toml` 版本號
- ✅ 建立 Git commit（預設不建立本地 Tag）
- ✅ （可選）若加上 --with-tag 旗標，會同步建立本地 Tag

**推送 tag 後會觸發**：

- 🏗️ Docker 映像建置 (`gcr.io/tagtoo-tracking/service:1.2.3`)
- 🚀 自動部署到生產環境
- 📦 建立 GitHub Release
- 📝 自動生成 CHANGELOG

### ⚙️ **版本管理架構**

#### **目錄結構**

```
integrated-event/
├── scripts/
│   ├── release.sh           # 統一發版入口
│   ├── release-core.sh      # 核心發版邏輯
│   └── rollback.sh          # 生產環境回滾
├── apps/[service]/
│   ├── release-config.sh    # 服務專屬配置（可選）
│   └── pyproject.toml       # 版本號來源
└── docs/VERSION_MANAGEMENT.md  # 完整文檔
```

#### **工作流程分工**

- **`release.yml`**: Git tag 觸發的正式發版流程
- **`apps-ci-cd.yml`**: 開發階段的 SHA 部署流程
- **配置檔案**: 服務專屬的發版客製化

### 🔧 **服務專屬配置**

每個服務可透過 `release-config.sh` 自訂發版行為：

```bash
# apps/legacy-event-sync/release-config.sh
SERVICE_NAME="legacy-event-sync"
REQUIRE_TESTS=true
CUSTOM_PRE_RELEASE_VALIDATIONS=(
    "make test"
    "make lint"
    "terraform fmt -check terraform/"
)
```

### 📋 **版本號規範**

| 異動類型                  | 版本遞增 | 範例          |
| ------------------------- | -------- | ------------- |
| `BREAKING CHANGE:`        | MAJOR    | 1.2.3 → 2.0.0 |
| `feat:`                   | MINOR    | 1.2.3 → 1.3.0 |
| `fix:`, `docs:`, `chore:` | PATCH    | 1.2.3 → 1.2.4 |

### 🔄 **回滾機制**

```bash
# 緊急回滾到指定版本
cd apps/legacy-event-sync
../../scripts/rollback.sh 1.1.9
```

回滾腳本會：

- ✅ 驗證目標版本存在
- ✅ 更新 Terraform `deployment_version`
- ✅ 執行 `terraform apply`
- ✅ 驗證回滾結果

### 🆕 **新服務版本管理設定**

新服務可快速啟用版本管理：

```bash
# 複製配置檔案模板
cp apps/_template/common/release-config.sh apps/new-service/
# 修改服務名稱和驗證規則
vim apps/new-service/release-config.sh
```

### 📚 **詳細文檔**

完整的版本管理說明請參考：[docs/VERSION_MANAGEMENT.md](docs/VERSION_MANAGEMENT.md)

## 2025-07-10 — 版本管理機制調整：檔案為主 + CI 自動產生 Tag

> 本日會議決議：對於當前 **純 Python 服務為主** 的 mono-repo，改採「版本檔案為單一事實來源，CI 負責推送 Git Tag」的折衷方案，簡化開發操作，同時保留快照 / 回滾能力。

### 📝 Requirements

1. **開發者零感知**：僅需執行 `release.sh patch|minor|major` 或手動修改 `pyproject.toml` 後 push。
2. **CI 偵測遞增**：若 `apps/<service>/pyproject.toml` 之 `version` 大於上一個 commit，CI 建立 `service-name-<version>` tag 並 push。
3. **下游流程不變**：既有 `release.yml` 仍以 Tag 觸發建置 / 部署 / GitHub Release。
4. **跨語言擴充**：未來可加入 `package.json`、`Cargo.toml` 等 parser；Tag 格式保持一致。

### 🛠 Tech Stack

- GitHub Actions
  - `auto-tag.yml` (新增)：`on: push`，使用 `actions/github-script` 解析 diff、建立 Tag。
- Shell + Python (`packaging.version`) 判斷 SemVer 遞增。
- 現有 `scripts/release.sh` / `release-core.sh`：改為**不**產生本地 Tag，只 commit。

### 📆 Milestones

| ID                       | 描述                                                         | 依賴              |
| ------------------------ | ------------------------------------------------------------ | ----------------- |
| auto-tag-workflow        | 建立 **auto-tag.yml** (detect & push tag)                    | 無                |
| release-sh-slim          | 將本地 Tag 動作移除，僅更新版本檔並 commit                   | auto-tag-workflow |
| docs-versioning-auto-tag | 更新 `DEVELOPMENT.md`、`VERSION_MANAGEMENT.md` 與模板 README | auto-tag-workflow |
| ci-validation            | 使用 `act` 驗證：改版號→CI 建 Tag→release.yml 部署           | auto-tag-workflow |

> 備註：原有 `service-<version>` Tag 觸發規則已保留，可向後相容；新流程僅是把「誰來 push tag」從開發者改為 CI。

## 2025-07-10 — Auto Tag Job Summary 改善

### Requirements

1. 當 Auto Tag 工作流程建立 **任何** tag 時，必須在 Job Summary 產生一張表格，包含：`Tag`、`Commit`、`Author`、`Triggered By`、`Date`（UTC）。
2. 若本次 Push 未建立任何 tag，Summary 仍需輸出 `ℹ️ 無任何 tag 被建立`。
3. 流程邏輯（版本比對、`git push --tags` 等）維持不變。

### Tech stack

- GitHub Actions YAML：`.github/workflows/auto-tag.yml`
- Bash 內嵌腳本 + Git CLI (`git log`, `git show`) 取得作者與時間
- GitHub Step Summary (`$GITHUB_STEP_SUMMARY`)

### Milestones

| ID               | 描述                                                                                     | 依賴             |
| ---------------- | ---------------------------------------------------------------------------------------- | ---------------- |
| tag-info-collect | 於建立 tag 時蒐集 `SHA短碼`、`Author`、`Triggered By`、`Date`，累加至變數 `CREATED_TAGS` | 無               |
| summary-output   | 建立或無 tag 皆寫入 Job Summary，使用符合 Prettier 的 Markdown 表格                      | tag-info-collect |
| local-validation | 使用 `act push -j auto-tag` 驗證 Summary 輸出                                            | summary-output   |
| commit-pr        | 依 Conventional Commits 提交 `[chore](ci): Auto Tag job summary 改善` PR                 | local-validation |

### 🔄 Draft Release 與 SHA Tag 決策 (2025-07-11)

1. **Draft Release 自動化**

   - 採用「版本摘要表格 + 自動產生 Release Notes」策略。
   - Auto-Tag 工作流程在推送 Git Tag 後，透過 `gh release create <tag>`：
     - `--draft` + `--title "<service> <version>"`
     - `--notes` 內嵌版本摘要表格，並保留 `🚧 Auto-generated draft, please add notes.` 提示。
   - 待補充變更內容時，只需直接編輯 Draft Release。不強制人工維護。

2. **Docker SHA Tag 統一 12 位**
   - dev（apps-ci-cd.yml）與 prod（release.yml）皆將 `SHORT_SHA=${GITHUB_SHA::12}` 作為映像第三 tag。
   - 三種 tag：`:<version>`、`:latest`、`:<short_sha12>`。
   - Terraform 仍以 `<version>` 傳遞 `deployment_version`，不受影響。

## 2025-07-11 — Auto Tag 安全性修補

### Context

Auto Tag workflow 在比較版本號時，直接將 shell 變數插入 Python 字串。若版本字串含有特殊字元（如單引號、分號），可能導致 Python 早期截斷並允許任意程式碼執行，屬於 **命令注入** 弱點。

### Fix

1. 於迴圈中先 `export NEW_VER OLD_VER`，再以單引號字串呼叫 `python -c`，於腳本內透過 `os.environ` 讀取版本字串。
2. 完全移除 shell 變數插值，避免任何字串逃逸攻擊面。
3. 新增 `git config user.name/email`，以 `github-actions[bot]` 身分建立 Annotated Tag，避免 CI 缺乏 committer identity 失敗。

```bash
export NEW_VER OLD_VER
python -c 'from packaging.version import parse as _p; import os, sys; sys.exit(0 if _p(os.environ["NEW_VER"]) > _p(os.environ["OLD_VER"]) else 1)'
```

### Files Affected

- `.github/workflows/auto-tag.yml`

### Impact

- 行為維持不變：仍可偵測版本遞增並自動推送 Git Tag。
- 消除由惡意版本字串引發的命令注入風險。
