# 新服務配置檔案範例
# 複製此檔案並重新命名，然後填入實際值

service:
  # 服務顯示名稱 (會自動轉換為不同格式)
  name: "Shopify Webhook"

  # 服務描述
  description: "整合 Shopify webhook 資料到 integrated_event 平台"

  # 部署類型: cloud-run, cloud-function, bigquery-only
  deployment_type: "cloud-run"

  # 作者資訊 (選填)
  author: "Tagtoo Data Team"
  email: "<EMAIL>"

# 技術配置 (選填，會使用預設值)
technical:
  # Python 版本
  python_version: "3.11"

  # 主要依賴 (會自動加入)
  dependencies:
    - "flask>=2.3.0"
    - "google-cloud-firestore>=2.11.0"
    - "google-cloud-bigquery>=3.11.0"

  # Cloud Run 配置
  cloud_run:
    cpu: "1000m"
    memory: "512Mi"
    min_instances: 0
    max_instances: 10

# 資料配置 (選填)
data:
  # Firestore 集合名稱模式
  firestore_collection: "{service_name}-{environment}"

  # BigQuery 資料集
  bigquery_dataset: "integrated_event"

  # 批次處理大小
  batch_size: 1000

  # 同步間隔 (分鐘)
  sync_interval: 5

# 監控配置 (選填)
monitoring:
  # 健康檢查路徑
  health_path: "/health"

  # 監控指標
  metrics:
    - "request_latency"
    - "error_rate"
    - "sync_success_rate"

# 安全配置 (選填)
security:
  # 是否需要 API 金鑰驗證
  require_api_key: true

  # 是否需要 webhook 簽名驗證
  require_webhook_signature: true

# 部署配置 (選填)
deployment:
  # 支援的環境
  environments:
    - "dev"
    - "staging"
    - "prod"

  # 自動部署分支
  auto_deploy:
    dev: "develop"
    prod: "main"
