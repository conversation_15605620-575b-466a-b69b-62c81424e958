# 📚 Apps CI/CD Pipeline 使用指南

這是 `integrated-event` 專案的統一 CI/CD pipeline，專為 monorepo 架構設計，能夠智慧偵測 `/apps` 目錄下的服務異動並自動執行測試和部署。

## 🎯 功能概述

- **🔍 智慧偵測**：自動偵測哪些服務有異動，只處理需要的服務
- **🧪 自動測試**：支援多種執行環境（Python、Node.js、Go、Docker）
- **🚀 自動部署**：使用 Terraform 進行基礎架構即程式碼的部署
- **📊 結果摘要**：在 GitHub Actions Summary 中顯示詳細執行結果

## 🔄 工作流程

```mermaid
graph TD
    A[推送到 main 分支] --> B[偵測異動的服務]
    B --> C{有服務異動?}
    C -->|否| D[略過執行]
    C -->|是| E[並行執行測試]
    E --> F[測試完成]
    F --> G{是 main 分支?}
    G -->|否| H[僅測試，不部署]
    G -->|是| I[並行執行部署]
    I --> J[產生執行摘要]
```

## 🏗️ 觸發條件

### 自動觸發

- **推送到 main 分支**：當 `apps/` 目錄下有任何檔案異動
- **Pull Request**：對 main 分支的 PR，只執行測試階段

### 偵測範圍

- ✅ 偵測：`apps/` 目錄下所有服務的異動
- ❌ 排除：`apps/_template/` 目錄（範本目錄）

## 🧪 測試階段

### 自動偵測機制

Pipeline 會自動偵測每個服務的結構和技術堆疊：

#### 執行環境偵測

| 檔案存在                               | 識別為  | 安裝動作                          |
| -------------------------------------- | ------- | --------------------------------- |
| `requirements.txt` 或 `pyproject.toml` | Python  | `pip install -r requirements.txt` |
| `package.json`                         | Node.js | `npm ci`                          |
| `go.mod`                               | Go      | `go mod download`                 |
| `Dockerfile`                           | Docker  | 無額外安裝                        |

#### 測試執行邏輯

1. **檢查 Makefile**：是否存在且包含 `test` target
2. **有測試**：執行 `make test`
3. **無測試**：略過測試階段（不會失敗）

### 測試需求（建議）

- 每個服務最好包含 `Makefile` 並定義 `test` target
- 測試應該是自包含的，不依賴外部服務

## 🚀 部署階段

### 觸發條件

- ✅ 僅在 **main 分支** 觸發
- ✅ 測試階段必須完成（可以略過但不能失敗）

### Terraform 偵測

Pipeline 會自動偵測 Terraform 設定：

| 檔案/目錄                   | Terraform 目錄 |
| --------------------------- | -------------- |
| `main.tf` 或 `terraform.tf` | `.`（根目錄）  |
| `terraform/` 目錄           | `terraform/`   |

### 部署流程

1. **Terraform Init**：初始化 Terraform
2. **Terraform Plan**：預覽變更內容
3. **Terraform Apply**：執行部署（自動確認）
4. **驗證部署**：執行驗證腳本（如果存在）

## 🔐 Secret 變數設定

### 必要的基礎變數

在 `Repository Settings > Secrets and variables > Actions` 中設定：

```
WIF_PROVIDER                    # Workload Identity Federation Provider
WIF_SERVICE_ACCOUNT            # Service Account 電子郵件
```

### 服務專用變數

每個服務可能需要不同的敏感變數（如 API 金鑰、密鑰等）。

**建議**：請參考各服務目錄下的 README 文件了解所需的 secret 變數設定，這樣可以避免此文件過於冗長且易於維護。

常見的變數命名模式：

- `{SERVICE_NAME}_PROD_{VARIABLE_NAME}`
- 例如：`LEGACY_EVENT_SYNC_PROD_SECRET_KEY`

## 📁 服務結構需求

每個服務應該遵循以下結構：

```
apps/your-service/
├── Makefile                 # 包含 test target（建議）
├── requirements.txt         # Python 相依性（如果是 Python 服務）
├── src/                     # 原始碼目錄
├── tests/                   # 測試目錄
├── terraform/               # 或在根目錄的 main.tf
│   ├── main.tf
│   ├── variables.tf
│   └── terraform.tfvars.example
├── scripts/                 # 可選腳本目錄
│   └── verify-deployment.sh # 部署驗證腳本（可選）
└── README.md                # 服務說明文件（建議包含所需的 secret 變數）
```

## 📊 執行結果查看

### GitHub Actions Summary

每次執行完成後，可以在 Actions 頁面查看詳細的執行摘要：

- 📊 **執行統計**：影響的服務數量和清單
- 📋 **階段狀態**：測試和部署的狀態
- 🎯 **整體結果**：成功/失敗/部分完成

### 日誌查看

- 點選具體的 job 可以查看詳細日誌
- 每個服務的測試和部署會並行執行，互不影響

## 🆕 新增服務指南

### 1. 建立服務目錄

```bash
# 使用範本快速建立（推薦）
make new-service SERVICE_NAME=your-new-service

# 或手動建立
mkdir apps/your-new-service
```

### 2. 設定基本檔案

- 建立 `Makefile` 並定義 `test` target
- 設定對應的執行環境檔案（`requirements.txt`、`package.json` 等）
- 建立 `terraform/` 目錄和相關設定
- 撰寫 `README.md` 說明服務功能和所需的 secret 變數

### 3. 測試 Pipeline

- 建立 PR 測試 CI 流程
- 合併到 main 分支測試完整的 CD 流程

## 🔧 常見問題排除

### Q: 服務沒有被偵測到？

**A**: 確認檔案異動在 `apps/` 目錄下，且不是 `_template` 目錄

### Q: 測試失敗？

**A**: 檢查：

1. Makefile 是否存在且有 `test` target
2. 相依性是否正確安裝
3. 測試腳本是否可以獨立執行

### Q: 部署失敗？

**A**: 檢查：

1. Terraform 設定是否正確
2. Secret 變數是否設定
3. GCP 權限是否足夠

### Q: 想要強制重新部署所有服務？

**A**: 可以：

1. 修改一個會影響所有服務的共用檔案
2. 或者手動重新執行 workflow

## 📝 開發建議

1. **遵循測試驅動開發**：先寫測試，再寫功能
2. **小步提交**：避免一次修改太多服務
3. **檢查 Pipeline 狀態**：每次 push 後確認 CI/CD 執行狀態
4. **使用 PR 預覽**：大的變更先建立 PR 確認測試通過

## 🔄 工作流程最佳實務

### 開發流程

```bash
1. 建立功能分支
2. 開發 + 測試
3. 建立 PR（觸發測試）
4. Code Review
5. 合併到 main（觸發測試 + 部署）
```

### 故障處理

```bash
1. 部署失敗 → 修復問題 → 新的 commit → 自動重新部署
2. 緊急回復 → 手動觸發或回復 commit
```

---

**最後更新**：2025-06-27
**維護者**：Tagtoo Data Team
**問題回報**：請在專案中建立 Issue
