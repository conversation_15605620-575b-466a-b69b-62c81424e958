name: Release Pipeline

on:
  push:
    tags:
      - "*-[0-9]+.[0-9]+.[0-9]+*" # 服務前綴 + SemVer 格式 (legacy-event-sync-1.2.3)

env:
  PROJECT_ID: tagtoo-tracking
  REGION: asia-east1

jobs:
  # 解析發版資訊
  parse-release:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.parse.outputs.version }}
      service: ${{ steps.parse.outputs.service }}
      is_prerelease: ${{ steps.parse.outputs.is_prerelease }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Parse tag and service information
        id: parse
        run: |
          TAG_NAME=${GITHUB_REF#refs/tags/}
          echo "Tagged version: $TAG_NAME"

          # 解析 tag：格式 <service-name>-<version>
          if [[ "$TAG_NAME" =~ ^(.+)-([0-9]+\.[0-9]+\.[0-9]+.*)$ ]]; then
            SERVICE="${BASH_REMATCH[1]}"
            VERSION="${BASH_REMATCH[2]}"
          else
            echo "❌ Tag 格式不符合 <service-name>-<version>: $TAG_NAME"
            exit 1
          fi

          # 判斷是否為 pre-release (版本字串包含 '-')
          if [[ "$VERSION" == *"-"* ]]; then
            IS_PRERELEASE="true"
          else
            IS_PRERELEASE="false"
          fi

          # 驗證服務目錄是否存在
          if [ ! -d "apps/$SERVICE" ]; then
            echo "❌ 服務目錄不存在: apps/$SERVICE"
            exit 1
          fi

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "service=$SERVICE" >> $GITHUB_OUTPUT
          echo "is_prerelease=$IS_PRERELEASE" >> $GITHUB_OUTPUT

  # 建置和部署
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: parse-release
    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Display release information
        run: |
          echo "🚀 版本發佈資訊"
          echo "Version: ${{ needs.parse-release.outputs.version }}"
          echo "Service: ${{ needs.parse-release.outputs.service }}"
          echo "Is pre-release: ${{ needs.parse-release.outputs.is_prerelease }}"

      - name: 檢查服務結構
        id: check-structure
        working-directory: ./apps/${{ needs.parse-release.outputs.service }}
        run: |
          # 檢查 Terraform 目錄
          HAS_TERRAFORM="false"
          TERRAFORM_DIR=""

          if [ -f "main.tf" ] || [ -f "terraform.tf" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="."
          elif [ -d "terraform" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="terraform"
          fi

          # 檢查 Docker 相關檔案
          HAS_DOCKER="false"
          if [ -f "Dockerfile" ]; then
            HAS_DOCKER="true"
          fi

          echo "has_terraform=$HAS_TERRAFORM" >> $GITHUB_OUTPUT
          echo "terraform_dir=$TERRAFORM_DIR" >> $GITHUB_OUTPUT
          echo "has_docker=$HAS_DOCKER" >> $GITHUB_OUTPUT

          echo "Service structure:"
          echo "- Has Terraform: $HAS_TERRAFORM ($TERRAFORM_DIR)"
          echo "- Has Docker: $HAS_DOCKER"

      - name: 設定 Docker Buildx
        if: steps.check-structure.outputs.has_docker == 'true'
        uses: docker/setup-buildx-action@v3

      - name: 設定 Google Cloud 認證
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: 設定 Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: 配置 Docker 認證到 GCR
        if: steps.check-structure.outputs.has_docker == 'true'
        run: gcloud auth configure-docker gcr.io

      - name: 建置和推送 Docker 映像
        if: steps.check-structure.outputs.has_docker == 'true'
        working-directory: ./apps/${{ needs.parse-release.outputs.service }}
        run: |
          SERVICE_NAME="${{ needs.parse-release.outputs.service }}"
          VERSION="${{ needs.parse-release.outputs.version }}"

          IMAGE_NAME="gcr.io/${{ env.PROJECT_ID }}/${SERVICE_NAME}"

          echo "🏗️ 建置 Docker 映像..."
          SHORT_SHA=$(echo "$GITHUB_SHA" | cut -c1-12)

          docker build \
            --tag "${IMAGE_NAME}:${VERSION}" \
            --tag "${IMAGE_NAME}:latest" \
            --tag "${IMAGE_NAME}:${SHORT_SHA}" \
            .

          echo "📤 推送 Docker 映像..."
          docker push "${IMAGE_NAME}:${VERSION}"
          docker push "${IMAGE_NAME}:latest"
          docker push "${IMAGE_NAME}:${SHORT_SHA}"

          echo "✅ Docker 映像建置完成: ${IMAGE_NAME}:${VERSION}"

      - name: 設定 Terraform
        if: steps.check-structure.outputs.has_terraform == 'true'
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: 部署到生產環境
        if: steps.check-structure.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ needs.parse-release.outputs.service }}/${{ steps.check-structure.outputs.terraform_dir }}
        run: |
          SERVICE_NAME="${{ needs.parse-release.outputs.service }}"
          VERSION="${{ needs.parse-release.outputs.version }}"

          echo "🚀 部署 ${SERVICE_NAME} 版本 ${VERSION} 到生產環境..."

          # Terraform 初始化
          terraform init

          # 建立部署計畫
          terraform plan \
            -var="deployment_version=${VERSION}" \
            -var="environment=prod" \
            -out="release-${VERSION}.tfplan" \
            -no-color

          # 套用部署
          terraform apply \
            -no-color \
            -auto-approve \
            "release-${VERSION}.tfplan"

          # 清理計畫檔案
          rm -f "release-${VERSION}.tfplan"

          echo "✅ 部署完成"

      - name: 驗證部署狀態
        run: |
          SERVICE_NAME="${{ needs.parse-release.outputs.service }}"
          VERSION="${{ needs.parse-release.outputs.version }}"

          echo "🔍 驗證部署狀態..."

          # 檢查 Cloud Run 服務狀態
          if gcloud run services describe "${SERVICE_NAME}-prod" \
             --region=${{ env.REGION }} \
             --format="value(status.conditions[0].status)" 2>/dev/null | grep -q "True"; then
            echo "✅ Cloud Run 服務運行正常"
          else
            echo "⚠️ Cloud Run 服務狀態檢查失敗"
          fi

          # 顯示服務 URL
          SERVICE_URL=$(gcloud run services describe "${SERVICE_NAME}-prod" \
                       --region=${{ env.REGION }} \
                       --format="value(status.url)" 2>/dev/null || echo "N/A")

          echo "🌐 服務 URL: ${SERVICE_URL}"

  # 建立 GitHub Release 和 CHANGELOG
  create-release:
    runs-on: ubuntu-latest
    needs: [parse-release, build-and-deploy]
    permissions:
      contents: write # 需要寫入權限來建立 release

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 生成 CHANGELOG
        id: changelog
        run: |
          SERVICE_NAME="${{ needs.parse-release.outputs.service }}"
          VERSION="${{ needs.parse-release.outputs.version }}"

          echo "📝 生成 ${SERVICE_NAME} v${VERSION} 的 CHANGELOG..."

          # 獲取上一個版本標籤
          PREV_TAG=$(git tag --sort=-version:refname | grep -v "^${VERSION}$" | head -1 || echo "")

          if [ -n "$PREV_TAG" ]; then
            echo "Previous version: $PREV_TAG"
            COMMIT_RANGE="${PREV_TAG}..${VERSION}"
          else
            echo "No previous version found, using all commits"
            COMMIT_RANGE="${VERSION}"
          fi

          # 生成變更日誌
          echo "## 🚀 ${SERVICE_NAME} v${VERSION}" > changelog.md
          echo "" >> changelog.md
          echo "**Release Date:** $(date +'%Y-%m-%d')" >> changelog.md
          echo "" >> changelog.md

          # 獲取 commits 並分類
          echo "### 📋 變更內容" >> changelog.md
          echo "" >> changelog.md

          # 取得所有相關的 commits
          if [ -n "$PREV_TAG" ]; then
            COMMITS=$(git log --oneline --no-merges "${COMMIT_RANGE}" --grep="$SERVICE_NAME" --all-match || \
                     git log --oneline --no-merges "${COMMIT_RANGE}" -- "apps/${SERVICE_NAME}/" || \
                     git log --oneline --no-merges "${COMMIT_RANGE}" | head -10)
          else
            COMMITS=$(git log --oneline --no-merges -- "apps/${SERVICE_NAME}/" | head -20)
          fi

          if [ -n "$COMMITS" ]; then
            # 分類 commits
            echo "$COMMITS" | while read -r line; do
              commit_hash=$(echo "$line" | cut -d' ' -f1)
              commit_msg=$(echo "$line" | cut -d' ' -f2-)

              case "$commit_msg" in
                *"feat:"*|*"feature:"*)
                  echo "- 🚀 **新功能**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"fix:"*|*"bugfix:"*)
                  echo "- 🐛 **錯誤修復**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"docs:"*)
                  echo "- 📖 **文檔更新**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"refactor:"*)
                  echo "- 🔨 **程式碼重構**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"test:"*)
                  echo "- 🧪 **測試**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"chore:"*)
                  echo "- 🔧 **維護**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *"release"*|*"bump version"*)
                  echo "- 📦 **版本發佈**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
                *)
                  echo "- 🔄 **其他變更**: $commit_msg ($commit_hash)" >> changelog.md
                  ;;
              esac
            done
          else
            echo "- 📦 版本發佈" >> changelog.md
          fi

          echo "" >> changelog.md
          echo "### 🏗️ 部署資訊" >> changelog.md
          echo "" >> changelog.md
          echo "- **服務名稱**: ${SERVICE_NAME}" >> changelog.md
          echo "- **Docker Image**: gcr.io/${{ env.PROJECT_ID }}/${SERVICE_NAME}:${VERSION}" >> changelog.md
          echo "- **環境**: Production" >> changelog.md
          echo "- **區域**: ${{ env.REGION }}" >> changelog.md

          # 儲存 changelog 內容
          CHANGELOG_CONTENT=$(cat changelog.md)
          echo "changelog_content<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG_CONTENT" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: 建立 GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ github.ref_name }}
          name: "${{ needs.parse-release.outputs.service }} v${{ needs.parse-release.outputs.version }}"
          body: ${{ steps.changelog.outputs.changelog_content }}
          prerelease: ${{ needs.parse-release.outputs.is_prerelease == 'true' }}
          generate_release_notes: false # 使用我們自訂的 CHANGELOG
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 發版完成通知
        run: |
          echo "🎉 版本發佈完成！"
          echo ""
          echo "📋 發版摘要："
          echo "- 服務: ${{ needs.parse-release.outputs.service }}"
          echo "- 版本: ${{ needs.parse-release.outputs.version }} (tag: ${{ github.ref_name }})"
          echo "- 類型: ${{ needs.parse-release.outputs.is_prerelease == 'true' && 'Pre-release' || 'Production Release' }}"
          echo "- 映像: gcr.io/${{ env.PROJECT_ID }}/${{ needs.parse-release.outputs.service }}:${{ needs.parse-release.outputs.version }}"
          echo ""
          echo "🔗 GitHub Release: https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }}"
