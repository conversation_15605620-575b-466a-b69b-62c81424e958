name: Shared Infra CI/CD

on:
  push:
    branches: [dev, main]
    paths:
      - "infrastructure/terraform/shared/**"
      - ".github/workflows/infra-shared.yml"
  pull_request:
    paths:
      - "infrastructure/terraform/shared/**"
      - ".github/workflows/infra-shared.yml"

env:
  PROJECT_ID: tagtoo-tracking
  REGION: asia-east1

jobs:
  deploy-dev:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    if: github.ref == 'refs/heads/dev' || github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 🔧 修復: 添加完整 git 歷史

      - name: Authenticate to Google Cloud (DEV)
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ vars.WIF_PROVIDER }}
          service_account: ${{ vars.TERRAFORM_USER_SA }}
          create_credentials_file: true # 生成 ADC JSON，避免 getAccessToken 403
          export_environment_variables: true # 匯出環境變數供 Terraform / gcloud 使用

      # Debug: 檢查目前憑證身分 (DEV)
      - name: Debug – Show Active Credentials (DEV)
        run: |
          echo "=== gcloud auth list ==="
          gcloud auth list
          echo "=== ADC JSON client_email ==="
          jq -r '.client_email' "${GOOGLE_APPLICATION_CREDENTIALS}"

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: Terraform Init (DEV)
        working-directory: infrastructure/terraform/shared
        run: terraform init -input=false

      # Terraform 格式檢查 (DEV)
      - name: Terraform Format Check (DEV)
        working-directory: infrastructure/terraform/shared
        run: terraform fmt -check -recursive

      # Terraform 語法驗證 (DEV)
      - name: Terraform Validate (DEV)
        working-directory: infrastructure/terraform/shared
        run: terraform validate

      - name: Terraform Select Workspace (DEV)
        working-directory: infrastructure/terraform/shared
        run: |
          terraform workspace select dev || terraform workspace new dev

      - name: Terraform Plan (DEV)
        id: plan-dev
        working-directory: infrastructure/terraform/shared
        run: |
          TF_PLAN_OUTPUT=$(terraform plan -no-color -var-file=dev.tfvars -input=false -out=tfplan-dev 2>&1)
          TF_PLAN_EXIT_CODE=$?

          # 將 terraform plan 輸出加入到 summary
          echo "## 🏗️ 共用基礎設施 Terraform Plan (DEV)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ $TF_PLAN_EXIT_CODE -eq 0 ]; then
            if echo "$TF_PLAN_OUTPUT" | grep -q "No changes"; then
              echo "✅ **無基礎設施變更**" >> $GITHUB_STEP_SUMMARY
            else
              echo "📋 **偵測到基礎設施變更**" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "<details>" >> $GITHUB_STEP_SUMMARY
              echo "<summary>📝 點擊查看詳細 Terraform Plan 輸出</summary>" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "</details>" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "❌ **Terraform Plan 失敗**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "<details>" >> $GITHUB_STEP_SUMMARY
            echo "<summary>🔍 點擊查看錯誤詳情</summary>" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "</details>" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # 如果 plan 失敗，退出
          if [ $TF_PLAN_EXIT_CODE -ne 0 ]; then
            echo "Terraform plan failed with exit code $TF_PLAN_EXIT_CODE"
            exit $TF_PLAN_EXIT_CODE
          fi

      - name: Save Plan as Artifact (PR only)
        if: github.event_name == 'pull_request'
        uses: actions/upload-artifact@v4
        with:
          name: tfplan-dev
          path: infrastructure/terraform/shared/tfplan-dev

      - name: Terraform Apply (DEV)
        if: github.event_name != 'pull_request'
        working-directory: infrastructure/terraform/shared
        run: terraform apply -no-color -auto-approve -input=false tfplan-dev

  deploy-prod:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event_name == 'pull_request'
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 🔧 修復: 添加完整 git 歷史

      - name: Authenticate to Google Cloud (PROD)
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ vars.WIF_PROVIDER }}
          service_account: ${{ vars.TERRAFORM_USER_SA }}
          create_credentials_file: true # ➊ 生成 ADC
          export_environment_variables: true # ➋ 自動設環境變數

      # Debug: 檢查目前憑證身分 (PROD)
      - name: Debug – Show Active Credentials (PROD)
        run: |
          echo "=== gcloud auth list ==="
          gcloud auth list
          echo "=== ADC JSON client_email ==="
          jq -r '.client_email' "${GOOGLE_APPLICATION_CREDENTIALS}"

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: Terraform Init (PROD)
        working-directory: infrastructure/terraform/shared
        run: terraform init -input=false

      # Terraform 格式檢查 (PROD)
      - name: Terraform Format Check (PROD)
        working-directory: infrastructure/terraform/shared
        run: terraform fmt -check -recursive

      # Terraform 語法驗證 (PROD)
      - name: Terraform Validate (PROD)
        working-directory: infrastructure/terraform/shared
        run: terraform validate

      - name: Terraform Select Workspace (PROD)
        working-directory: infrastructure/terraform/shared
        run: |
          terraform workspace select prod || terraform workspace new prod

      - name: Terraform Plan (PROD)
        id: plan-prod
        working-directory: infrastructure/terraform/shared
        run: |
          TF_PLAN_OUTPUT=$(terraform plan -no-color -var-file=prod.tfvars -input=false -out=tfplan-prod 2>&1)
          TF_PLAN_EXIT_CODE=$?

          # 將 terraform plan 輸出加入到 summary
          echo "## 🏗️ 共用基礎設施 Terraform Plan (PROD)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ $TF_PLAN_EXIT_CODE -eq 0 ]; then
            if echo "$TF_PLAN_OUTPUT" | grep -q "No changes"; then
              echo "✅ **無基礎設施變更**" >> $GITHUB_STEP_SUMMARY
            else
              echo "📋 **偵測到基礎設施變更**" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "<details>" >> $GITHUB_STEP_SUMMARY
              echo "<summary>📝 點擊查看詳細 Terraform Plan 輸出</summary>" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "</details>" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "❌ **Terraform Plan 失敗**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "<details>" >> $GITHUB_STEP_SUMMARY
            echo "<summary>🔍 點擊查看錯誤詳情</summary>" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "</details>" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # 如果 plan 失敗，退出
          if [ $TF_PLAN_EXIT_CODE -ne 0 ]; then
            echo "Terraform plan failed with exit code $TF_PLAN_EXIT_CODE"
            exit $TF_PLAN_EXIT_CODE
          fi

      - name: Save Plan Artifact
        uses: actions/upload-artifact@v4
        with:
          name: tfplan-prod
          path: infrastructure/terraform/shared/tfplan-prod

      - name: Terraform Apply (PROD)
        if: github.event_name != 'pull_request'
        working-directory: infrastructure/terraform/shared
        run: terraform apply -no-color -auto-approve -input=false tfplan-prod
