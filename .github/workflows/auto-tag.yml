name: Auto Tag

on:
  push:
    branches:
      - main

permissions:
  contents: write # 需要寫入權限以推送 tag

jobs:
  auto-tag:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 需要完整歷史以比較版本

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Detect version bumps and create service tags
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          set -euo pipefail

          pip install packaging

          # 設定 Git 身分，避免 git tag 時因 committer identity 不明而失敗
          git config user.name  "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          CREATED_TAGS=""

          BEFORE_SHA="${{ github.event.before }}"
          AFTER_SHA="${{ github.sha }}"

          # act push 時 github.event.before 會是空字串，導致 git diff 取不到變更，
          # 這裡若偵測到為空就 fallback 使用上一個 commit 的 SHA，確保本地測試可執行。
          if [ -z "$BEFORE_SHA" ]; then
            BEFORE_SHA=$(git rev-parse "$AFTER_SHA^" 2>/dev/null || echo '')
          fi
          echo "🔍 Compare $BEFORE_SHA → $AFTER_SHA"

          # 找出變更的 pyproject.toml
          CHANGED_FILES=$(git diff --name-only "$BEFORE_SHA" "$AFTER_SHA" | grep -E '^apps/.+/pyproject.toml' || true)
          if [ -z "$CHANGED_FILES" ]; then
            echo "No pyproject.toml changed – skip"

            # 仍然寫入 Summary 供使用者辨識
            echo "## ℹ️ 本次 Commit 未偵測到 pyproject.toml 變更，未建立任何 Git Tag" >> "$GITHUB_STEP_SUMMARY"
            exit 0
          fi

          TAG_PUSH_NEEDED=false

          for FILE in $CHANGED_FILES; do
            SERVICE=$(echo "$FILE" | cut -d'/' -f2)
            NEW_VER=$(grep -oP 'version\s*=\s*"([^"]+)"' "$FILE" | head -1 | sed -E 's/.*"([^"]+)"/\1/')
            OLD_RAW=$(git show "$BEFORE_SHA:$FILE" 2>/dev/null || echo '')
            if [ -z "$OLD_RAW" ]; then
              echo "➕ $SERVICE first version $NEW_VER (no previous version)"
              SHOULD_TAG=true
            else
              OLD_VER=$(echo "$OLD_RAW" | grep -oP 'version\s*=\s*"([^"]+)"' | head -1 | sed -E 's/.*"([^"]+)"/\1/')
              export NEW_VER OLD_VER
              python -c 'from packaging.version import parse as _p; import os, sys; sys.exit(0 if _p(os.environ["NEW_VER"]) > _p(os.environ["OLD_VER"]) else 1)'
              if [ $? -eq 0 ]; then
                echo "⬆️  $SERVICE version bump: $OLD_VER → $NEW_VER"
                SHOULD_TAG=true
              else
                SHOULD_TAG=false
              fi
            fi

            if [ "$SHOULD_TAG" = true ]; then
              TAG="${SERVICE}-${NEW_VER}"
              if git tag --list | grep -q "^${TAG}$"; then
                echo "⚠️  Tag ${TAG} already exists, skip"
              else
                echo "🏷️  Creating tag ${TAG}"
                git tag -a "$TAG" -m "Auto tag for $SERVICE version $NEW_VER"
                TAG_PUSH_NEEDED=true

                # 收集 Tag 資訊供 Job Summary 使用
                SHA_SHORT=$(git rev-parse --short=12 "$AFTER_SHA")
                AUTHOR=$(git log -1 --format='%an' "$AFTER_SHA")
                DATE=$(git log -1 --date=format:'%Y-%m-%d %H:%M:%S UTC' --format='%ad' "$AFTER_SHA")
                TRIGGERER="${{ github.actor }}"
                CREATED_TAGS+="${TAG}|${SHA_SHORT}|${AUTHOR}|${TRIGGERER}|${DATE}\n"
              fi
            fi
          done

          if [ "$TAG_PUSH_NEEDED" = true ]; then
            echo "🚀 Pushing new tags..."
            git push origin --tags
          else
            echo "ℹ️  No new tags to push"
          fi

          # 產生 Job Summary
          if [ -n "$CREATED_TAGS" ]; then
            {
              echo "## 🏷️ 新增 Tags"
              echo ""
              echo "| Tag | Commit | Author | Triggered By | Date |"
              echo "| --- | ------ | ------ | ------------ | ---- |"
              while IFS='|' read -r T S A TR D; do
                [ -z "$T" ] && continue
                echo "| \`$T\` | \`$S\` | $A | $TR | $D |"
              done <<< "$(printf '%b' "$CREATED_TAGS")"
            } >> "$GITHUB_STEP_SUMMARY"
          else
            echo "## ℹ️ 無任何 tag 被建立" >> "$GITHUB_STEP_SUMMARY"
          fi

          # 保存 Tag 資訊供下一步使用
          echo -e "$CREATED_TAGS" > /tmp/created_tags.txt

      - name: Create draft releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          if [ ! -s /tmp/created_tags.txt ]; then
            echo "No tags to release"
            exit 0
          fi

          while IFS='|' read -r TAG SHA_SHORT AUTHOR TRIGGERER DATE; do
            [ -z "$TAG" ] && continue
            SERVICE_NAME="${TAG%-*}"
            VERSION="${TAG##*-}"
            TITLE="$SERVICE_NAME $VERSION"

            # 使用變數避免 heredoc 問題
            BODY="## ✨ 版本摘要
          | 項目 | 值 |
          |------|----|
          | **服務** | \`$SERVICE_NAME\` |
          | **版本** | \`$VERSION\` |
          | **Commit** | $SHA_SHORT |
          | **作者** | $AUTHOR |
          | **觸發者** | $TRIGGERER |
          | **日期 (UTC)** | $DATE |

          ---

          > 🚧 Auto-generated draft, please add notes."

            echo "📝 Creating draft release for $TAG"
            gh release create "$TAG" --title "$TITLE" --notes "$BODY" --draft --verify-tag || echo "⚠️  Release $TAG already exists or failed"
          done < /tmp/created_tags.txt
