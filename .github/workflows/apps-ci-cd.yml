name: Apps CI/CD Pipeline

on:
  push:
    branches: [main]
    paths:
      - "apps/**"
      - ".github/workflows/apps-ci-cd.yml"
  pull_request:
    branches: [main]
    paths:
      - "apps/**"
      - ".github/workflows/apps-ci-cd.yml"
  workflow_dispatch:
    inputs:
      service:
        description: "指定要部署的服務（留空則部署所有變動的服務）"
        required: false
        type: string
      environment:
        description: "目標環境"
        required: true
        default: "prod"
        type: choice
        options:
          - prod
          - dev

env:
  PROJECT_ID: tagtoo-tracking
  REGION: asia-east1

jobs:
  # 檢測變動的服務
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.changes.outputs.services }}
      matrix: ${{ steps.changes.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changed services
        id: changes
        run: |
          # 如果是手動觸發且指定了服務
          if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ -n "${{ github.event.inputs.service }}" ]; then
            SERVICES="${{ github.event.inputs.service }}"
            echo "Manual dispatch for service: $SERVICES"
          else
            # 獲取變動的檔案
            if [ "${{ github.event_name }}" = "pull_request" ]; then
              CHANGED_FILES=$(git diff --name-only ${{ github.event.pull_request.base.sha }}...${{ github.sha }})
            elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
              # 手動觸發但未指定服務，檢查所有服務
              CHANGED_FILES="apps/legacy-event-sync/README.md"
              echo "Manual dispatch without service specified, deploying legacy-event-sync"
            else
              CHANGED_FILES=$(git diff --name-only ${{ github.event.before }}...${{ github.sha }})
            fi

            echo "Changed files:"
            echo "$CHANGED_FILES"

            # 找出變動的服務
            SERVICES=$(echo "$CHANGED_FILES" | grep "^apps/" | cut -d'/' -f2 | sort -u | grep -v "^_template$" || true)
          fi

          echo "Detected services:"
          echo "$SERVICES"

          # 將多行 SERVICES 轉成單行字串，避免 GITHUB_OUTPUT 格式錯誤
          SERVICES_SINGLE=$(echo "$SERVICES" | tr '\n' ' ' | xargs)

          # 建立 JSON 陣列給 matrix strategy
          if [ -n "$SERVICES" ]; then
            MATRIX=$(echo "$SERVICES" | jq -R -s -c 'split("\n") | map(select(length > 0))')
            echo "services=$SERVICES_SINGLE" >> $GITHUB_OUTPUT
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
          else
            echo "services=" >> $GITHUB_OUTPUT
            echo "matrix=[]" >> $GITHUB_OUTPUT
          fi

  # 早期 Terraform 驗證階段（快速失敗）
  terraform-validation:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.matrix != '[]'
    strategy:
      matrix:
        service: ${{ fromJSON(needs.detect-changes.outputs.matrix) }}
      fail-fast: true # Terraform 驗證失敗就立即停止所有任務

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 🔧 修復: 添加完整 git 歷史

      - name: Check Terraform structure
        id: check-terraform
        working-directory: ./apps/${{ matrix.service }}
        run: |
          HAS_TERRAFORM="false"
          TERRAFORM_DIR=""

          if [ -f "main.tf" ] || [ -f "terraform.tf" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="."
          elif [ -d "terraform" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="terraform"
          fi

          echo "has_terraform=$HAS_TERRAFORM" >> $GITHUB_OUTPUT
          echo "terraform_dir=$TERRAFORM_DIR" >> $GITHUB_OUTPUT

          echo "Service: ${{ matrix.service }}"
          echo "Has Terraform: $HAS_TERRAFORM"
          echo "Terraform directory: $TERRAFORM_DIR"

      - name: Set up Terraform
        if: steps.check-terraform.outputs.has_terraform == 'true'
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: Early Terraform Validation
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          echo "🔍 執行早期 Terraform 驗證（快速失敗）..."

          # 1. 格式檢查
          echo "📝 檢查 Terraform 格式..."
          terraform fmt -check -recursive || {
            echo "❌ Terraform 格式檢查失敗！請執行 'make tf-fmt' 修正。"
            exit 1
          }

          # 2. 基本語法驗證（無需後端）
          echo "🔍 執行 Terraform 語法驗證..."
          terraform init -backend=false
          terraform validate || {
            echo "❌ Terraform 語法驗證失敗！請檢查 .tf 文件語法。"
            exit 1
          }

          echo "✅ Terraform 早期驗證通過"

      - name: Skip Terraform validation
        if: steps.check-terraform.outputs.has_terraform == 'false'
        run: |
          echo "⏭️  服務 ${{ matrix.service }} 沒有 Terraform 設定，跳過驗證"

  # 測試階段
  test:
    runs-on: ubuntu-latest
    needs: [detect-changes, terraform-validation]
    if: needs.detect-changes.outputs.matrix != '[]'
    strategy:
      matrix:
        service: ${{ fromJSON(needs.detect-changes.outputs.matrix) }}
      fail-fast: false

    permissions:
      contents: read
      # 必須設定 id-token: write 才能使用 Workload Identity Federation (WIF)
      # GitHub Actions 預設 id-token 權限為 none，需要明確設定為 write
      # 才能注入 $ACTIONS_ID_TOKEN_REQUEST_TOKEN 和 $ACTIONS_ID_TOKEN_REQUEST_URL
      # 這些環境變數是 google-github-actions/auth@v2 進行 OIDC 認證的必要條件
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 🔧 修復: 添加完整 git 歷史

      - name: 安裝 Docker Compose
        run: |
          sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
          sudo chmod +x /usr/local/bin/docker-compose
          docker-compose --version

      - name: Check service structure
        id: check-structure
        working-directory: ./apps/${{ matrix.service }}
        run: |
          # 檢查是否有 Makefile 且有 test target
          HAS_MAKEFILE="false"
          HAS_TEST_TARGET="false"
          RUNTIME="unknown"

          if [ -f "Makefile" ]; then
            HAS_MAKEFILE="true"
            if make -n test >/dev/null 2>&1; then
              HAS_TEST_TARGET="true"
            fi
          fi

          # 檢測 runtime
          if [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
            RUNTIME="python"
          elif [ -f "package.json" ]; then
            RUNTIME="node"
          elif [ -f "go.mod" ]; then
            RUNTIME="go"
          elif [ -f "Dockerfile" ]; then
            RUNTIME="docker"
          fi

          echo "has_makefile=$HAS_MAKEFILE" >> $GITHUB_OUTPUT
          echo "has_test_target=$HAS_TEST_TARGET" >> $GITHUB_OUTPUT
          echo "runtime=$RUNTIME" >> $GITHUB_OUTPUT

          echo "Service: ${{ matrix.service }}"
          echo "Has Makefile: $HAS_MAKEFILE"
          echo "Has test target: $HAS_TEST_TARGET"
          echo "Runtime: $RUNTIME"

      - name: Set up Python
        if: steps.check-structure.outputs.runtime == 'python'
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Authenticate to Google Cloud
        if: steps.check-structure.outputs.has_test_target == 'true'
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ vars.WIF_PROVIDER }}
          service_account: ${{ vars.INTEGRATED_EVENT_RUNNER_SA }}
          # 建立 ADC 檔案供 Docker 容器使用
          create_credentials_file: true

      - name: Set up Cloud SDK
        if: steps.check-structure.outputs.has_test_target == 'true'
        uses: google-github-actions/setup-gcloud@v2

      - name: Test authentication
        if: steps.check-structure.outputs.has_test_target == 'true'
        run: |
          echo "🔍 測試 Google Cloud 認證..."
          gcloud auth list
          gcloud config list

          # 測試 BigQuery 連線
          echo "🔍 測試 BigQuery 連線..."
          bq ls --max_results=1 || echo "BigQuery 連線測試失敗（可能是權限問題）"

      - name: Prepare test environment
        if: steps.check-structure.outputs.has_test_target == 'true'
        working-directory: ./apps/${{ matrix.service }}
        run: |
          # 建立 .gcloud-creds 目錄並複製 ADC 檔案
          mkdir -p ../../.gcloud-creds
          cp "$GOOGLE_APPLICATION_CREDENTIALS" ../../.gcloud-creds/adc.json

          # 修復檔案權限，確保容器中的 appuser 可以讀取
          chmod 644 ../../.gcloud-creds/adc.json

          # 檢查檔案權限和內容
          echo "✅ 測試環境準備完成"
          echo "ADC 檔案位置: $GOOGLE_APPLICATION_CREDENTIALS"
          echo "容器掛載位置: ../../.gcloud-creds/adc.json"
          echo "檔案權限: $(ls -la ../../.gcloud-creds/adc.json)"
          echo "檔案大小: $(wc -c < ../../.gcloud-creds/adc.json) bytes"

      - name: Run tests
        if: steps.check-structure.outputs.has_test_target == 'true'
        working-directory: ./apps/${{ matrix.service }}
        run: |
          echo "🧪 執行測試..."
          make test

      # 🔧 修復: 修正測試結果和覆蓋率報告的路徑
      - name: Upload test results
        if: steps.check-structure.outputs.has_test_target == 'true' && always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.service }}
          path: |
            ./apps/${{ matrix.service }}/.coverage
            ./apps/${{ matrix.service }}/coverage.xml
            ./apps/${{ matrix.service }}/htmlcov/
            ./apps/${{ matrix.service }}/pytest-report.xml
          retention-days: 7
          if-no-files-found: warn # 🔧 修復: 如果沒有檔案不要失敗

      - name: Skip tests
        if: steps.check-structure.outputs.has_test_target == 'false'
        run: |
          echo "⏭️  服務 ${{ matrix.service }} 沒有測試設定，跳過測試階段"

  # 🆕 驗證 Shared Infrastructure 並取得 Outputs（純讀取模式）
  ensure-shared-infrastructure:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.matrix != '[]' && github.ref == 'refs/heads/main'

    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check if shared infrastructure changes exist
        id: check-shared-changes
        run: |
          # 🔧 修復: 改善 git diff 錯誤處理和 SHA 檢查
          CHANGED_FILES=""

          echo "🔍 檢查 shared infrastructure 變更..."
          echo "Event: ${{ github.event_name }}"
          echo "Before SHA: ${{ github.event.before }}"
          echo "Current SHA: ${{ github.sha }}"

          # 根據觸發事件使用不同的策略
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "🔍 PR 模式：比較 base 和 head SHA"
            if git cat-file -e "${{ github.event.pull_request.base.sha }}" 2>/dev/null; then
              CHANGED_FILES=$(git diff --name-only ${{ github.event.pull_request.base.sha }}...${{ github.sha }} 2>/dev/null || echo "")
            else
              echo "⚠️ Base SHA 無效，使用 HEAD^..HEAD"
              CHANGED_FILES=$(git diff --name-only HEAD^..HEAD 2>/dev/null || echo "")
            fi
          elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # 手動觸發：假設沒有 shared infrastructure 變更，直接檢查已部署狀態
            echo "🔍 手動觸發：假設沒有 shared infrastructure 變更"
            CHANGED_FILES=""
          else
            # Push 事件：使用漸進式回退策略
            echo "🔍 Push 模式：使用多重回退策略"

            # 策略 1: 使用 before SHA
            if [ -n "${{ github.event.before }}" ] && [ "${{ github.event.before }}" != "0000000000000000000000000000000000000000" ]; then
              if git cat-file -e "${{ github.event.before }}" 2>/dev/null; then
                echo "✅ 策略 1: 使用 before SHA"
                CHANGED_FILES=$(git diff --name-only ${{ github.event.before }}...${{ github.sha }} 2>/dev/null || echo "")
              else
                echo "⚠️ Before SHA 不存在，嘗試策略 2"
                # 策略 2: 使用 HEAD^
                if git cat-file -e "HEAD^" 2>/dev/null; then
                  echo "✅ 策略 2: 使用 HEAD^"
                  CHANGED_FILES=$(git diff --name-only HEAD^..HEAD 2>/dev/null || echo "")
                else
                  echo "⚠️ HEAD^ 不存在，使用策略 3"
                  # 策略 3: 使用 git show（最近一次 commit）
                  CHANGED_FILES=$(git show --name-only --pretty=format: HEAD 2>/dev/null | grep -v '^$' || echo "")
                fi
              fi
            else
              echo "⚠️ Before SHA 為空或無效，使用 git show"
              CHANGED_FILES=$(git show --name-only --pretty=format: HEAD 2>/dev/null | grep -v '^$' || echo "")
            fi
          fi

          echo "🔍 檢測到的變更檔案:"
          echo "$CHANGED_FILES"

          # 檢查是否有 shared infrastructure 變更
          if [ -n "$CHANGED_FILES" ] && echo "$CHANGED_FILES" | grep -q "^infrastructure/terraform/shared/"; then
            echo "shared_changed=true" >> $GITHUB_OUTPUT
            echo "🔄 檢測到 shared infrastructure 變更，跳過此 job"
            echo "📋 infra-shared.yml 會處理 shared infrastructure 部署"
          else
            echo "shared_changed=false" >> $GITHUB_OUTPUT
            echo "✅ 沒有 shared infrastructure 變更，讀取已部署的 outputs"
          fi

      - name: Authenticate to Google Cloud
        if: steps.check-shared-changes.outputs.shared_changed == 'false'
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ vars.WIF_PROVIDER }}
          service_account: ${{ vars.TERRAFORM_USER_SA }}

      - name: Set up Cloud SDK
        if: steps.check-shared-changes.outputs.shared_changed == 'false'
        uses: google-github-actions/setup-gcloud@v2

      - name: Set up Terraform
        if: steps.check-shared-changes.outputs.shared_changed == 'false'
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: Verify Shared Infrastructure Outputs (Read-only)
        if: steps.check-shared-changes.outputs.shared_changed == 'false'
        working-directory: ./infrastructure/terraform/shared
        run: |
          echo "📋 驗證 Shared Infrastructure 並讀取 Outputs（純讀取模式）..."

          # 初始化 Terraform
          terraform init

          # 選擇 prod workspace
          if ! terraform workspace select prod 2>/dev/null; then
            echo "❌ 無法找到 prod workspace"
            echo "請先確保 Shared Infrastructure 已透過 infra-shared.yml 部署"
            exit 1
          fi

          # 檢查 remote state 是否存在且有效（純讀取，不執行任何 apply）
          echo "🔍 檢查 remote state..."
          if ! terraform show -json > /dev/null 2>&1; then
            echo "❌ Shared Infrastructure remote state 不存在或無效"
            echo "請先透過 infra-shared.yml 部署 Shared Infrastructure："
            echo "  1. 推送 infrastructure/terraform/shared/ 目錄的變更"
            echo "  2. 等待 infra-shared.yml 完成部署"
            echo "  3. 再推送應用程式變更"
            exit 1
          fi

          echo "✅ Shared Infrastructure remote state 存在且有效"

          # 讀取並驗證必要的 outputs（不執行任何 plan 或 apply）
          echo "📊 讀取 Shared Infrastructure 輸出..."

          # 驗證關鍵 outputs 存在
          echo "檢查 project_id..."
          terraform output project_id > /dev/null || {
            echo "❌ 無法讀取 project_id output"
            exit 1
          }

          echo "檢查 region..."
          terraform output region > /dev/null || {
            echo "❌ 無法讀取 region output"
            exit 1
          }

          echo "檢查 service_account_email..."
          terraform output service_account_email > /dev/null || {
            echo "❌ 無法讀取 service_account_email output"
            exit 1
          }

          echo "📊 顯示重要的 Shared Infrastructure 資訊："
          echo "  Project ID: $(terraform output -raw project_id)"
          echo "  Region: $(terraform output -raw region)"
          echo "  Service Account: $(terraform output -raw service_account_email)"

          echo "✅ Shared Infrastructure 輸出驗證完成（純讀取模式）"

      - name: Skip due to shared infrastructure changes
        if: steps.check-shared-changes.outputs.shared_changed == 'true'
        run: |
          echo "⏭️  跳過 shared infrastructure 驗證（檢測到 shared infrastructure 變更）"
          echo "infra-shared.yml 會處理 shared infrastructure 的部署"
          echo "應用服務部署將在 shared infrastructure 部署完成後進行"

  # 部署階段
  deploy:
    runs-on: ubuntu-latest
    needs:
      [detect-changes, terraform-validation, test, ensure-shared-infrastructure]
    if: needs.detect-changes.outputs.matrix != '[]' && github.ref == 'refs/heads/main'
    strategy:
      matrix:
        service: ${{ fromJSON(needs.detect-changes.outputs.matrix) }}
      fail-fast: false

    # environment: production  # 如果你有設定 GitHub Environments 才需要這行

    permissions:
      contents: read
      # 必須設定 id-token: write 才能使用 Workload Identity Federation (WIF)
      # 部署階段需要透過 WIF 認證到 Google Cloud 執行 Terraform 部署
      # 詳細說明請參考: https://docs.github.com/en/actions/security-guides/automatic-token-authentication#permissions-for-the-github_token
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 🔧 修復: 添加完整 git 歷史

      - name: Check Terraform structure
        id: check-terraform
        working-directory: ./apps/${{ matrix.service }}
        run: |
          HAS_TERRAFORM="false"
          TERRAFORM_DIR=""

          # 檢查是否有 terraform 設定
          if [ -f "main.tf" ] || [ -f "terraform.tf" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="."
          elif [ -d "terraform" ]; then
            HAS_TERRAFORM="true"
            TERRAFORM_DIR="terraform"
          fi

          echo "has_terraform=$HAS_TERRAFORM" >> $GITHUB_OUTPUT
          echo "terraform_dir=$TERRAFORM_DIR" >> $GITHUB_OUTPUT

          echo "Service: ${{ matrix.service }}"
          echo "Has Terraform: $HAS_TERRAFORM"
          echo "Terraform directory: $TERRAFORM_DIR"

      - name: Authenticate to Google Cloud
        if: steps.check-terraform.outputs.has_terraform == 'true'
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ vars.WIF_PROVIDER }}
          service_account: ${{ vars.TERRAFORM_USER_SA }}

      - name: Set up Cloud SDK
        if: steps.check-terraform.outputs.has_terraform == 'true'
        uses: google-github-actions/setup-gcloud@v2

      - name: Set up Terraform
        if: steps.check-terraform.outputs.has_terraform == 'true'
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ~1.12.0

      - name: Check service structure for deployment
        id: check-structure
        working-directory: ./apps/${{ matrix.service }}
        run: |
          # 檢測 runtime - 優先檢查 Dockerfile
          RUNTIME="unknown"
          HAS_DOCKERFILE="false"

          # 檢查是否有 Dockerfile（用於 Docker build）
          if [ -f "Dockerfile" ]; then
            HAS_DOCKERFILE="true"
            RUNTIME="docker"
          elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
            RUNTIME="python"
          elif [ -f "package.json" ]; then
            RUNTIME="node"
          elif [ -f "go.mod" ]; then
            RUNTIME="go"
          fi

          echo "runtime=$RUNTIME" >> $GITHUB_OUTPUT
          echo "has_dockerfile=$HAS_DOCKERFILE" >> $GITHUB_OUTPUT
          echo "Service: ${{ matrix.service }}"
          echo "Runtime: $RUNTIME"
          echo "Has Dockerfile: $HAS_DOCKERFILE"

      # 增加 Docker Build & Push 步驟
      - name: Set up Docker Buildx
        if: steps.check-terraform.outputs.has_terraform == 'true' && steps.check-structure.outputs.has_dockerfile == 'true'
        uses: docker/setup-buildx-action@v3

      - name: Configure Docker for GCP
        if: steps.check-terraform.outputs.has_terraform == 'true' && steps.check-structure.outputs.has_dockerfile == 'true'
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

      # 計算 12 位數短 SHA，供映像 tag 使用
      - name: Set short SHA
        if: steps.check-terraform.outputs.has_terraform == 'true' && steps.check-structure.outputs.has_dockerfile == 'true'
        id: vars
        run: echo "short_sha=$(echo ${GITHUB_SHA} | cut -c1-12)" >> $GITHUB_OUTPUT

      - name: Build and Push Docker Image
        if: steps.check-terraform.outputs.has_terraform == 'true' && steps.check-structure.outputs.has_dockerfile == 'true'
        uses: docker/build-push-action@v5
        with:
          context: ./apps/${{ matrix.service }}
          push: true
          tags: |
            ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/integrated-event-apps/${{ matrix.service }}:${{ github.sha }}
            ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/integrated-event-apps/${{ matrix.service }}:${{ steps.vars.outputs.short_sha }}

      - name: Terraform Init
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          terraform init

      # Terraform 格式檢查
      - name: Terraform Format Check
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          terraform fmt -check -recursive

      # Terraform 語法驗證
      - name: Terraform Validate
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          terraform validate

      - name: Terraform Plan
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          # 設定基本變數
          TF_VARS=""
          TF_VARS="$TF_VARS -var=deployment_version=${{ github.sha }}"
          TF_VARS="$TF_VARS -var=environment=prod"

          # 根據服務添加敏感變數
          case "${{ matrix.service }}" in
            "legacy-event-sync")
              # 註解：目前 legacy-event-sync 服務不需要 secret_key
              # TF_VARS="$TF_VARS -var=secret_key=${{ secrets.LEGACY_EVENT_SYNC_PROD_SECRET_KEY }}"
              # [ -n "${{ secrets.LEGACY_EVENT_SYNC_PROD_API_KEY }}" ] && TF_VARS="$TF_VARS -var=api_key=${{ secrets.LEGACY_EVENT_SYNC_PROD_API_KEY }}"
              ;;
            # 其他服務的設定可以在這裡添加
          esac

          # 執行 terraform plan (添加 -no-color 避免 ANSI 顏色代碼)
          echo "🔍 Terraform Plan..."
          TF_PLAN_OUTPUT=$(terraform plan -no-color $TF_VARS 2>&1)
          TF_PLAN_EXIT_CODE=$?

          # 將 terraform plan 輸出加入到 summary
          echo "## 🏗️ Terraform Plan - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ $TF_PLAN_EXIT_CODE -eq 0 ]; then
            if echo "$TF_PLAN_OUTPUT" | grep -q "No changes"; then
              echo "✅ **無基礎設施變更**" >> $GITHUB_STEP_SUMMARY
            else
              echo "📋 **偵測到基礎設施變更**" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "<details>" >> $GITHUB_STEP_SUMMARY
              echo "<summary>📝 點擊查看詳細 Terraform Plan 輸出</summary>" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
              echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "</details>" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "❌ **Terraform Plan 失敗**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "<details>" >> $GITHUB_STEP_SUMMARY
            echo "<summary>🔍 點擊查看錯誤詳情</summary>" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "$TF_PLAN_OUTPUT" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "</details>" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY

          # 如果 plan 失敗，退出
          if [ $TF_PLAN_EXIT_CODE -ne 0 ]; then
            echo "Terraform plan failed with exit code $TF_PLAN_EXIT_CODE"
            exit $TF_PLAN_EXIT_CODE
          fi

      - name: Terraform Apply
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}/${{ steps.check-terraform.outputs.terraform_dir }}
        run: |
          # 設定變數（與 plan 相同）
          TF_VARS=""
          TF_VARS="$TF_VARS -var=deployment_version=${{ github.sha }}"
          TF_VARS="$TF_VARS -var=environment=prod"

          case "${{ matrix.service }}" in
            "legacy-event-sync")
              # 註解：目前 legacy-event-sync 服務不需要 secret_key
              # TF_VARS="$TF_VARS -var=secret_key=${{ secrets.LEGACY_EVENT_SYNC_PROD_SECRET_KEY }}"
              [ -n "${{ secrets.LEGACY_EVENT_SYNC_PROD_API_KEY }}" ] && TF_VARS="$TF_VARS -var=api_key=${{ secrets.LEGACY_EVENT_SYNC_PROD_API_KEY }}"
              ;;
          esac

          # 執行 terraform apply (添加 -no-color 避免 ANSI 顏色代碼)
          echo "🚀 Terraform Apply..."
          terraform apply -no-color -auto-approve $TF_VARS

      - name: Verify Deployment
        if: steps.check-terraform.outputs.has_terraform == 'true'
        working-directory: ./apps/${{ matrix.service }}
        run: |
          echo "✅ 部署完成"

          # 等待服務啟動
          sleep 30

          # 如果有驗證腳本就執行
          if [ -f "scripts/verify-deployment.sh" ]; then
            echo "🔍 執行部署驗證..."
            bash scripts/verify-deployment.sh prod
          fi

      - name: Skip deployment
        if: steps.check-terraform.outputs.has_terraform == 'false'
        run: |
          echo "⏭️  服務 ${{ matrix.service }} 沒有 Terraform 設定，跳過部署階段"

  # 結果總結
  summary:
    runs-on: ubuntu-latest
    needs:
      [
        detect-changes,
        terraform-validation,
        test,
        deploy,
        ensure-shared-infrastructure,
      ]
    if: always() && needs.detect-changes.outputs.matrix != '[]'
    steps:
      - uses: actions/checkout@v4

      - name: Generate detailed summary
        run: |
          echo "# 🚀 Apps CI/CD 執行報告" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 基本資訊
          echo "## 📋 執行資訊" >> $GITHUB_STEP_SUMMARY
          echo "| 項目 | 值 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|-----|" >> $GITHUB_STEP_SUMMARY
          echo "| 🕐 執行時間 | $(date +'%Y-%m-%d %H:%M:%S UTC') |" >> $GITHUB_STEP_SUMMARY
          echo "| 🌿 分支 | \`${{ github.ref_name }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| 📝 提交 | [\`${{ github.sha }}\`](https://github.com/${{ github.repository }}/commit/${{ github.sha }}) |" >> $GITHUB_STEP_SUMMARY
          echo "| 👤 觸發者 | ${{ github.actor }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 🎯 觸發原因 | ${{ github.event_name }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 影響服務統計
          SERVICES="${{ needs.detect-changes.outputs.services }}"
          SERVICES_COUNT=$(echo "$SERVICES" | wc -w)

          echo "## 📊 影響範圍" >> $GITHUB_STEP_SUMMARY
          echo "- **總計服務數**: $SERVICES_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- **服務清單**: $(echo "$SERVICES" | tr ' ' ',' | sed 's/,/, /g')" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 各階段狀態
          TERRAFORM_VALIDATION_STATUS="${{ needs.terraform-validation.result }}"
          TEST_STATUS="${{ needs.test.result }}"
          DEPLOY_STATUS="${{ needs.deploy.result }}"
          SHARED_INFRA_STATUS="${{ needs.ensure-shared-infrastructure.result }}"

          echo "## 🏗️ 各階段執行狀態" >> $GITHUB_STEP_SUMMARY
          echo "| 階段 | 狀態 | 說明 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|------|------|" >> $GITHUB_STEP_SUMMARY

          # Terraform 驗證狀態
          case "$TERRAFORM_VALIDATION_STATUS" in
            "success") TERRAFORM_ICON="✅"; TERRAFORM_DESC="語法和格式檢查通過" ;;
            "failure") TERRAFORM_ICON="❌"; TERRAFORM_DESC="語法或格式檢查失敗" ;;
            "skipped") TERRAFORM_ICON="⏭️"; TERRAFORM_DESC="無 Terraform 設定" ;;
            *) TERRAFORM_ICON="⚠️"; TERRAFORM_DESC="狀態未知" ;;
          esac
          echo "| 🔍 Terraform 驗證 | $TERRAFORM_ICON $TERRAFORM_VALIDATION_STATUS | $TERRAFORM_DESC |" >> $GITHUB_STEP_SUMMARY

          # 共享基礎設施狀態
          case "$SHARED_INFRA_STATUS" in
            "success") SHARED_ICON="✅"; SHARED_DESC="共享基礎設施驗證完成" ;;
            "failure") SHARED_ICON="❌"; SHARED_DESC="共享基礎設施驗證失敗" ;;
            "skipped") SHARED_ICON="⏭️"; SHARED_DESC="非主分支，跳過驗證" ;;
            *) SHARED_ICON="⚠️"; SHARED_DESC="狀態未知" ;;
          esac
          echo "| 🏢 共享基礎設施 | $SHARED_ICON $SHARED_INFRA_STATUS | $SHARED_DESC |" >> $GITHUB_STEP_SUMMARY

          # 測試狀態
          case "$TEST_STATUS" in
            "success") TEST_ICON="✅"; TEST_DESC="所有測試通過" ;;
            "failure") TEST_ICON="❌"; TEST_DESC="測試失敗或錯誤" ;;
            "skipped") TEST_ICON="⏭️"; TEST_DESC="無測試設定" ;;
            *) TEST_ICON="⚠️"; TEST_DESC="狀態未知" ;;
          esac
          echo "| 🧪 測試 | $TEST_ICON $TEST_STATUS | $TEST_DESC |" >> $GITHUB_STEP_SUMMARY

          # 部署狀態
          case "$DEPLOY_STATUS" in
            "success") DEPLOY_ICON="✅"; DEPLOY_DESC="部署成功完成" ;;
            "failure") DEPLOY_ICON="❌"; DEPLOY_DESC="部署失敗" ;;
            "skipped") DEPLOY_ICON="⏭️"; DEPLOY_DESC="非主分支或無 Terraform 設定" ;;
            *) DEPLOY_ICON="⚠️"; DEPLOY_DESC="狀態未知" ;;
          esac
          echo "| 🚀 部署 | $DEPLOY_ICON $DEPLOY_STATUS | $DEPLOY_DESC |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 服務詳細狀態
          echo "## 📦 服務詳細狀態" >> $GITHUB_STEP_SUMMARY
          echo "| 服務 | Terraform 驗證 | 測試 | 部署 | 狀態 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|-------------|------|------|------|" >> $GITHUB_STEP_SUMMARY

          for SERVICE in $SERVICES; do
            # 預設所有服務都會經過各階段（實際狀態由 matrix strategy 決定）
            SERVICE_TERRAFORM="✅"
            SERVICE_TEST="✅"
            SERVICE_DEPLOY="✅"
            SERVICE_STATUS="✅ 正常"

            # 如果有任何階段失敗，調整狀態
            if [ "$TERRAFORM_VALIDATION_STATUS" = "failure" ]; then
              SERVICE_TERRAFORM="❌"
              SERVICE_STATUS="❌ 失敗"
            fi

            if [ "$TEST_STATUS" = "failure" ]; then
              SERVICE_TEST="❌"
              SERVICE_STATUS="❌ 失敗"
            fi

            if [ "$DEPLOY_STATUS" = "failure" ]; then
              SERVICE_DEPLOY="❌"
              SERVICE_STATUS="❌ 失敗"
            fi

            # 處理跳過的情況
            if [ "$TEST_STATUS" = "skipped" ]; then
              SERVICE_TEST="⏭️"
            fi

            if [ "$DEPLOY_STATUS" = "skipped" ]; then
              SERVICE_DEPLOY="⏭️"
              SERVICE_STATUS="⏭️ 跳過"
            fi

            echo "| \`$SERVICE\` | $SERVICE_TERRAFORM | $SERVICE_TEST | $SERVICE_DEPLOY | $SERVICE_STATUS |" >> $GITHUB_STEP_SUMMARY
          done
          echo "" >> $GITHUB_STEP_SUMMARY

          # 決定整體狀態
          OVERALL_STATUS="✅ 成功"
          OVERALL_COLOR="🟢"

          if [ "$TERRAFORM_VALIDATION_STATUS" = "failure" ] || [ "$TEST_STATUS" = "failure" ] || [ "$DEPLOY_STATUS" = "failure" ] || [ "$SHARED_INFRA_STATUS" = "failure" ]; then
            OVERALL_STATUS="❌ 失敗"
            OVERALL_COLOR="🔴"
          elif [ "$TERRAFORM_VALIDATION_STATUS" = "skipped" ] || [ "$TEST_STATUS" = "skipped" ] || [ "$DEPLOY_STATUS" = "skipped" ]; then
            OVERALL_STATUS="⚠️ 部分完成"
            OVERALL_COLOR="🟡"
          fi

          echo "## 🎯 總體結果" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### $OVERALL_COLOR $OVERALL_STATUS" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 失敗資訊和故障排除
          if [[ "$OVERALL_STATUS" == *"失敗"* ]]; then
            echo "## 🚨 失敗詳情與故障排除" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            if [ "$TERRAFORM_VALIDATION_STATUS" = "failure" ]; then
              echo "### ❌ Terraform 驗證失敗" >> $GITHUB_STEP_SUMMARY
              echo "**可能原因：**" >> $GITHUB_STEP_SUMMARY
              echo "- Terraform 語法錯誤" >> $GITHUB_STEP_SUMMARY
              echo "- 格式不符合規範" >> $GITHUB_STEP_SUMMARY
              echo "- 缺少必要的設定檔案" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "**解決方法：**" >> $GITHUB_STEP_SUMMARY
              echo "1. 在本地執行 \`make tf-fmt\` 修正格式" >> $GITHUB_STEP_SUMMARY
              echo "2. 執行 \`terraform validate\` 檢查語法" >> $GITHUB_STEP_SUMMARY
              echo "3. 檢查 Terraform 設定檔案是否完整" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi

            if [ "$SHARED_INFRA_STATUS" = "failure" ]; then
              echo "### ❌ 共享基礎設施驗證失敗" >> $GITHUB_STEP_SUMMARY
              echo "**可能原因：**" >> $GITHUB_STEP_SUMMARY
              echo "- 共享基礎設施尚未部署" >> $GITHUB_STEP_SUMMARY
              echo "- Terraform state 檔案損壞或不存在" >> $GITHUB_STEP_SUMMARY
              echo "- 權限不足無法存取 GCS bucket" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "**解決方法：**" >> $GITHUB_STEP_SUMMARY
              echo "1. 先確保共享基礎設施已透過 infra-shared.yml 部署" >> $GITHUB_STEP_SUMMARY
              echo "2. 檢查 Terraform backend 設定是否正確" >> $GITHUB_STEP_SUMMARY
              echo "3. 驗證 Service Account 權限" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi

            if [ "$TEST_STATUS" = "failure" ]; then
              echo "### ❌ 測試失敗" >> $GITHUB_STEP_SUMMARY
              echo "**下一步行動：**" >> $GITHUB_STEP_SUMMARY
              echo "1. 查看 [測試結果 artifacts](#) 了解具體失敗原因" >> $GITHUB_STEP_SUMMARY
              echo "2. 在本地執行 \`make test\` 重現問題" >> $GITHUB_STEP_SUMMARY
              echo "3. 檢查測試環境設定和相依性" >> $GITHUB_STEP_SUMMARY
              echo "4. 確認 Google Cloud 認證是否正確設定" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi

            if [ "$DEPLOY_STATUS" = "failure" ]; then
              echo "### ❌ 部署失敗" >> $GITHUB_STEP_SUMMARY
              echo "**下一步行動：**" >> $GITHUB_STEP_SUMMARY
              echo "1. 檢查 Terraform plan 輸出" >> $GITHUB_STEP_SUMMARY
              echo "2. 驗證 GCP 權限和配額" >> $GITHUB_STEP_SUMMARY
              echo "3. 確認所有必要的 secrets 都已設定" >> $GITHUB_STEP_SUMMARY
              echo "4. 檢查是否有資源命名衝突" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi
          fi

          # 成功資訊和後續步驟
          if [[ "$OVERALL_STATUS" == *"成功"* ]]; then
            echo "## 🎉 部署成功" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 📊 部署資訊" >> $GITHUB_STEP_SUMMARY
            echo "- **部署版本**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **部署時間**: $(date +'%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
            echo "- **影響服務**: $SERVICES_COUNT 個服務" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🔗 有用連結" >> $GITHUB_STEP_SUMMARY
            echo "- [Google Cloud Console](https://console.cloud.google.com/functions/list?project=${{ env.PROJECT_ID }})" >> $GITHUB_STEP_SUMMARY
            echo "- [Cloud Logging](https://console.cloud.google.com/logs/query?project=${{ env.PROJECT_ID }})" >> $GITHUB_STEP_SUMMARY
            echo "- [Cloud Monitoring](https://console.cloud.google.com/monitoring?project=${{ env.PROJECT_ID }})" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 📋 後續建議" >> $GITHUB_STEP_SUMMARY
            echo "1. 🔍 檢查服務在 Cloud Console 中的運行狀況" >> $GITHUB_STEP_SUMMARY
            echo "2. 📊 監控相關指標和日誌" >> $GITHUB_STEP_SUMMARY
            echo "3. ✅ 驗證功能是否正常運作" >> $GITHUB_STEP_SUMMARY
            echo "4. 📝 如有需要，更新相關文件" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi

          # 資源連結
          echo "## 🔗 相關資源" >> $GITHUB_STEP_SUMMARY
          echo "| 資源 | 連結 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|------|" >> $GITHUB_STEP_SUMMARY
          echo "| 📊 GitHub Actions 執行記錄 | [查看詳細日誌](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) |" >> $GITHUB_STEP_SUMMARY
          echo "| 🎯 本次變更 | [查看 commit](https://github.com/${{ github.repository }}/commit/${{ github.sha }}) |" >> $GITHUB_STEP_SUMMARY
          echo "| 📁 專案 README | [查看文件](https://github.com/${{ github.repository }}/blob/main/README.md) |" >> $GITHUB_STEP_SUMMARY
          echo "| 🏗️ 開發文件 | [查看 DEVELOPMENT.md](https://github.com/${{ github.repository }}/blob/main/DEVELOPMENT.md) |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # 統計資訊
          echo "## 📈 統計資訊" >> $GITHUB_STEP_SUMMARY
          echo "- **工作流程執行 ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
          echo "- **工作流程編號**: ${{ github.run_number }}" >> $GITHUB_STEP_SUMMARY
          echo "- **執行持續時間**: ${{ github.job }}（完成時間以各 job 實際結束時間為準）" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "---" >> $GITHUB_STEP_SUMMARY
          echo "*📅 報告產生時間: $(date +'%Y-%m-%d %H:%M:%S UTC')*" >> $GITHUB_STEP_SUMMARY
          echo "*🤖 由 Apps CI/CD Pipeline 自動產生*" >> $GITHUB_STEP_SUMMARY
