gcloud config get-value project  # Must show "tagtoo-tracking"
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

# Use shared outputs
locals {
  shared_outputs = data.terraform_remote_state.shared.outputs
  # Access: shared_outputs.service_account_email, shared_outputs.project_id, etc.
}
task_config = {
    "max_attempts": 1,  # Reduced from 5 to prevent duplicates
    "min_backoff": "5s"
}
---
# Integrated Event Platform – AI Coding Agent Instructions

## 🏗️ Architecture & Structure

- **Mono-repo** for GCP-based data integration, event sync, and analytics.
- **Key directories:**
  - `apps/`: Each service (Cloud Run, Cloud Function, BigQuery-only) in its own subfolder, all templated from `apps/_template/`
  - `infrastructure/terraform/shared/`: Shared GCP infra (VPC, IAM, BigQuery, Pub/Sub)
  - `infrastructure/terraform/schema/`: Centralized BigQuery schema definitions
  - `Makefile`: Entry for all build, deploy, schema, and local dev commands
  - `.github/workflows/`: CI/CD, auto-detects changed services

## 🚦 Critical Workflows & Commands

- **GCP config check is mandatory** before any gcloud/terraform:
  - `make check-gcloud-config` (auto-switches to correct project)
- **Service creation:**
  - `make create-service SERVICE_NAME='foo' DESCRIPTION='bar'`
- **Schema management:**
  - `make schema-status`, `make schema-update-dev`, `make schema-validate`
- **Local dev:**
  - `make dev-setup`, `make dev-up` (docker-compose)
- **Deploy & logs:**
  - `make deploy-dev`, `make logs`, `make status`

## 🧩 Patterns & Conventions

- **Commit messages:**
  - 必須使用繁體中文
  - Schema 變更需加 `[SCHEMA]` 標籤
- **Service structure:**
  - `src/main.py` 為進入點
  - `terraform/` 內必須引用 shared remote state
  - `Dockerfile` 需支援 `${GUNICORN_TIMEOUT:-1740}` 等 runtime env
- **BigQuery:**
  - 批次最大 10,000 筆，避免 SSL timeout
  - 僅用 MERGE upsert，嚴禁直接 INSERT
  - Schema 只允許在 `infrastructure/terraform/schema/` 管理
- **Cloud Tasks:**
  - `max_attempts: 1`，避免重複
  - 任務 queue name、payload、重試策略皆有明確 pattern
- **Firestore staging:**
  - 先進 Firestore，後 ETL 進 BigQuery
- **環境變數** 只允許 runtime 注入，不可 build-time 寫死

## 🧪 測試與 Mock 規範

- **所有單元測試必須完全 mock GCP client**（BigQuery, Firestore, Tasks）
- **嚴格區分 unit/integration test**，integration 測試需明確標記且預設不跑
- **測試檔案範例：**
  - `tests/test_main.py`：
    - 使用 pytest fixture `mock_gcp_clients`，patch 所有外部 client
    - app 內部 processor 也要 mock，避免誤觸真實 GCP
    - 測試只驗證業務邏輯，不驗證 infra 連線
- **CI/CD 只允許跑 unit test，integration test 需手動觸發**

## 🔗 Integration Points

- **BigQuery:**
  - 主表：`tagtoo-tracking.event_prod.integrated_event`
  - Schema 只允許 centralized 管理，所有服務引用 remote state outputs
- **Cloud Tasks:**
  - 參考 `legacy-event-sync` 的 queue 設定與 payload 結構
- **跨服務溝通**：嚴格透過 shared outputs 進行 discovery

## ⚠️ Gotchas & Anti-patterns

- **infra 先於服務部署**，否則 remote state 會失敗
- **任何 GCP 操作前必跑 make check-gcloud-config**
- **Schema 變更必須協作，commit message 標註 [SCHEMA]**
- **嚴禁在程式碼中硬編環境參數或 queue 名稱**

## 📁 Key Files & Examples

- `Makefile`：所有開發/部署/驗證指令
- `apps/_template/`：服務 scaffold 樣板
- `infrastructure/terraform/shared/outputs.tf`：跨服務資源
- `DEVELOPMENT_GUIDELINES.md`：團隊協作與 AI agent 行為規範
- `tests/test_main.py`：mock、測試分層、CI/CD 樣板
- `.github/workflows/apps-ci-cd.yml`：CI/CD pipeline
