---
mode: ask
---

# 🚀 開發守則規範 (41 條)

## 🔧 Git 相關規範 (規則 1-4)

- **1.** git add 之前先用 `git status` 檢查，不要直接 `git add -A`
- **2.** git commit message 撰寫請只看已經被 git add 的部分 (git stage area)
- **3.** 使用格式：`[type](scope): description`，基於 conventional commits 修改
- **4.** commit message 的標題要有重點，內容請用條列式的方式列出

## 🔄 開發流程規範 (規則 5-9)

- **5.** 任務完成時呼叫 MCP interactive_feedback
- **6.** 程式開發時使用 Container 建立開發環境（編譯、測試、執行程式）
- **7.** 如果有 Makefile 的話優先檢視並使用
- **8.** 先從打造一個可行的 MVP 開始，一步一步增加功能和測試
- **9.** 以 Test Driven Development 的方式來開發

## 📋 計畫文檔規範 (規則 10-13)

- **10.** 開發計畫統一記錄在專案根目錄的 `DEVELOPMENT.md` 中
- **11.** 在確認要修改程式碼之前，先討論並確認完整的計畫
- **12.** 討論時先提出最簡單的解法
- **13.** README.md 程式碼部分維持原文即便是註解也一樣

## 💻 程式碼品質規範 (規則 14-18)

- **14.** **DO NOT REMOVE MY COMMENTS** 不要隨便移除註解
- **15.** 修改 Python 程式碼時務必遵守 PEP8（預設使用 python3）
- **16.** 重構程式碼時不要隨意移除原本的東西，要移除時要說明原因
- **17.** 程式碼一律使用英文，但註解可以用中文
- **18.** 注重程式碼和架構的可讀性、易用性、擴展性

## 🛠️ 工具使用規範 (規則 19-22)

- **19.** 規劃時可以使用 sequentialthinking MCP
- **20.** filesystem MCP 使用絕對路徑，避免權限問題
- **21.** 讀取檔案用 filesystem MCP，寫入修改用 Cursor 內建 tool
- **22.** 主動告知資訊最後更新日期和版本

## 🧪 測試策略規範 (規則 23-27)

- **23.** **核心功能優先**：關鍵業務邏輯必須有測試，覆蓋率目標 80%+
- **24.** **測試金字塔**：單元測試 70%、整合測試 20%、端對端測試 10%
- **25.** **測試隔離**：所有測試不影響正式資料，使用 Mock 處理外部連結
- **26.** **簡單變更豁免**：純文檔修改、樣式調整可跳過測試
- **27.** **漸進式要求**：新專案從簡單測試開始，隨專案成熟度提升要求

## 🔐 安全性規範 (規則 28-30)

- **28.** 不要在程式碼中硬編碼敏感資訊（API 金鑰、密碼、連線字串）
- **29.** 使用環境變數或安全的配置管理系統
- **30.** API 認證資訊透過安全方式傳遞和儲存

## ⚡ 效能與成本規範 (規則 31-33)

- **31.** 使用 API 時盡量使用 batch 操作，避免過多 API quota
- **32.** **BigQuery 成本控制**：查詢前必須估算查詢量和價錢
  - 單次查詢超過 $10 USD 需確認
  - 日查詢總量超過 $50 USD 需檢查
  - 使用 `--dry_run` 參數預估成本
- **33.** BigQuery 避免使用迴圈，使用 CROSS JOIN 產生日期序列一次處理所有日期

## 🐛 錯誤處理規範 (規則 34-35)

- **34.** 統一使用結構化日誌記錄：`DEBUG/INFO/WARNING/ERROR/CRITICAL`
- **35.** 錯誤訊息要包含足夠的上下文資訊便於調試

## 📊 程式碼審查規範 (規則 36-38)

- **36.** **自我檢查**：
  - 一般函數 100 行內
  - 業務邏輯函數 200 行內
  - 複雜演算法函數 300 行內，但需詳細註解
  - 巢狀層級不超過 4 層
- **37.** **命名清晰**：變數、函數、類別名稱要能表達其用途
- **38.** **重構時機**：發現重複程式碼、函數過長、邏輯複雜時主動重構

## 🌐 國際化與文檔規範 (規則 39-41)

- **39.** Always respond in 台灣正體中文
- **40.** 修改程式碼後必須同時更新相關註解、文件、流程圖等文檔
- **41.** 修改程式碼的時候也順便看看有沒有修改部分的測試，請一併修改

---

## 📝 文檔分工

**README.md（使用者導向）**：背景說明、功能簡介、安裝方式、使用說明、配置說明
**DEVELOPMENT.md（開發者導向）**：當前狀態、需求規格、技術棧、開發里程碑、開發環境設定

---

_最後更新：2025-06-19_
_適用於所有軟體開發專案_
