# BigQuery Only 服務模板

此模板適用於純 BigQuery 資料處理任務，例如：

- 定期資料聚合
- 資料品質檢查
- 報表生成
- 資料轉換任務

## 📁 結構說明

```
bigquery_only/
├── sql/                   # SQL 查詢檔案
│   └── daily_aggregation.sql
├── terraform/             # 基礎設施配置
│   ├── main.tf
│   └── variables.tf
└── README.md             # 此文件
```

## 🚀 使用說明

1. **複製此模板**：

   ```bash
   # 從專案根目錄
   python scripts/create-service.py --service-name your-service-name --service-type bigquery_only --description "Your service description"
   ```

2. **開發 SQL 查詢**：

   ```bash
   cd apps/your-service-name
   # 編輯 sql/ 目錄下的 SQL 檔案
   ```

3. **部署**：
   ```bash
   # 自動納入 CI/CD 流程，只要具備 Makefile 和 terraform/ 目錄
   make deploy-dev
   ```

## 🔧 配置要點

### BigQuery 配置

- 使用 Scheduled Queries 定期執行
- 支援參數化查詢
- 自動分區和叢集化

### Cloud Scheduler 配置

- 支援 cron 表達式
- 配置重試策略
- 錯誤通知機制

### 資料管理

- 自動資料保留策略
- 成本控制和配額管理
- 資料加密和存取控制

## 📊 監控

- 查詢執行日誌
- 成本監控和警報
- 資料品質指標

## 💡 最佳實踐

1. **效能優化**：適當使用分區和叢集化
2. **成本控制**：避免全表掃描，使用查詢限制
3. **資料品質**：實作資料驗證和品質檢查
4. **安全性**：使用列級和行級安全控制

更多詳細資訊請參考 `../common/README.md`。

## 🚀 快速開始

### SQL 開發

1. 在 `sql/` 目錄下建立查詢檔案
2. 使用 BigQuery 變數進行參數化：

```sql
SELECT
  DATE(created_at) as date,
  COUNT(*) as total_events
FROM `{project_id}.integrated_event.events`
WHERE DATE(created_at) = DATE('{execution_date}')
GROUP BY DATE(created_at)
```

### 測試查詢

1. 驗證 SQL 語法：

```bash
python scripts/validate_sql.py sql/
```

2. 執行測試查詢：

```bash
python scripts/test_queries.py --environment=dev
```

### 部署

1. 開發環境部署：

```bash
cd terraform
terraform init
terraform plan -var="environment=dev"
terraform apply -var="environment=dev"
```

2. 生產環境部署：

```bash
terraform plan -var="environment=prod"
terraform apply -var="environment=prod"
```

## 📊 SQL 最佳實踐

### 1. 參數化查詢

使用參數而非硬編碼值：

```sql
-- ✅ 好的做法
WHERE DATE(timestamp) = DATE('{execution_date}')

-- ❌ 避免
WHERE DATE(timestamp) = '2025-01-01'
```

### 2. 分割表格優化

利用分割欄位提升效能：

```sql
-- ✅ 使用分割欄位
WHERE DATE(timestamp) BETWEEN '2025-01-01' AND '2025-01-31'

-- ❌ 掃描全表
WHERE EXTRACT(YEAR FROM timestamp) = 2025
```

### 3. 成本控制

限制查詢範圍：

```sql
-- 限制查詢資料量
WHERE DATE(timestamp) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
```

## 🔧 配置說明

主要配置檔案：

- `sql/`: SQL 查詢檔案
- `terraform/`: 基礎設施定義
- `scripts/`: 輔助工具

## 📚 相關文檔

- [BigQuery 文檔](https://cloud.google.com/bigquery/docs)
- [SQL 最佳實踐](docs/SQL_BEST_PRACTICES.md)
- [排程指南](docs/SCHEDULING.md)
