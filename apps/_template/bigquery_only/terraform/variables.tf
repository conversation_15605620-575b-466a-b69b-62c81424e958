# {{SERVICE_NAME}} Terraform 變數

variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
  default     = "tagtoo-tracking"
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod。"
  }
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "{{SERVICE_NAME_KEBAB}}"
}

# 排程配置
variable "schedule_expression" {
  description = "排程表達式 (cron 格式)"
  type        = string
  default     = "0 2 * * *" # 每天凌晨 2 點執行
}

variable "schedule_timezone" {
  description = "排程時區"
  type        = string
  default     = "Asia/Taipei"
}
