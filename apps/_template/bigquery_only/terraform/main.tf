# {{SERVICE_NAME}} Terraform 配置
# BigQuery Only 服務部署

terraform {
  required_version = ">= 1.12.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-terraform-state"
    prefix = "integrated-event/{{SERVICE_NAME_KEBAB}}"
  }
}

# 本地變數
locals {
  service_full_name = "${var.service_name}-${var.environment}"

  common_labels = {
    app         = var.service_name
    environment = var.environment
    managed_by  = "terraform"
    team        = "data-team"
  }
}

# 資料來源
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-terraform-state"
    prefix = "integrated-event/shared"
  }
}

data "google_project" "current" {
  project_id = var.project_id
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# 讀取 SQL 查詢檔案
locals {
  daily_aggregation_sql = templatefile("${path.module}/../sql/daily_aggregation.sql", {
    project_id     = var.project_id
    execution_date = "$${DS}" # Data Transfer 參數
  })
}

# 使用 BigQuery Scheduler 模組
module "bigquery_scheduler" {
  source = "../../../../infrastructure/terraform/modules/bigquery-scheduler"

  service_name          = local.service_full_name
  project_id            = var.project_id
  region                = var.region
  environment           = var.environment
  service_account_email = data.terraform_remote_state.shared.outputs.integrated_event_sa_email

  # BigQuery 配置
  query               = local.daily_aggregation_sql
  destination_dataset = "integrated_event"
  destination_table   = "${var.service_name}_daily_summary"

  # 排程配置
  schedule_expression = var.schedule_expression
  schedule_timezone   = var.schedule_timezone

  # 資料設定
  write_disposition  = "WRITE_TRUNCATE"
  partitioning_field = "date"
  clustering_fields  = ["service_type", "environment"]

  # 監控配置
  enable_monitoring = true
  notification_channels = [
    data.terraform_remote_state.shared.outputs.email_notification_channel
  ]

  # 使用 BigQuery Data Transfer (推薦)
  enable_data_transfer = true
  enable_scheduler     = false

  labels = local.common_labels
}

# 輸出
output "data_transfer_config_name" {
  description = "BigQuery Data Transfer 配置名稱"
  value       = module.bigquery_scheduler.data_transfer_config_name
}

output "destination_table" {
  description = "目標資料表"
  value       = "${var.project_id}.integrated_event.${var.service_name}_daily_summary"
}

output "schedule_expression" {
  description = "排程表達式"
  value       = var.schedule_expression
}

output "security_status" {
  description = "安全狀態檢查"
  value = {
    service_account_configured = data.terraform_remote_state.shared.outputs.integrated_event_sa_email != ""
    monitoring_enabled         = true
    data_transfer_secured      = "✅ 使用服務帳號執行 BigQuery 任務"
  }
}
