-- {{SERVICE_NAME}} 每日聚合查詢
-- 此查詢用於生成每日統計數據

DECLARE execution_date DATE DEFAULT DATE('{execution_date}');

-- 建立或替換聚合表
CREATE OR REPLACE TABLE `{project_id}.integrated_event.{{SERVICE_NAME_SNAKE}}_daily_summary`
PARTITION BY date
CLUSTER BY service_type, environment
AS

WITH daily_events AS (
  SELECT
    DATE(timestamp) as date,
    service_type,
    environment,
    COUNT(*) as total_events,
    COUNT(DISTINCT user_id) as unique_users,
    COUNTIF(status = 'success') as successful_events,
    COUNTIF(status = 'error') as failed_events,
    AVG(processing_time_ms) as avg_processing_time,
    MAX(processing_time_ms) as max_processing_time
  FROM `{project_id}.integrated_event.events`
  WHERE DATE(timestamp) = execution_date
    AND service_name = '{{SERVICE_NAME_KEBAB}}'
  GROUP BY
    DATE(timestamp),
    service_type,
    environment
),

quality_metrics AS (
  SELECT
    date,
    service_type,
    environment,
    SAFE_DIVIDE(successful_events, total_events) * 100 as success_rate,
    SAFE_DIVIDE(failed_events, total_events) * 100 as error_rate
  FROM daily_events
)

SELECT
  e.date,
  e.service_type,
  e.environment,
  e.total_events,
  e.unique_users,
  e.successful_events,
  e.failed_events,
  e.avg_processing_time,
  e.max_processing_time,
  q.success_rate,
  q.error_rate,
  CURRENT_TIMESTAMP() as created_at
FROM daily_events e
JOIN quality_metrics q USING (date, service_type, environment)
ORDER BY date DESC, service_type, environment;
