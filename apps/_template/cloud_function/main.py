"""
{{SERVICE_NAME}} Cloud Function

這是一個 Google Cloud Function，用於處理定期任務或事件驅動的短時間任務。
"""

import json
import logging
from typing import Any, Dict

import functions_framework
from google.cloud import firestore, bigquery
from flask import Request

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化 Google Cloud 客戶端
db = firestore.Client()
bq_client = bigquery.Client()


@functions_framework.http
def main(request: Request) -> str:
    """
    Cloud Function 主要入口點

    Args:
        request: HTTP 請求物件

    Returns:
        str: 回應訊息
    """
    try:
        # 解析請求資料
        request_data = _parse_request(request)

        # 執行主要業務邏輯
        result = _process_data(request_data)

        # 回傳成功回應
        return json.dumps({
            'status': 'success',
            'message': '{{SERVICE_NAME}} 執行完成',
            'result': result
        })

    except Exception as e:
        logger.error(f"{{SERVICE_NAME}} 執行失敗: {str(e)}")
        return json.dumps({
            'status': 'error',
            'message': str(e)
        }), 500


def _parse_request(request: Request) -> Dict[str, Any]:
    """
    解析 HTTP 請求

    Args:
        request: HTTP 請求物件

    Returns:
        Dict[str, Any]: 解析後的請求資料
    """
    if request.method == 'POST':
        if request.is_json:
            return request.get_json(silent=True) or {}
        else:
            return dict(request.form)
    else:
        return dict(request.args)


def _process_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    處理主要業務邏輯

    Args:
        data: 輸入資料

    Returns:
        Dict[str, Any]: 處理結果
    """
    logger.info(f"開始處理資料: {data}")

    # TODO: 實作業務邏輯
    # 例如：
    # 1. 從 Firestore 讀取配置
    # 2. 處理資料
    # 3. 寫入 BigQuery
    # 4. 更新狀態

    # 範例：寫入資料到 BigQuery
    table_id = "integrated_event.{{SERVICE_NAME_SNAKE}}_events"

    rows_to_insert = [
        {
            "timestamp": firestore.SERVER_TIMESTAMP,
            "service": "{{SERVICE_NAME}}",
            "status": "processed",
            "data": json.dumps(data)
        }
    ]

    errors = bq_client.insert_rows_json(table_id, rows_to_insert)
    if errors:
        raise Exception(f"BigQuery 寫入失敗: {errors}")

    logger.info("資料處理完成")

    return {
        "processed_records": len(rows_to_insert),
        "table": table_id
    }


# 用於本地測試的健康檢查端點
@functions_framework.http
def health(request: Request) -> str:
    """健康檢查端點"""
    return json.dumps({"status": "healthy", "service": "{{SERVICE_NAME}}"})
