# Cloud Function 服務模板

此模板適用於事件驅動的短時間任務，例如：

- 定期數據處理
- 檔案處理觸發器
- 事件響應處理
- 簡單的 API 端點

## 📁 結構說明

```
cloud_function/
├── main.py                # 主要 Cloud Function 程式碼
├── requirements.txt       # Python 依賴
├── README.md             # 此文件
└── terraform/            # 基礎設施配置
    ├── main.tf
    └── variables.tf
```

## 🚀 使用說明

1. **複製此模板**：

   ```bash
   # 從專案根目錄
   python scripts/create-service.py --service-name your-service-name --service-type cloud_function --description "Your service description"
   ```

2. **本地開發**：

   ```bash
   cd apps/your-service-name
   pip install -r requirements.txt
   python main.py  # 本地測試
   ```

3. **部署**：
   ```bash
   # 自動納入 CI/CD 流程，只要具備 Makefile 和 terraform/ 目錄
   make deploy-dev
   ```

## 🔧 配置要點

### Cloud Function 配置

- Runtime: Python 3.11
- 記憶體：256MB-8GB
- 超時時間：最多 540 秒
- 支援 VPC 連接

### 觸發器支援

- HTTP 觸發器
- Cloud Scheduler
- Cloud Storage 事件
- Pub/Sub 訊息

### 環境變數

- 使用 terraform 設定環境變數
- 敏感資訊使用 Secret Manager

## 📊 監控

- 執行日誌自動收集到 Cloud Logging
- 自動配置 Cloud Monitoring 指標
- 支援 Cloud Trace 追蹤

## 💡 最佳實踐

1. **冷啟動優化**：避免在全域範圍初始化重量級物件
2. **錯誤處理**：適當的異常處理和重試機制
3. **資源管理**：合理設定記憶體和超時時間
4. **安全性**：使用 IAM 和 service account 最小權限原則

更多詳細資訊請參考 `../common/README.md`。

4. 本地測試 Function：

```bash
functions-framework --target=main --debug
```

### 部署

1. 開發環境部署：

```bash
cd terraform
terraform init
terraform plan -var="environment=dev"
terraform apply -var="environment=dev"
```

2. 生產環境部署：

```bash
terraform plan -var="environment=prod"
terraform apply -var="environment=prod"
```

## 📊 監控

- **Cloud Console**: 查看 Function 執行日誌
- **Monitoring**: 檢查錯誤率和執行時間
- **Alerts**: 自動錯誤通知

## 🔧 配置說明

主要配置檔案：

- `main.py`: Function 入口點
- `config.py`: 配置管理
- `terraform/`: 基礎設施定義
- `requirements.txt`: Python 依賴

## 📚 相關文檔

- [Cloud Functions 文檔](https://cloud.google.com/functions/docs)
- [部署指南](docs/DEPLOYMENT.md)
- [開發指南](docs/DEVELOPMENT.md)
