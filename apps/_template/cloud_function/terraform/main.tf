# {{SERVICE_NAME}} Terraform 配置
# Cloud Functions 服務部署

terraform {
  required_version = ">= 1.12.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/{{SERVICE_NAME_KEBAB}}"
  }
}

# 本地變數
locals {
  service_full_name = "${var.service_name}-${var.environment}"

  common_labels = {
    app         = var.service_name
    environment = var.environment
    managed_by  = "terraform"
    team        = "data-team"
  }
}

# 引用共用基礎設施的資料
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

# 本地變數 - 使用共用資源
locals {
  shared_outputs = data.terraform_remote_state.shared.outputs
}

provider "google" {
  project = local.shared_outputs.project_id
  region  = local.shared_outputs.region
}

# 使用 Cloud Function 模組
module "cloud_function" {
  source = "../../../../infrastructure/terraform/modules/cloud-function"

  service_name          = local.service_full_name
  project_id            = local.shared_outputs.project_id
  region                = local.shared_outputs.region
  environment           = var.environment
  service_account_email = local.shared_outputs.service_account_email

  # Function 配置
  function_source_dir = "../"
  entry_point         = "main"
  runtime             = "python311"
  timeout             = var.function_timeout
  memory_mb           = var.function_memory

  # 環境變數
  env_vars = {
    ENVIRONMENT      = var.environment
    PROJECT_ID       = local.shared_outputs.project_id
    BIGQUERY_DATASET = "integrated_event"
    SERVICE_NAME     = var.service_name
  }

  # 觸發器配置
  enable_scheduler    = true
  schedule_expression = var.schedule_expression
  schedule_timezone   = var.schedule_timezone

  # 監控配置
  enable_monitoring = true
  notification_channels = [
    local.shared_outputs.email_notification_channel
  ]

  labels = local.common_labels
}

# 輸出
output "function_name" {
  description = "Cloud Function 名稱"
  value       = module.cloud_function.function_name
}

output "function_url" {
  description = "Cloud Function URL"
  value       = module.cloud_function.function_url
}

output "scheduler_job_name" {
  description = "Cloud Scheduler 任務名稱"
  value       = module.cloud_function.scheduler_job_name
}

output "security_status" {
  description = "安全狀態檢查"
  value = {
    service_account_configured = local.shared_outputs.service_account_email != ""
    monitoring_enabled         = true
    scheduler_auth             = "✅ Cloud Scheduler 使用 OIDC 驗證"
  }
}
