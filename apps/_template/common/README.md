# {{SERVICE_NAME}} - {{DESCRIPTION}}

> 此服務用於整合 {{SERVICE_NAME}} 資料到 integrated_event BigQuery 表格

## 📋 專案概述

此服務負責接收和處理來自 {{SERVICE_NAME}} 的事件資料，並將其標準化後儲存到 Firestore 和 BigQuery 中。

### 🎯 主要功能

- ✅ 接收 webhook 事件資料
- ✅ 資料驗證和標準化處理
- ✅ 儲存到 Firestore 進行緩衝
- ✅ 批次同步到 BigQuery
- ✅ 健康檢查和監控
- ✅ 錯誤處理和重試機制

## 🏗️ 技術架構

```
{{SERVICE_NAME}} → Webhook → Flask App → Firestore → BigQuery
                                ↓
                        Cloud Scheduler (定期同步)
```

### 技術堆疊

- **語言**: Python 3.11
- **框架**: Flask + Gunicorn
- **資料庫**: Google Firestore, BigQuery
- **部署**: Cloud Run
- **監控**: Google Cloud Monitoring
- **CI/CD**: GitHub Actions

## 🚀 快速開始

### 前置需求

- Python 3.11+
- Docker Desktop
- Google Cloud CLI
- Terraform

### 🛠️ 使用此模板建立新服務

**重要**: 此資料夾是模板，請勿直接修改！請按照以下步驟建立新服務：

#### 方法一：手動建立 (基本方式)

1. **複製模板資料夾**:

   ```bash
   cp -r apps/_template apps/your-service-name
   cd apps/your-service-name
   ```

2. **替換模板變數**:
   搜尋並替換所有 `{{placeholder}}` 變數：

   - `{{SERVICE_NAME}}`: 服務顯示名稱 (例如: "Shopify Webhook")
   - `{{SERVICE_NAME_KEBAB}}`: kebab-case 服務名稱 (例如: "shopify-webhook")
   - `{{SERVICE_NAME_PASCAL}}`: PascalCase 類別名稱 (例如: "ShopifyWebhook")
   - `{{DESCRIPTION}}`: 服務描述

3. **配置 CI/CD 自動化**:
   新服務會自動被納入 CI/CD 流程，只要：

   - 具備 `Makefile` (包含標準 make targets)
   - 具備 `terraform/` 目錄 (包含正確的 Terraform 配置)

4. **設定環境變數**:
   ```bash
   cp .env.example .env
   # 編輯 .env 檔案填入實際值
   ```

#### 方法二：自動化腳本 (推薦)

```bash
# 從專案根目錄執行
python scripts/create-service.py --service-name your-service-name --service-type cloud_run --description "Your service description"

# 支援的服務類型：cloud_run, cloud_function, bigquery_only
```

**注意**: 新服務會自動納入 CI/CD 流程，無需手動設定 GitHub Actions。

### 本地開發

1. **設定 pre-commit hooks (推薦)**:

   ```bash
   # 一鍵設定自動程式碼檢查
   make pre-commit-setup
   ```

2. **建立開發環境**:

   ```bash
   # 建立 .env 檔案
   cp .env.example .env

   # 啟動所有服務 (包含模擬器)
   docker-compose up -d --build
   ```

3. **運行測試**:

   ```bash
   # 在容器內運行測試
   docker-compose exec {{SERVICE_NAME_KEBAB}} pytest

   # 或查看測試覆蓋率
   docker-compose exec {{SERVICE_NAME_KEBAB}} pytest --cov=src
   ```

4. **查看日誌**:

   ```bash
   docker-compose logs -f {{SERVICE_NAME_KEBAB}}
   ```

5. **停止服務**:
   ```bash
   docker-compose down
   ```

### 🔍 程式碼品質檢查

此專案實作了多層級的驗證機制，確保程式碼品質：

#### Pre-commit Hooks (推薦)

```bash
# 設定自動檢查
make pre-commit-setup

# 手動執行檢查
make pre-commit-run
```

每次 `git commit` 時會自動執行：

- 🎨 程式碼格式化 (black, isort)
- 🔍 程式碼品質檢查 (flake8)
- 🏗️ Terraform 格式化和驗證
- 📝 JSON/YAML 格式檢查

#### Terraform 驗證

```bash
# 完整 Terraform 檢查 (推薦)
make tf-check

# 個別檢查項目
make tf-init      # 初始化
make tf-fmt       # 格式化
make tf-validate  # 語法驗證
make tf-plan      # 計畫檢查
```

#### 手動檢查

```bash
# 程式碼品質
make lint         # 程式碼檢查
make format       # 程式碼格式化
make type-check   # 型別檢查

# 完整測試
make test         # 所有測試
make test-unit    # 單元測試
make test-integration  # 整合測試
```

💡 **提示**: 建議先執行 `make tf-check` 和 `make test` 確保一切正常，再提交程式碼。

## 📝 API 文檔

### 端點

| 端點       | 方法 | 描述              |
| ---------- | ---- | ----------------- |
| `/health`  | GET  | 健康檢查          |
| `/webhook` | POST | 接收 webhook 事件 |
| `/sync`    | POST | 手動觸發同步      |

### Webhook 資料格式

```json
{
  "ec_id": "客戶 ID",
  "user_id": "使用者 ID",
  "email": "<EMAIL>",
  "event_type": "purchase",
  "event_name": "purchase_completed",
  "page_url": "https://example.com/page",
  "items": [
    {
      "item_id": "product_123",
      "item_name": "Product Name",
      "price": 99.99,
      "quantity": 1
    }
  ],
  "custom_parameters": {
    "campaign": "summer_sale"
  }
}
```

## 🔧 配置說明

### 環境變數

參考 `.env.example` 檔案，主要配置項目：

- `PROJECT_ID`: GCP 專案 ID
- `ENVIRONMENT`: 環境 (dev/staging/prod)
- `FIRESTORE_DATABASE`: Firestore 資料庫名稱
- `BIGQUERY_DATASET`: BigQuery 資料集名稱

### Firestore 集合結構

```
{{SERVICE_NAME_KEBAB}}-{environment}/
├── document_id_1
│   ├── event_time: timestamp
│   ├── partner_source: string
│   ├── user: object
│   ├── event_type: string
│   ├── synced_to_bq: boolean
│   └── raw_data: object
```

## 🚀 部署

💡 **重要**: 部署前會自動執行 Terraform 檢查，確保基礎設施配置正確。

### 開發環境

```bash
# 自動執行 tf-check + 部署
make deploy-dev
```

### 生產環境

```bash
# 自動執行 tf-check + 生成計畫（需要手動確認）
make deploy-prod
```

### 手動 Terraform 部署

```bash
# 執行完整檢查
make tf-check

# 手動部署
cd terraform
terraform init
terraform plan -var="environment=dev"
terraform apply -var="environment=dev"
```

### CI/CD 流程

GitHub Actions 會按以下順序執行：

```
1. 🔍 terraform-validation  ← 早期 Terraform 驗證（快速失敗）
2. 🎨 code-quality         ← 程式碼品質檢查
3. 🧪 unit-tests          ← 單元測試
4. 🔗 integration-tests   ← 整合測試
5. 🏗️ build               ← 建置 Docker 映像
6. 🚀 deploy              ← 部署到 GCP
```

**優勢**:

- ⚡ **快速失敗**: Terraform 錯誤在 2-3 分鐘內發現
- 💰 **節省成本**: 避免不必要的測試和建置
- 🔄 **並行處理**: 多服務同時驗證

## 🧪 測試

### 測試架構

```
tests/
├── unit/          # 單元測試
├── integration/   # 整合測試
└── e2e/          # 端到端測試
```

### 測試標記

- `@pytest.mark.unit`: 單元測試
- `@pytest.mark.integration`: 整合測試
- `@pytest.mark.slow`: 執行時間較長的測試
- `@pytest.mark.external`: 需要外部服務的測試

### 測試覆蓋率

目標覆蓋率: 80%

```bash
pytest --cov=src --cov-report=html
```

## 📊 監控

### 健康檢查

- 端點: `/health`
- 監控頻率: 每分鐘
- 超時時間: 10 秒

### 關鍵指標

- 請求延遲 (P95 < 500ms)
- 錯誤率 (< 1%)
- 同步成功率 (> 99%)
- Firestore 寫入成功率 (> 99.9%)

### 日誌

使用 Google Cloud Logging，搜尋標籤：

```
resource.type="cloud_run_revision"
resource.labels.service_name="{{SERVICE_NAME_KEBAB}}-{environment}"
```

## 🔍 故障排除

### 常見問題

1. **Webhook 接收失敗**

   - 檢查 Content-Type 是否為 `application/json`
   - 驗證請求體格式
   - 查看服務日誌

2. **Firestore 寫入失敗**

   - 檢查服務帳戶權限
   - 驗證 Firestore 資料庫是否存在
   - 檢查網路連線

3. **BigQuery 同步失敗**
   - 檢查資料格式是否符合 schema
   - 驗證 BigQuery 權限
   - 檢查配額使用情況

### 除錯命令

```bash
# 查看服務日誌
make logs

# 檢查服務狀態
make status

# 本地除錯
docker-compose logs {{SERVICE_NAME_KEBAB}}
```

## 📋 開發檢查清單

### 🏁 初始設定

- [ ] 從模板建立新服務資料夾 (選擇適當的服務類型)
- [ ] 替換所有模板變數 (`{{placeholder}}`)
- [ ] 確認具備 `Makefile` 和 `terraform/` 目錄 (CI/CD 自動整合)
- [ ] 設定 `.env` 檔案
- [ ] 更新此 README.md 檔案

### 🔧 開發階段

- [ ] 設定 pre-commit hooks (`make pre-commit-setup`)
- [ ] 實作 `process_event` 方法的業務邏輯
- [ ] 根據實際需求調整資料格式
- [ ] 撰寫單元測試 (覆蓋率 > 80%)
- [ ] 撰寫整合測試
- [ ] Terraform 檢查通過 (`make tf-check`)
- [ ] 本地測試通過 (`make test`)
- [ ] 程式碼品質檢查通過 (`make lint`)
- [ ] Pre-commit 檢查通過 (`make pre-commit-run`)

### 🏗️ 基礎設施

- [ ] 設定 Terraform 變數
- [ ] Terraform 語法驗證通過 (`make tf-validate`)
- [ ] Terraform 計畫檢查通過 (`make tf-plan`)
- [ ] 建立 Cloud Run 服務
- [ ] 設定 IAM 權限
- [ ] 配置 Cloud Scheduler
- [ ] 設定監控和告警

### 🚀 部署階段

- [ ] 開發環境部署成功
- [ ] 健康檢查通過
- [ ] 端到端測試通過
- [ ] 生產環境部署
- [ ] 監控告警設定完成

### 📝 文檔和維護

- [ ] 更新 API 文檔
- [ ] 撰寫操作手冊
- [ ] 設定日誌保留政策
- [ ] 建立故障排除指南
- [ ] 團隊 handover 完成

## 🤝 團隊協作

### 負責範圍

- **應用開發團隊**: 實作 `src/` 和 `tests/` 的內容
- **DevOps 團隊**: 負責 `terraform/` 和跨服務整合

### 開發流程

1. 建立 feature branch
2. 實作功能和測試
3. 提交 Pull Request
4. Code Review
5. 合併到 develop
6. 部署到開發環境測試
7. 合併到 main 部署生產環境

### Git 提交規範

使用 Conventional Commits：

```
feat({{SERVICE_NAME_KEBAB}}): 新增 webhook 驗證功能
fix({{SERVICE_NAME_KEBAB}}): 修復 BigQuery 同步錯誤
docs({{SERVICE_NAME_KEBAB}}): 更新 API 文檔
test({{SERVICE_NAME_KEBAB}}): 新增整合測試
```

## 📞 支援

**維護團隊**: <EMAIL>
**最後更新**: {{DATE}}
**狀態**: 🏗️ 開發中

---

📚 更多資訊請參考 [專案 Wiki](https://github.com/tagtoo/integrated-event/wiki)
