# {{SERVICE_NAME}} flake8 配置

[flake8]
# 最大行長度
max-line-length = 88

# 排除的檔案和目錄
exclude =
    .git,
    __pycache__,
    .pytest_cache,
    .tox,
    venv,
    env,
    build,
    dist,
    *.egg-info,
    .venv,
    terraform/

# 忽略的錯誤代碼
ignore =
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501

# 每個檔案的最大複雜度
max-complexity = 10

# 導入順序檢查
import-order-style = google
application-import-names = src

# 文檔字串檢查
docstring-convention = google

# 選擇的錯誤和警告
select =
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity
    B,  # flake8-bugbear
    I,  # isort

# 統計
statistics = True
count = True
