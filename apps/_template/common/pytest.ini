# {{SERVICE_NAME}} pytest 配置

[tool:pytest]
# 測試目錄
testpaths = tests

# 測試檔案模式
python_files = test_*.py *_test.py

# 測試類別模式
python_classes = Test*

# 測試函數模式
python_functions = test_*

# 最小版本
minversion = 7.0

# 額外選項
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10

# 標記定義
markers =
    unit: 單元測試
    integration: 整合測試
    e2e: 端到端測試
    slow: 執行時間較長的測試
    external: 需要外部服務的測試
    mock: 使用 mock 的測試

# 過濾警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 測試發現
norecursedirs =
    .git
    .tox
    dist
    build
    *.egg
    venv
    env
