# {{SERVICE_NAME}} 環境變數配置範例
# 複製此檔案為 .env 並填入實際值

# ====================================
# 基本服務配置
# ====================================
SERVICE_NAME={{SERVICE_NAME_KEBAB}}
ENVIRONMENT=dev
DEBUG=true
LOG_LEVEL=INFO
PORT=8080

# ====================================
# Google Cloud 配置
# ====================================
PROJECT_ID=tagtoo-tracking
BIGQUERY_DATASET=event_test
FIRESTORE_DATABASE=(default)

# 本地模擬器連線（開發環境自動設定）
FIRESTORE_EMULATOR_HOST=firestore:8080
BIGQUERY_EMULATOR_HOST=bigquery:9050

# ====================================
# 資料來源配置（根據服務調整）
# ====================================
SOURCE_TABLE=
TARGET_TABLE=

# ====================================
# 處理配置
# ====================================
BATCH_SIZE=1000
SYNC_INTERVAL_MINUTES=5
MAX_RETRIES=3
RETRY_DELAY_SECONDS=5

# ====================================
# 分時段處理配置（如需要）
# ====================================
HOURS_PER_SEGMENT=4
MAX_CONCURRENT_SEGMENTS=6

# ====================================
# Cloud Function 資源配置（如需要）
# ====================================
MEMORY_MB=2048
CPU_COUNT=2

# ====================================
# 安全配置
# ====================================
SECRET_KEY=dev-secret-key-change-in-production
API_KEY=
WEBHOOK_SECRET=

# ====================================
# 監控配置
# ====================================
ENABLE_METRICS=true
METRICS_PORT=9090
