# =============================================================================
# 版本管理配置檔案模板
# =============================================================================
#
# 用途：自訂服務專屬的版本發佈行為
# 位置：apps/[service-name]/release-config.sh
# 說明：此檔案為可選，未設定時將使用預設值
#
# =============================================================================

# === 基本設定 ===
SERVICE_NAME="my-service"                        # 服務名稱（建議修改為實際服務名稱）
VERSION_FILE="pyproject.toml"                    # 版本號來源檔案
VERSION_PATTERN='version = "([^"]+)"'            # 版本號擷取規則
DOCKER_REGISTRY="gcr.io/tagtoo-tracking"         # Docker Registry URL

# === 驗證設定 ===
REQUIRE_CHANGELOG=false                          # 是否檢查 CHANGELOG.md 更新
REQUIRE_TESTS=false                              # 是否要求測試通過
REQUIRE_CLEAN_WORKING_DIR=true                   # 是否要求工作目錄乾淨

# === Git 設定 ===
GIT_REMOTE="origin"                              # Git remote 名稱
DEFAULT_BRANCH="main"                            # 預設分支名稱

# === Terraform 設定 ===
TERRAFORM_DIR="terraform"                        # Terraform 目錄位置
TERRAFORM_VARS_FILE=""                           # 變數檔案路徑（可選，例如：terraform.tfvars）

# === 客製化驗證鉤子 ===
# 發版前會依序執行這些指令，任一失敗則停止發版
CUSTOM_PRE_RELEASE_VALIDATIONS=(
    # "make test"                                # 執行測試
    # "make lint"                                # 程式碼檢查
    # "terraform fmt -check"                     # Terraform 格式檢查
    # "./scripts/validate-config.sh"            # 自訂配置驗證
)

# === 客製化後續動作 ===
# 發版完成後會執行這些指令（失敗不影響發版結果）
CUSTOM_POST_RELEASE_ACTIONS=(
    # "echo 'Notifying team...'"                # 範例：簡單通知
    # "./scripts/notify-slack.sh v${NEW_VERSION}"  # 範例：Slack 通知
    # "./scripts/update-docs.sh ${NEW_VERSION}" # 範例：更新文檔
)

# =============================================================================
# 配置說明與範例
# =============================================================================

# 常見的驗證配置範例：
#
# 1. 基本測試驗證：
#    REQUIRE_TESTS=true
#    CUSTOM_PRE_RELEASE_VALIDATIONS=("make test")
#
# 2. 完整程式碼品質檢查：
#    CUSTOM_PRE_RELEASE_VALIDATIONS=(
#        "make test"
#        "make lint"
#        "make type-check"
#        "terraform fmt -check"
#    )
#
# 3. CHANGELOG 要求：
#    REQUIRE_CHANGELOG=true
#
# 4. 客製化通知：
#    CUSTOM_POST_RELEASE_ACTIONS=(
#        "slack-notify.sh 'Released ${SERVICE_NAME} v${NEW_VERSION}'"
#        "update-api-docs.sh ${NEW_VERSION}"
#    )

# 變數說明：
# - ${NEW_VERSION}：新版本號（例如：1.2.3）
# - ${CURRENT_VERSION}：當前版本號
# - ${SERVICE_NAME}：服務名稱
# - ${VERSION_FILE}：版本檔案路徑

# 注意事項：
# 1. 所有陣列中的指令都在服務根目錄執行
# 2. 指令失敗時會顯示詳細錯誤訊息
# 3. 可以使用環境變數和 shell 語法
# 4. 建議先在測試環境驗證客製化指令
