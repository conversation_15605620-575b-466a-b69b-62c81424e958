# {{SERVICE_NAME}} - Makefile
# 常用開發指令

.PHONY: help dev-setup dev-up dev-down dev-clean dev-logs test test-unit test-integration test-watch lint format type-check build deploy-dev deploy-prod shell status health-check tf-check tf-validate tf-fmt tf-plan tf-init pre-commit-setup pre-commit-run deploy-shared-infrastructure

# 顯示幫助資訊
help:
	@echo "{{SERVICE_NAME}} - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  dev-setup     - 設定開發環境"
	@echo "  dev-up        - 啟動本地服務 (包含模擬器)"
	@echo "  dev-down      - 停止本地服務"
	@echo "  dev-clean     - 清理本地環境"
	@echo "  dev-logs      - 查看服務日誌"
	@echo ""
	@echo "Testing:"
	@echo "  test          - 運行所有測試"
	@echo "  test-unit     - 運行單元測試"
	@echo "  test-integration - 運行整合測試"
	@echo "  test-watch    - 監看模式運行測試"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint          - 代碼檢查"
	@echo "  format        - 格式化代碼"
	@echo "  type-check    - 型別檢查"
	@echo "  pre-commit-setup - 設定 pre-commit hooks"
	@echo "  pre-commit-run   - 執行 pre-commit 檢查"
	@echo ""
	@echo "Terraform:"
	@echo "  tf-check      - 完整 Terraform 檢查（推薦）"
	@echo "  tf-validate   - Terraform 語法驗證"
	@echo "  tf-fmt        - Terraform 格式化"
	@echo "  tf-plan       - Terraform 計畫檢查"
	@echo "  tf-init       - Terraform 初始化"
	@echo ""
	@echo "Infrastructure:"
	@echo "  deploy-shared-infrastructure - 部署共用基礎設施（必須在應用部署前執行）"
	@echo ""
	@echo "Build & Deploy:"
	@echo "  build         - 建置 Docker 映像"
	@echo "  deploy-dev    - 部署到開發環境"
	@echo "  deploy-prod   - 部署到生產環境"
	@echo ""
	@echo "Tools:"
	@echo "  shell         - 進入容器 shell"
	@echo "  status        - 查看容器狀態"
	@echo "  health-check  - 健康檢查"

# 設定開發環境
dev-setup:
	@echo "Setting up development environment..."
	@docker --version || (echo "Please install Docker Desktop" && exit 1)
	@docker-compose --version || (echo "Please install Docker Compose" && exit 1)
	@echo "Development environment setup complete!"

# 啟動本地服務
dev-up:
	@echo "Starting local development services..."
	@docker-compose up -d --build
	@echo "Services started! Waiting for services to be ready..."
	@sleep 10
	@echo "Services status:"
	@docker-compose ps

# 停止本地服務
dev-down:
	@echo "Stopping local development services..."
	@docker-compose down
	@echo "Services stopped!"

# 清理本地環境
dev-clean:
	@echo "Cleaning up local environment..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "Local environment cleaned!"

# 查看服務日誌
dev-logs:
	@docker-compose logs -f {{SERVICE_NAME_KEBAB}}

# 運行所有測試
test:
	@echo "Running all tests..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		if [ -f docker-compose.ci.yml ]; then \
			echo "📋 啟動服務..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d --build; \
			echo "📊 檢查容器狀態..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps; \
			echo "📝 顯示容器日誌..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs --tail=50; \
			echo "⏰ 等待服務啟動..."; \
			sleep 30; \
			echo "🔍 再次檢查容器狀態..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps; \
			if ! docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps | grep -q "Up"; then \
				echo "❌ 容器啟動失敗，顯示詳細日誌:"; \
				docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs; \
				docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
				exit 1; \
			fi; \
			echo "🧪 執行測試..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T {{SERVICE_NAME_KEBAB}} pytest -v --cov=src --cov-report=term-missing; \
			TEST_EXIT_CODE=$$?; \
			echo "📋 測試完成，清理容器..."; \
			if [ $$TEST_EXIT_CODE -ne 0 ]; then \
				echo "❌ 測試失敗，顯示最終日誌:"; \
				docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs --tail=100; \
			fi; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
			exit $$TEST_EXIT_CODE; \
		else \
			echo "📋 啟動服務..."; \
			docker-compose up -d --build; \
			echo "📊 檢查容器狀態..."; \
			docker-compose ps; \
			echo "📝 顯示容器日誌..."; \
			docker-compose logs --tail=50; \
			echo "⏰ 等待服務啟動..."; \
			sleep 30; \
			echo "🔍 再次檢查容器狀態..."; \
			docker-compose ps; \
			if ! docker-compose ps | grep -q "Up"; then \
				echo "❌ 容器啟動失敗，顯示詳細日誌:"; \
				docker-compose logs; \
				docker-compose down; \
				exit 1; \
			fi; \
			echo "🧪 執行測試..."; \
			docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v --cov=src --cov-report=term-missing; \
			TEST_EXIT_CODE=$$?; \
			echo "📋 測試完成，清理容器..."; \
			if [ $$TEST_EXIT_CODE -ne 0 ]; then \
				echo "❌ 測試失敗，顯示最終日誌:"; \
				docker-compose logs --tail=100; \
			fi; \
			docker-compose down; \
			exit $$TEST_EXIT_CODE; \
		fi; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v --cov=src --cov-report=term-missing; \
	fi

# 運行單元測試
test-unit:
	@echo "Running unit tests..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		if [ -f docker-compose.ci.yml ]; then \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d --build; \
		else \
			docker-compose up -d --build; \
		fi; \
		echo "等待服務啟動..."; \
		sleep 30; \
		if [ -f docker-compose.ci.yml ]; then \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m unit; \
			TEST_EXIT_CODE=$$?; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
		else \
			docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m unit; \
			TEST_EXIT_CODE=$$?; \
			docker-compose down; \
		fi; \
		exit $$TEST_EXIT_CODE; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m unit; \
	fi

# 運行整合測試
test-integration:
	@echo "Running integration tests..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		if [ -f docker-compose.ci.yml ]; then \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d --build; \
		else \
			docker-compose up -d --build; \
		fi; \
		echo "等待服務啟動..."; \
		sleep 30; \
		if [ -f docker-compose.ci.yml ]; then \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m integration; \
			TEST_EXIT_CODE=$$?; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
		else \
			docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m integration; \
			TEST_EXIT_CODE=$$?; \
			docker-compose down; \
		fi; \
		exit $$TEST_EXIT_CODE; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T {{SERVICE_NAME_KEBAB}} pytest -v -m integration; \
	fi

# 監看模式運行測試
test-watch:
	@echo "Running tests in watch mode..."
	@docker-compose exec {{SERVICE_NAME_KEBAB}} pytest-watch --runner "pytest -v"

# 代碼檢查
lint:
	@echo "Running code quality checks..."
	@docker-compose exec -T {{SERVICE_NAME_KEBAB}} flake8 src/ tests/
	@echo "Code quality checks passed!"

# 格式化代碼
format:
	@echo "Formatting code..."
	@docker-compose exec -T {{SERVICE_NAME_KEBAB}} black src/ tests/
	@docker-compose exec -T {{SERVICE_NAME_KEBAB}} isort src/ tests/
	@echo "Code formatted!"

# 型別檢查
type-check:
	@echo "Running type checks..."
	@docker-compose exec -T {{SERVICE_NAME_KEBAB}} mypy src/

# 建置 Docker 映像
build:
	@echo "Building Docker image..."
	@docker build -t {{SERVICE_NAME_KEBAB}}:latest .
	@echo "Docker image built successfully!"

# ====== Terraform 相關指令 ======

# 完整 Terraform 檢查（推薦使用）
tf-check:
	@echo "🔍 執行完整 Terraform 檢查..."
	@$(MAKE) tf-init
	@$(MAKE) tf-fmt
	@$(MAKE) tf-validate
	@echo "✅ Terraform 檢查全部通過！"

# Terraform 初始化
tf-init:
	@echo "🚀 初始化 Terraform..."
	@cd terraform && terraform init -backend=false
	@echo "✅ Terraform 初始化完成"

# Terraform 格式化
tf-fmt:
	@echo "🎨 格式化 Terraform 檔案..."
	@cd terraform && terraform fmt -recursive -check || (echo "❌ 格式化檢查失敗，執行自動格式化..." && terraform fmt -recursive)
	@echo "✅ Terraform 格式化完成"

# Terraform 語法驗證
tf-validate:
	@echo "🔍 驗證 Terraform 語法..."
	@cd terraform && terraform validate
	@echo "✅ Terraform 語法驗證通過"

# Terraform 計畫檢查（乾跑）
tf-plan:
	@echo "📋 執行 Terraform 計畫檢查..."
	@cd terraform && terraform init
	@echo "🔍 開發環境計畫檢查:"
	@cd terraform && terraform plan -var="environment=dev" -out=tfplan-dev
	@echo "✅ Terraform 計畫檢查完成，計畫檔案已儲存為 tfplan-dev"

# ====== Pre-commit 相關指令 ======

# 設定 pre-commit hooks
pre-commit-setup:
	@echo "🔧 設定 pre-commit hooks..."
	@if ! command -v pre-commit >/dev/null 2>&1; then \
		echo "⚠️  pre-commit 未安裝，正在安裝..."; \
		pip install pre-commit; \
	fi
	@pre-commit install
	@pre-commit install --hook-type commit-msg
	@echo "✅ Pre-commit hooks 設定完成"
	@echo ""
	@echo "💡 現在當你執行 git commit 時，會自動執行："
	@echo "   - 程式碼格式化 (black, isort)"
	@echo "   - 程式碼品質檢查 (flake8)"
	@echo "   - Terraform 格式化和驗證"
	@echo "   - JSON/YAML 格式檢查"

# 手動執行 pre-commit 檢查
pre-commit-run:
	@echo "🔍 執行 pre-commit 檢查..."
	@if ! command -v pre-commit >/dev/null 2>&1; then \
		echo "❌ pre-commit 未安裝，請執行: make pre-commit-setup"; \
		exit 1; \
	fi
	@pre-commit run --all-files
	@echo "✅ Pre-commit 檢查完成"

# ====== 基礎設施部署指令 ======

# 部署共用基礎設施
deploy-shared-infrastructure:
	@echo "🚀 部署共用基礎設施..."
	@echo "⚠️  注意：這會部署 shared infrastructure，包含 Service Account、BigQuery Dataset 等資源"
	@echo ""
	@if [ ! -d "../../infrastructure/terraform/shared" ]; then \
		echo "❌ 找不到 shared infrastructure 目錄"; \
		exit 1; \
	fi
	@echo "📁 切換到 shared infrastructure 目錄..."
	@cd ../../infrastructure/terraform/shared && \
		if [ -x "./deploy.sh" ]; then \
			echo "🔧 使用部署腳本..."; \
			./deploy.sh prod; \
		else \
			echo "🔧 手動執行 Terraform..."; \
			terraform init && \
			terraform workspace select prod 2>/dev/null || terraform workspace new prod && \
			terraform plan -var="environment=prod" && \
			terraform apply -var="environment=prod"; \
		fi
	@echo ""
	@echo "✅ 共用基礎設施部署完成！"
	@echo ""
	@echo "🔗 接下來可以部署應用服務："
	@echo "  make deploy-prod"

# ====== 部署相關指令 ======

# 部署到開發環境
deploy-dev:
	@echo "🚀 部署到開發環境..."
	@echo "🔍 執行部署前檢查..."
	@$(MAKE) tf-check
	@echo "📋 檢查共用基礎設施是否存在..."
	@if ! cd ../../infrastructure/terraform/shared && terraform init > /dev/null 2>&1 && terraform workspace select dev > /dev/null 2>&1 && terraform show > /dev/null 2>&1; then \
		echo "⚠️  共用基礎設施未部署，正在部署..."; \
		cd ../../infrastructure/terraform/shared && \
		(terraform workspace select dev 2>/dev/null || terraform workspace new dev) && \
		terraform plan -var="environment=dev" && \
		terraform apply -var="environment=dev" -auto-approve; \
	else \
		echo "✅ 共用基礎設施已存在"; \
	fi
	@echo "📋 執行 Terraform 部署..."
	@cd terraform && terraform init
	@cd terraform && terraform workspace select dev 2>/dev/null || terraform workspace new dev
	@cd terraform && terraform plan -var="environment=dev"
	@cd terraform && terraform apply -var="environment=dev" -auto-approve
	@echo "✅ 開發環境部署完成"

# 部署到生產環境
deploy-prod:
	@echo "🚀 部署到生產環境..."
	@echo "🔍 執行部署前檢查..."
	@$(MAKE) tf-check
	@echo "📋 檢查共用基礎設施是否存在..."
	@if ! cd ../../infrastructure/terraform/shared && terraform init > /dev/null 2>&1 && terraform workspace select prod > /dev/null 2>&1 && terraform show > /dev/null 2>&1; then \
		echo "❌ 共用基礎設施未部署！"; \
		echo "請先執行: make deploy-shared-infrastructure"; \
		exit 1; \
	else \
		echo "✅ 共用基礎設施已存在"; \
	fi
	@echo "📋 執行 Terraform 計畫..."
	@cd terraform && terraform init
	@cd terraform && terraform workspace select prod 2>/dev/null || terraform workspace new prod
	@cd terraform && terraform plan -var="environment=prod"
	@echo ""
	@echo "⚠️  請仔細檢查上述計畫，確認無誤後手動執行："
	@echo "   cd terraform && terraform apply -var=\"environment=prod\""
	@echo ""
	@echo "💡 生產環境部署需要手動確認以確保安全性"

# 進入容器 shell
shell:
	@docker-compose exec {{SERVICE_NAME_KEBAB}} /bin/bash

# 查看容器狀態
status:
	@docker-compose ps
	@docker-compose top

# 健康檢查
health-check:
	@echo "Checking service health..."
	@curl -s http://localhost:8080/health | jq . || echo "Make sure services are running with 'make dev-up'"
