"""
{{SERVICE_NAME}} 配置管理

統一管理應用程式配置，支援不同環境設定
"""

import os
from typing import Optional
from dataclasses import dataclass
from pathlib import Path

# 載入環境變數
from dotenv import load_dotenv

# 載入 .env 檔案
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)


@dataclass
class DatabaseConfig:
    """資料庫配置"""
    project_id: str
    firestore_database: str
    bigquery_dataset: str
    firestore_emulator_host: Optional[str] = None
    bigquery_emulator_host: Optional[str] = None


@dataclass
class ServiceConfig:
    """服務配置"""
    name: str
    version: str
    environment: str
    debug: bool
    log_level: str
    host: str
    port: int
    workers: int


@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str
    api_key: Optional[str] = None
    webhook_secret: Optional[str] = None


@dataclass
class ProcessingConfig:
    """資料處理配置"""
    batch_size: int
    sync_interval_minutes: int
    max_retries: int
    retry_delay_seconds: int


@dataclass
class MonitoringConfig:
    """監控配置"""
    enable_metrics: bool
    metrics_port: int
    prometheus_endpoint: str = "/metrics"


class Config:
    """主要配置類別"""

    def __init__(self):
        self.environment = os.getenv('ENVIRONMENT', 'dev')

        # 資料庫配置
        self.database = DatabaseConfig(
            project_id=os.getenv('PROJECT_ID', 'tagtoo-tracking-dev'),
            firestore_database=os.getenv('FIRESTORE_DATABASE', '(default)'),
            bigquery_dataset=os.getenv('BIGQUERY_DATASET', 'integrated_event'),
            firestore_emulator_host=os.getenv('FIRESTORE_EMULATOR_HOST'),
            bigquery_emulator_host=os.getenv('BIGQUERY_EMULATOR_HOST'),
        )

        # 服務配置
        self.service = ServiceConfig(
            name=os.getenv('SERVICE_NAME', '{{SERVICE_NAME_KEBAB}}'),
            version='1.0.0',
            environment=self.environment,
            debug=os.getenv('DEBUG', 'false').lower() == 'true',
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            host=os.getenv('HOST', '0.0.0.0'),
            port=int(os.getenv('PORT', 8080)),
            workers=int(os.getenv('WORKERS', 2)),
        )

        # 安全配置
        self.security = SecurityConfig(
            secret_key=os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production'),
            api_key=os.getenv('API_KEY'),
            webhook_secret=os.getenv('WEBHOOK_SECRET'),
        )

        # 處理配置
        self.processing = ProcessingConfig(
            batch_size=int(os.getenv('BATCH_SIZE', 1000)),
            sync_interval_minutes=int(os.getenv('SYNC_INTERVAL_MINUTES', 5)),
            max_retries=int(os.getenv('MAX_RETRIES', 3)),
            retry_delay_seconds=int(os.getenv('RETRY_DELAY_SECONDS', 5)),
        )

        # 監控配置
        self.monitoring = MonitoringConfig(
            enable_metrics=os.getenv('ENABLE_METRICS', 'true').lower() == 'true',
            metrics_port=int(os.getenv('METRICS_PORT', 9090)),
        )

    @property
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.environment == 'dev'

    @property
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.environment == 'prod'

    @property
    def use_emulator(self) -> bool:
        """是否使用模擬器"""
        return (self.database.firestore_emulator_host is not None or
                self.database.bigquery_emulator_host is not None)

    def validate(self) -> None:
        """驗證配置"""
        errors = []

        # 檢查必要配置
        if not self.database.project_id:
            errors.append("PROJECT_ID is required")

        if self.is_production and self.security.secret_key == 'dev-secret-key-change-in-production':
            errors.append("SECRET_KEY must be changed in production")

        if self.service.port < 1 or self.service.port > 65535:
            errors.append("PORT must be between 1 and 65535")

        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")


# 全域配置實例
config = Config()

# 驗證配置
config.validate()
