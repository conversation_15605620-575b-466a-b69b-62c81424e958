"""
{{SERVICE_NAME}} - {{DESCRIPTION}}

這是一個範本服務，用於整合事件資料到 integrated_event BigQuery 表格。
使用此範本時請替換所有 {{placeholder}} 變數。
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from flask import Flask, request, jsonify
from google.cloud import firestore, bigquery
from google.cloud.exceptions import NotFound, GoogleCloudError

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 環境變數
PROJECT_ID = os.environ.get('PROJECT_ID', 'tagtoo-tracking')
FIRESTORE_DATABASE = os.environ.get('FIRESTORE_DATABASE', '(default)')
BIGQUERY_DATASET = os.environ.get('BIGQUERY_DATASET', 'integrated_event')
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'dev')

# 全域客戶端變數（延遲初始化）
firestore_client = None
bigquery_client = None


def init_gcp_clients(max_retries: int = 3) -> bool:
    """
    初始化 GCP 客戶端

    Args:
        max_retries: 最大重試次數

    Returns:
        bool: 初始化是否成功
    """
    global firestore_client, bigquery_client

    if firestore_client is not None and bigquery_client is not None:
        return True

    for attempt in range(max_retries):
        try:
            if firestore_client is None:
                firestore_client = firestore.Client(
                    project=PROJECT_ID,
                    database=FIRESTORE_DATABASE
                )
                logger.info("Firestore client initialized successfully")

            if bigquery_client is None:
                bigquery_client = bigquery.Client(project=PROJECT_ID)
                logger.info("BigQuery client initialized successfully")

            return True

        except Exception as e:
            logger.warning(f"GCP clients initialization attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:
                logger.error("Failed to initialize GCP clients after all retries")
                return False

    return False


class {{SERVICE_NAME_PASCAL}}Processor:
    """{{SERVICE_NAME}} 資料處理器"""

    def __init__(self):
        self.collection_name = f"{{SERVICE_NAME_KEBAB}}-{ENVIRONMENT}"
        self.target_table = f"{PROJECT_ID}.{BIGQUERY_DATASET}.integrated_event"

    def process_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理原始事件資料，轉換為 integrated_event 格式

        Args:
            event_data: 原始事件資料

        Returns:
            標準化的事件資料
        """
        try:
            # TODO: 實作具體的資料轉換邏輯
            processed_event = {
                "event_time": datetime.utcnow().isoformat(),
                "partner_source": "{{SERVICE_NAME_KEBAB}}",
                "ec_id": event_data.get("ec_id", ""),
                "user": {
                    "user_id": event_data.get("user_id"),
                    "email": event_data.get("email"),
                    "phone": event_data.get("phone"),
                    "external_id": event_data.get("external_id"),
                },
                "event_type": event_data.get("event_type", "custom"),
                "event_name": event_data.get("event_name", ""),
                "page_url": event_data.get("page_url", ""),
                "referrer_url": event_data.get("referrer_url", ""),
                "items": event_data.get("items", []),
                "custom_parameters": event_data.get("custom_parameters", {}),
                "source_system": "{{SERVICE_NAME_KEBAB}}",
                "processing_time": datetime.utcnow().isoformat(),
                "raw_data": event_data
            }

            return processed_event

        except Exception as e:
            logger.error(f"Error processing event: {str(e)}")
            raise

    def save_to_firestore(self, event_data: Dict[str, Any]) -> str:
        """
        儲存資料到 Firestore

        Args:
            event_data: 要儲存的事件資料

        Returns:
            文檔 ID
        """
        try:
            # 確保 GCP 客戶端已初始化
            if not init_gcp_clients():
                raise Exception("Failed to initialize GCP clients")

            doc_ref = firestore_client.collection(self.collection_name).add(event_data)
            doc_id = doc_ref[1].id
            logger.info(f"Event saved to Firestore: {doc_id}")
            return doc_id

        except Exception as e:
            logger.error(f"Error saving to Firestore: {str(e)}")
            raise

    def sync_to_bigquery(self, batch_size: int = 1000) -> Dict[str, Any]:
        """
        同步 Firestore 資料到 BigQuery

        Args:
            batch_size: 批次處理大小

        Returns:
            同步結果統計
        """
        try:
            # 確保 GCP 客戶端已初始化
            if not init_gcp_clients():
                raise Exception("Failed to initialize GCP clients")

            # 獲取待處理的資料
            docs = firestore_client.collection(self.collection_name)\
                .where("synced_to_bq", "==", False)\
                .limit(batch_size)\
                .stream()

            events_to_sync = []
            doc_ids_to_update = []

            for doc in docs:
                events_to_sync.append(doc.to_dict())
                doc_ids_to_update.append(doc.id)

            if not events_to_sync:
                return {"status": "success", "synced_count": 0}

            # 插入到 BigQuery
            table = bigquery_client.get_table(self.target_table)
            errors = bigquery_client.insert_rows_json(table, events_to_sync)

            if errors:
                logger.error(f"BigQuery insert errors: {errors}")
                raise Exception(f"BigQuery insert failed: {errors}")

            # 標記為已同步
            batch = firestore_client.batch()
            for doc_id in doc_ids_to_update:
                doc_ref = firestore_client.collection(self.collection_name).document(doc_id)
                batch.update(doc_ref, {"synced_to_bq": True, "sync_time": datetime.utcnow()})
            batch.commit()

            logger.info(f"Successfully synced {len(events_to_sync)} events to BigQuery")

            return {
                "status": "success",
                "synced_count": len(events_to_sync)
            }

        except Exception as e:
            logger.error(f"Error syncing to BigQuery: {str(e)}")
            raise


# 初始化處理器
processor = {{SERVICE_NAME_PASCAL}}Processor()


@app.route('/health', methods=['GET'])
def health_check():
    """
    健康檢查端點

    總是回傳 200 狀態碼以確保容器能正常啟動，
    但在回應中提供詳細的服務狀態資訊
    """
    # 嘗試初始化 GCP 客戶端（如果尚未初始化）
    gcp_status = init_gcp_clients()

    health_data = {
        "status": "healthy" if gcp_status else "degraded",
        "service": "{{SERVICE_NAME_KEBAB}}",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "gcp_clients": {
            "firestore": firestore_client is not None,
            "bigquery": bigquery_client is not None,
            "initialization_status": "success" if gcp_status else "failed"
        },
        "message": "Service is running" if gcp_status else "Service running but GCP clients not ready"
    }

    # 總是回傳 200，讓 Cloud Run 認為服務健康
    # 實際的服務狀態可以通過 status 欄位判斷
    return jsonify(health_data), 200


@app.route('/webhook', methods=['POST'])
def webhook_handler():
    """處理 webhook 請求"""
    try:
        # 驗證請求
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400

        event_data = request.get_json()
        if not event_data:
            return jsonify({"error": "Empty request body"}), 400

        # 處理事件資料
        processed_event = processor.process_event(event_data)

        # 儲存到 Firestore
        doc_id = processor.save_to_firestore(processed_event)

        return jsonify({
            "status": "success",
            "document_id": doc_id,
            "message": "Event processed successfully"
        }), 200

    except Exception as e:
        logger.error(f"Webhook processing error: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


@app.route('/sync', methods=['POST'])
def manual_sync():
    """手動觸發同步到 BigQuery"""
    try:
        batch_size = request.json.get('batch_size', 1000) if request.is_json else 1000
        result = processor.sync_to_bigquery(batch_size)

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Manual sync error: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


def main(request=None):
    """Cloud Function 入口點"""
    # 如果是 Cloud Function，處理 PubSub 觸發
    if request and hasattr(request, 'get_json'):
        try:
            # 處理 PubSub 訊息
            envelope = request.get_json()
            if envelope and 'message' in envelope:
                # 解析訊息內容
                # TODO: 根據實際需求實作 PubSub 處理邏輯
                pass

            return 'OK', 200

        except Exception as e:
            logger.error(f"Cloud Function error: {str(e)}")
            return f'Error: {str(e)}', 500

    # 本地開發模式
    return app


if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=True)
