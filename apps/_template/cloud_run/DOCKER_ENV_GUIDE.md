# Docker Compose 環境變數管理指南

## 🎯 **動態環境變數載入方案**

### 方法一：使用 `env_file`（推薦）

```yaml
services:
  my-service:
    env_file:
      - .env
      - .env.local # 可選的本地覆蓋
```

**優點**：

- 集中管理所有環境變數
- 支援多個檔案
- 自動載入，無需在 docker-compose.yml 中列出每個變數
- 支援註解和分組

### 方法二：使用環境變數替換

```yaml
services:
  my-service:
    environment:
      - PROJECT_ID=${PROJECT_ID:-tagtoo-tracking}
      - BATCH_SIZE=${BATCH_SIZE:-1000}
      - MEMORY_MB=${MEMORY_MB:-2048}
```

**說明**：

- `${VAR_NAME}`: 使用環境變數值
- `${VAR_NAME:-default}`: 提供預設值
- 從主機環境或 `.env` 檔案讀取

### 方法三：混合使用

```yaml
services:
  my-service:
    env_file: .env
    environment:
      # 覆蓋特定變數
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      # 動態計算的值
      - SERVICE_URL=http://localhost:${PORT:-8080}
```

## 🔧 **最佳實踐配置**

### 1. 檔案結構

```
your-service/
├── .env.example        # 範例檔案（加入版控）
├── .env               # 實際配置（不加入版控）
├── .env.dev           # 開發環境特定
├── .env.prod          # 生產環境特定
└── docker-compose.yml
```

### 2. `.gitignore` 設定

```gitignore
# 環境變數檔案
.env
.env.local
.env.*.local
```

### 3. 多環境支援

**docker-compose.yml**:

```yaml
services:
  my-service:
    env_file:
      - .env
      - .env.${ENVIRONMENT:-dev}
    environment:
      - COMPOSE_ENVIRONMENT=${ENVIRONMENT:-dev}
```

**使用方式**:

```bash
# 開發環境
ENVIRONMENT=dev docker-compose up

# 生產環境
ENVIRONMENT=prod docker-compose up
```

### 4. 動態服務名稱

**docker-compose.yml**:

```yaml
services:
  ${SERVICE_NAME:-my-service}:
    container_name: ${SERVICE_NAME:-my-service}-${ENVIRONMENT:-dev}
    env_file: .env
```

## 🚀 **實用技巧**

### 1. 驗證環境變數

```bash
# 檢查載入的環境變數
docker-compose config

# 檢查特定服務的環境變數
docker-compose exec my-service env
```

### 2. Makefile 整合

```makefile
# 載入環境變數並啟動
dev-up:
	@test -f .env || cp .env.example .env
	@docker-compose --env-file .env up -d

# 不同環境
dev-up:
	ENVIRONMENT=dev docker-compose up -d

prod-up:
	ENVIRONMENT=prod docker-compose up -d
```

### 3. 環境變數優先順序

1. Compose 檔案中的 `environment`
2. Shell 環境變數
3. `env_file` 指定的檔案
4. Dockerfile 中的 `ENV`

### 4. 動態端口分配

```yaml
services:
  my-service:
    ports:
      - "${HOST_PORT:-8080}:${CONTAINER_PORT:-8080}"
    env_file: .env
```

## ⚠️ **注意事項**

### 1. 安全性

- 敏感資訊放在 `.env` 檔案中
- 不要將 `.env` 檔案加入版控
- 生產環境使用 secrets 或專門的配置管理

### 2. 變數命名規範

```bash
# 好的命名
PROJECT_ID=tagtoo-tracking
SERVICE_NAME=legacy-event-sync
BATCH_SIZE=200000

# 避免的命名
proj=tagtoo        # 太短
batch_size=200000  # 不一致的命名風格
```

### 3. 文檔化

- 在 `.env.example` 中提供所有必要變數
- 加入註解說明各變數用途
- 提供合理的預設值

## 📋 **範例完整配置**

**.env.example**:

```bash
# 基本配置
SERVICE_NAME=my-service
ENVIRONMENT=dev
PROJECT_ID=tagtoo-tracking

# 資源配置
MEMORY_MB=2048
CPU_COUNT=2
BATCH_SIZE=1000

# 網路配置
HOST_PORT=8080
CONTAINER_PORT=8080
```

**docker-compose.yml**:

```yaml
services:
  ${SERVICE_NAME:-my-service}:
    platform: linux/amd64
    build: .
    ports:
      - "${HOST_PORT:-8080}:${CONTAINER_PORT:-8080}"
    env_file:
      - .env
    environment:
      - COMPOSE_PROJECT_NAME=${SERVICE_NAME:-my-service}
      - COMPOSE_ENVIRONMENT=${ENVIRONMENT:-dev}
    restart: unless-stopped
```

這樣的設計讓你可以：

- 一次設定，多處使用
- 不同環境使用不同配置
- 保持 docker-compose.yml 的通用性
- 避免硬編碼值
