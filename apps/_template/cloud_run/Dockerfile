# {{SERVICE_NAME}} Dockerfile
# 多階段建置，優化映像大小和安全性

# 建置階段
FROM python:3.11-slim AS builder

WORKDIR /app

# 安裝系統依賴
RUN apt-get update --fix-missing && \
    apt-get install -y --fix-missing \
    gcc \
    g++ \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 複製需求檔案
COPY requirements.txt .

# 安裝 Python 依賴
RUN pip install --no-cache-dir --user -r requirements.txt

# 運行階段
FROM python:3.11-slim

# 建立非 root 使用者
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

# 安裝運行時依賴
RUN apt-get update --fix-missing && \
    apt-get install -y --fix-missing \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 從建置階段複製 Python 套件
COPY --from=builder /root/.local /home/<USER>/.local

# 複製應用程式碼
COPY src/ ./src/
COPY *.py ./

# 設定環境變數
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8080

# 切換到非 root 使用者
USER appuser

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 啟動應用
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--workers", "2", "--timeout", "120", "src.main:app"]
