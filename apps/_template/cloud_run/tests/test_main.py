"""
{{SERVICE_NAME}} 測試範例

示範如何撰寫單元測試、整合測試和端到端測試
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 測試應用程式
from src.main import app, {{SERVICE_NAME_PASCAL}}Processor


class TestHealthCheck:
    """健康檢查測試"""

    @pytest.fixture
    def client(self):
        """Flask 測試客戶端"""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

    def test_health_endpoint(self, client):
        """測試健康檢查端點"""
        response = client.get('/health')

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert data['service'] == '{{SERVICE_NAME_KEBAB}}'
        assert 'timestamp' in data


class TestProcessor:
    """資料處理器測試"""

    @pytest.fixture
    def processor(self):
        """建立處理器實例"""
        return {{SERVICE_NAME_PASCAL}}Processor()

    @pytest.fixture
    def sample_event_data(self):
        """範例事件資料"""
        return {
            "ec_id": "test_ec_123",
            "user_id": "user_456",
            "email": "<EMAIL>",
            "event_type": "purchase",
            "event_name": "purchase_completed",
            "page_url": "https://example.com/checkout",
            "items": [
                {
                    "item_id": "product_789",
                    "item_name": "Test Product",
                    "price": 99.99,
                    "quantity": 1
                }
            ],
            "custom_parameters": {
                "campaign": "summer_sale",
                "medium": "email"
            }
        }

    def test_process_event_success(self, processor, sample_event_data):
        """測試成功處理事件"""
        result = processor.process_event(sample_event_data)

        # 檢查必要欄位
        assert result['partner_source'] == '{{SERVICE_NAME_KEBAB}}'
        assert result['ec_id'] == 'test_ec_123'
        assert result['user']['user_id'] == 'user_456'
        assert result['user']['email'] == '<EMAIL>'
        assert result['event_type'] == 'purchase'
        assert result['event_name'] == 'purchase_completed'
        assert result['source_system'] == '{{SERVICE_NAME_KEBAB}}'
        assert result['raw_data'] == sample_event_data

        # 檢查時間戳記
        assert 'event_time' in result
        assert 'processing_time' in result

    def test_process_event_minimal_data(self, processor):
        """測試最小資料處理"""
        minimal_data = {"ec_id": "test"}

        result = processor.process_event(minimal_data)

        assert result['partner_source'] == '{{SERVICE_NAME_KEBAB}}'
        assert result['ec_id'] == 'test'
        assert result['user']['user_id'] is None
        assert result['event_type'] == 'custom'
        assert result['items'] == []

    @patch('src.main.firestore_client')
    def test_save_to_firestore_success(self, mock_firestore, processor, sample_event_data):
        """測試成功儲存到 Firestore"""
        # 模擬 Firestore 回應
        mock_doc_ref = Mock()
        mock_doc_ref.id = 'test_doc_123'
        mock_firestore.collection.return_value.add.return_value = (None, mock_doc_ref)

        processed_event = processor.process_event(sample_event_data)
        doc_id = processor.save_to_firestore(processed_event)

        assert doc_id == 'test_doc_123'
        mock_firestore.collection.assert_called_once()

    @patch('src.main.firestore_client')
    def test_save_to_firestore_error(self, mock_firestore, processor, sample_event_data):
        """測試 Firestore 儲存錯誤"""
        # 模擬 Firestore 錯誤
        mock_firestore.collection.return_value.add.side_effect = Exception("Firestore error")

        processed_event = processor.process_event(sample_event_data)

        with pytest.raises(Exception, match="Firestore error"):
            processor.save_to_firestore(processed_event)


class TestWebhookEndpoint:
    """Webhook 端點測試"""

    @pytest.fixture
    def client(self):
        """Flask 測試客戶端"""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

    @pytest.fixture
    def sample_webhook_data(self):
        """範例 webhook 資料"""
        return {
            "ec_id": "webhook_test_123",
            "user_id": "webhook_user_456",
            "event_type": "page_view",
            "page_url": "https://example.com/product/123"
        }

    @patch('src.main.processor')
    def test_webhook_success(self, mock_processor, client, sample_webhook_data):
        """測試成功處理 webhook"""
        # 模擬處理器回應
        mock_processor.process_event.return_value = {"processed": True}
        mock_processor.save_to_firestore.return_value = "doc_123"

        response = client.post('/webhook',
                             data=json.dumps(sample_webhook_data),
                             content_type='application/json')

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'success'
        assert data['document_id'] == 'doc_123'

        mock_processor.process_event.assert_called_once_with(sample_webhook_data)
        mock_processor.save_to_firestore.assert_called_once()

    def test_webhook_invalid_content_type(self, client):
        """測試無效的 Content-Type"""
        response = client.post('/webhook',
                             data="invalid data",
                             content_type='text/plain')

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['error'] == 'Content-Type must be application/json'

    def test_webhook_empty_body(self, client):
        """測試空的請求體"""
        response = client.post('/webhook',
                             data='',
                             content_type='application/json')

        assert response.status_code == 400

    @patch('src.main.processor')
    def test_webhook_processing_error(self, mock_processor, client, sample_webhook_data):
        """測試處理錯誤"""
        # 模擬處理錯誤
        mock_processor.process_event.side_effect = Exception("Processing error")

        response = client.post('/webhook',
                             data=json.dumps(sample_webhook_data),
                             content_type='application/json')

        assert response.status_code == 500
        data = json.loads(response.data)
        assert data['status'] == 'error'


class TestSyncEndpoint:
    """同步端點測試"""

    @pytest.fixture
    def client(self):
        """Flask 測試客戶端"""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

    @patch('src.main.processor')
    def test_manual_sync_success(self, mock_processor, client):
        """測試成功手動同步"""
        # 模擬同步結果
        mock_processor.sync_to_bigquery.return_value = {
            "status": "success",
            "synced_count": 10
        }

        response = client.post('/sync',
                             data=json.dumps({"batch_size": 500}),
                             content_type='application/json')

        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'success'
        assert data['synced_count'] == 10

        mock_processor.sync_to_bigquery.assert_called_once_with(500)

    @patch('src.main.processor')
    def test_manual_sync_default_batch_size(self, mock_processor, client):
        """測試預設批次大小"""
        mock_processor.sync_to_bigquery.return_value = {"status": "success", "synced_count": 0}

        response = client.post('/sync')

        assert response.status_code == 200
        mock_processor.sync_to_bigquery.assert_called_once_with(1000)


# 整合測試
@pytest.mark.integration
class TestIntegration:
    """整合測試"""

    @pytest.fixture
    def client(self):
        """Flask 測試客戶端"""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

    @pytest.mark.slow
    @patch('src.main.firestore_client')
    @patch('src.main.bigquery_client')
    def test_full_workflow(self, mock_bigquery, mock_firestore, client):
        """測試完整工作流程"""
        # 模擬 Firestore
        mock_doc_ref = Mock()
        mock_doc_ref.id = 'integration_test_doc'
        mock_firestore.collection.return_value.add.return_value = (None, mock_doc_ref)

        # 模擬 BigQuery
        mock_bigquery.insert_rows_json.return_value = []

        # 測試資料
        test_data = {
            "ec_id": "integration_test",
            "user_id": "test_user",
            "event_type": "purchase"
        }

        # 發送 webhook
        response = client.post('/webhook',
                             data=json.dumps(test_data),
                             content_type='application/json')

        assert response.status_code == 200

        # 驗證 Firestore 被呼叫
        mock_firestore.collection.assert_called()

        # 觸發同步
        response = client.post('/sync')
        assert response.status_code == 200


# 測試標記範例
@pytest.mark.unit
def test_unit_example():
    """單元測試範例"""
    pass


@pytest.mark.slow
def test_slow_example():
    """執行時間較長的測試範例"""
    pass


@pytest.mark.external
def test_external_service():
    """需要外部服務的測試範例"""
    pass
