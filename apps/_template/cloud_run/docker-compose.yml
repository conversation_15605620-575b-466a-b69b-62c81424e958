# {{SERVICE_NAME}} 本地開發環境
# 使用 docker-compose up -d 啟動開發環境

services:
  { { SERVICE_NAME_KEBAB } }:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - .env
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./config.py:/app/config.py
      - ./.env:/app/.env
    depends_on:
      - firestore
      - bigquery
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

  firestore:
    platform: linux/amd64
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    ports:
      - "8081:8080"
    command: >
      sh -c "gcloud emulators firestore start
             --host-port=0.0.0.0:8080
             --project=tagtoo-tracking-dev"
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

  bigquery:
    platform: linux/amd64
    image: ghcr.io/goccy/bigquery-emulator:latest
    ports:
      - "9050:9050"
    environment:
      - PROJECT_ID=tagtoo-tracking-dev
      - DATASET_ID=integrated_event
    volumes:
      - ./terraform/schema:/schema
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
    networks:
      - "{{SERVICE_NAME_KEBAB}}-network"
    restart: unless-stopped

networks:
  "{{SERVICE_NAME_KEBAB}}-network":
    driver: bridge

volumes:
  grafana-storage:
