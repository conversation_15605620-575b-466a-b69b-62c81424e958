# Cloud Run 服務模板

此模板適用於需要提供 HTTP API 服務的應用，例如：

- Webhook 接收器
- REST API 服務
- 需要持續運行的服務

## 📁 結構說明

```
cloud_run/
├── .dockerignore          # Docker 忽略檔案
├── Dockerfile             # Docker 映像設定
├── docker-compose.yml     # 本地開發環境
├── docker-compose.ci.yml  # CI 環境設定
├── DOCKER_ENV_GUIDE.md    # Docker 環境變數指南
├── src/                   # 應用程式碼
│   └── main.py           # 主要應用程式
├── tests/                 # 測試程式碼
├── docs/                  # 文檔
│   └── TERRAFORM_VALIDATION.md
└── terraform/             # 基礎設施配置
    ├── main.tf
    └── variables.tf
```

## 🚀 使用說明

1. **複製此模板**：

   ```bash
   # 從專案根目錄
   python scripts/create-service.py --service-name your-service-name --service-type cloud_run --description "Your service description"
   ```

2. **開發環境**：

   ```bash
   cd apps/your-service-name
   docker-compose up -d --build
   ```

3. **部署**：
   ```bash
   # 自動納入 CI/CD 流程，只要具備 Makefile 和 terraform/ 目錄
   make deploy-dev
   ```

## 🔧 配置要點

### Docker 相關

- 使用 `python:3.11-slim` 基礎映像
- 最佳化的 layer 順序和快取策略
- 支援 multi-stage build

### Cloud Run 配置

- 自動擴展 (0-100 instances)
- 記憶體：512MB-2GB
- CPU：1-2 vCPUs
- 支援 service account 認證

### 網路和安全

- 不開放 `allUsers` 權限
- 使用 Cloud Load Balancer + IAP
- 支援 HTTPS 和自定義域名

## 📊 監控

- 健康檢查端點：`/health`
- 自動配置 Cloud Monitoring
- 日誌自動收集到 Cloud Logging

## 💡 最佳實踐

1. **環境變數管理**：使用 `.env` 檔案，勿在 Dockerfile 中硬編碼
2. **錯誤處理**：實作適當的錯誤處理和重試機制
3. **日誌記錄**：使用結構化日誌，方便 Cloud Logging 分析
4. **資源管理**：適當設定 CPU/Memory 限制，避免資源浪費

更多詳細資訊請參考 `../common/README.md`。
