# CI 環境的 docker-compose 覆蓋設定
# 用於 GitHub Actions 等 CI 環境

services:
  { { SERVICE_NAME_KEBAB } }:
    # CI 環境不需要 volume 掛載原始碼（已經在映像中）
    volumes:
      - ./.env:/app/.env
    environment:
      # CI 環境使用環境變數進行認證，不需要 ADC 檔案
      - GOOGLE_APPLICATION_CREDENTIALS=
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
    # 簡化的命令，不使用 reload
    command:
      [
        "gunicorn",
        "--bind",
        "0.0.0.0:8080",
        "--workers",
        "1",
        "--timeout",
        "120",
        "src.main:app",
      ]
    # CI 環境的健康檢查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
