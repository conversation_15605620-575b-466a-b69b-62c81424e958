# Terraform 驗證機制指南

> 本專案已實作多層級的 Terraform 驗證機制，確保在不同階段都能早期發現問題，避免在 CI/CD 中才發現錯誤。

## 🎯 **驗證機制概覽**

我們實作了三個層級的 Terraform 驗證：

1. **🔒 本地 Pre-commit Hooks** - 在 `git commit` 時自動執行
2. **🛠️ Makefile 整合** - 本地開發時手動執行
3. **🚀 CI/CD 早期驗證** - 在 GitHub Actions 中優先執行

---

## 🔒 **1. Pre-commit Hooks（推薦）**

### 🚀 **快速設定**

```bash
# 一鍵設定 pre-commit hooks
make pre-commit-setup
```

### 🔍 **檢查項目**

每次 `git commit` 時會自動執行：

- ✅ **Terraform 格式化** (`terraform fmt`)
- ✅ **Terraform 語法驗證** (`terraform validate`)
- ✅ **Terraform 安全檢查** (`tfsec`)
- ✅ **Python 程式碼格式化** (`black`, `isort`)
- ✅ **程式碼品質檢查** (`flake8`)
- ✅ **JSON/YAML 格式檢查**
- ✅ **環境變數檢查**

### 💡 **使用技巧**

```bash
# 手動執行所有檢查
make pre-commit-run

# 跳過 pre-commit hooks（緊急情況）
git commit --no-verify -m "緊急修復"

# 查看 pre-commit 狀態
pre-commit --version
```

---

## 🛠️ **2. Makefile 整合**

### 📋 **可用指令**

```bash
# 🔍 完整 Terraform 檢查（推薦）
make tf-check

# 個別檢查項目
make tf-init      # Terraform 初始化
make tf-fmt       # 格式化檢查
make tf-validate  # 語法驗證
make tf-plan      # 計畫檢查

# 部署前檢查（會自動執行 tf-check）
make deploy-dev   # 開發環境部署
make deploy-prod  # 生產環境部署
```

### 🎯 **建議工作流程**

```bash
# 日常開發流程
make tf-check     # 執行完整檢查
make test         # 執行應用程式測試
git add .         # pre-commit hooks 會自動執行
git commit -m "feat: 新增功能"

# 部署流程
make deploy-dev   # 會自動執行 tf-check
```

---

## 🚀 **3. CI/CD 早期驗證**

### 📊 **驗證流程**

在 GitHub Actions 中，Terraform 驗證會在早期階段執行：

```
1. 📋 detect-changes     ← 偵測變動的服務
2. 🔍 terraform-validation ← 🆕 早期 Terraform 驗證（快速失敗）
3. 🧪 test              ← 應用程式測試
4. 🚀 deploy            ← 部署到 GCP
```

### 🔧 **驗證項目**

**早期驗證階段**：

- ✅ **格式檢查** (`terraform fmt -check`)
- ✅ **語法驗證** (`terraform validate`)
- ❌ **快速失敗** - 任何錯誤都會立即停止整個 pipeline

**部署階段**：

- ✅ **初始化** (`terraform init`)
- ✅ **計畫檢查** (`terraform plan`)
- ✅ **實際部署** (`terraform apply`)

### 💡 **優勢**

- **⚡ 快速失敗**: Terraform 錯誤在 2-3 分鐘內就能發現
- **💰 節省成本**: 避免執行昂貴的測試和建置步驟
- 🔄 **並行處理**: 多個服務可以同時進行驗證

---

## 🚨 **故障排解**

### ❌ **常見問題**

#### 1. **Pre-commit 安裝失敗**

```bash
# 解決方案
pip install pre-commit
make pre-commit-setup
```

#### 2. **Terraform 格式化錯誤**

```bash
# 自動修正格式
make tf-fmt

# 或手動執行
cd terraform && terraform fmt -recursive
```

#### 3. **Terraform 語法錯誤**

```bash
# 檢查具體錯誤
make tf-validate

# 檢查檔案語法
cd terraform && terraform validate
```

#### 4. **CI/CD 驗證失敗**

```bash
# 本地重現問題
make tf-check

# 檢查特定檔案
cd terraform
terraform init -backend=false
terraform validate
```

### 🔍 **除錯技巧**

```bash
# 查看詳細的 Terraform 錯誤
cd terraform
TF_LOG=DEBUG terraform validate

# 檢查 Terraform 版本一致性
terraform version

# 驗證 pre-commit hooks 配置
pre-commit validate-config
```

---

## 📊 **效果統計**

實作這套驗證機制後，預期可以達到：

- 🎯 **90%+ 錯誤提前發現** - 在本地就能發現大部分問題
- ⚡ **CI/CD 執行時間減少 50%** - 避免不必要的測試和建置
- 💰 **雲端資源成本節省 30%** - 減少失敗的部署嘗試
- 🔄 **開發體驗提升** - 更快的反饋循環

---

## 🚀 **最佳實踐**

### ✅ **推薦做法**

1. **總是使用 pre-commit hooks**

   ```bash
   make pre-commit-setup  # 專案設定完就執行
   ```

2. **部署前執行完整檢查**

   ```bash
   make tf-check  # 而不是直接 make deploy-dev
   ```

3. **保持 Terraform 版本一致性**

   - 本地: 使用 `.terraform-version` 或 `tfenv`
   - CI/CD: 在 workflow 中明確指定版本

4. **定期更新工具版本**
   ```bash
   pre-commit autoupdate  # 更新 pre-commit hooks
   ```

### ❌ **避免做法**

1. ❌ **跳過 pre-commit hooks** (除非緊急情況)
2. ❌ **直接在 CI/CD 中測試 Terraform 變更**
3. ❌ **忽略格式化錯誤**
4. ❌ **在生產環境中實驗 Terraform 語法**

---

## 📚 **相關文件**

- [Pre-commit 官方文檔](https://pre-commit.com/)
- [Terraform 官方文檔](https://terraform.io/docs)
- [pre-commit-terraform](https://github.com/antonbabenko/pre-commit-terraform)
- [專案部署指南](DEPLOYMENT.md)

---

💡 **提示**: 這套機制設計的核心理念是「越早發現問題，修復成本越低」。建議開發者養成習慣，在每次修改 Terraform 檔案後都執行 `make tf-check`。
