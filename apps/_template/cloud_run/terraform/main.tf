# {{SERVICE_NAME}} Terraform 配置
# Cloud Run 服務部署

terraform {
  required_version = ">= 1.12.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/{{SERVICE_NAME_KEBAB}}"
  }
}

# 引用共用基礎設施的資料
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
  workspace = var.environment
}

# 本地變數
locals {
  shared_outputs = data.terraform_remote_state.shared.outputs

  service_full_name = "${var.service_name}-${var.environment}"

  common_labels = {
    service     = var.service_name
    environment = var.environment
    managed_by  = "terraform"
    team        = "data-team"
  }

  env_vars = {
    ENVIRONMENT      = var.environment
    PROJECT_ID       = local.shared_outputs.project_id
    BIGQUERY_DATASET = "integrated_event"
    SERVICE_NAME     = var.service_name
  }
}

# Google Cloud Provider
provider "google" {
  project = local.shared_outputs.project_id
  region  = local.shared_outputs.region
}

# Cloud Run 服務
resource "google_cloud_run_service" "main" {
  name     = local.service_full_name
  location = local.shared_outputs.region

  template {
    metadata {
      labels = local.common_labels

      annotations = {
        "autoscaling.knative.dev/minScale"  = var.min_instances
        "autoscaling.knative.dev/maxScale"  = var.max_instances
        "run.googleapis.com/cpu-throttling" = "false"
      }
    }

    spec {
      container_concurrency = 100
      timeout_seconds       = 300

      containers {
        image = var.container_image

        resources {
          limits = {
            cpu    = var.cpu_limit
            memory = var.memory_limit
          }
        }

        dynamic "env" {
          for_each = local.env_vars
          content {
            name  = env.key
            value = env.value
          }
        }

        # 健康檢查
        liveness_probe {
          http_get {
            path = "/health"
            port = 8080
          }
          initial_delay_seconds = 30
          period_seconds        = 30
          timeout_seconds       = 5
        }

        # 準備檢查
        startup_probe {
          http_get {
            path = "/health"
            port = 8080
          }
          initial_delay_seconds = 0
          period_seconds        = 10
          timeout_seconds       = 5
          failure_threshold     = 10
        }

        ports {
          container_port = 8080
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  lifecycle {
    ignore_changes = [
      template[0].metadata[0].annotations["run.googleapis.com/operation-id"]
    ]
  }
}

# 安全的 IAM 設定 - 注意：實際使用時需要引用 shared infrastructure 的 service account
# 範例：member = "serviceAccount:${data.terraform_remote_state.shared.outputs.service_account_email}"
resource "google_cloud_run_service_iam_member" "service_account_invoker" {
  service  = google_cloud_run_service.main.name
  location = google_cloud_run_service.main.location
  role     = "roles/run.invoker"
  member   = "serviceAccount:{{REPLACE_WITH_SHARED_SERVICE_ACCOUNT_EMAIL}}"
}

# 開發環境可選：允許開發者直接存取 (僅限 dev 環境)
resource "google_cloud_run_service_iam_member" "dev_access" {
  count = var.environment == "dev" ? 1 : 0

  service  = google_cloud_run_service.main.name
  location = google_cloud_run_service.main.location
  role     = "roles/run.invoker"
  member   = "user:${var.developer_email}"
}

# Firestore 資料庫 (如果需要專用資料庫)
resource "google_firestore_database" "main" {
  count       = var.environment == "prod" ? 1 : 0
  project     = local.shared_outputs.project_id
  name        = "${var.service_name}-${var.environment}"
  location_id = local.shared_outputs.region
  type        = "FIRESTORE_NATIVE"
}

# BigQuery 資料集權限
resource "google_bigquery_dataset_iam_member" "editor" {
  dataset_id = "integrated_event"
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${google_cloud_run_service.main.template[0].spec[0].service_account_name}"
}

# Cloud Scheduler (定期同步工作) - 使用安全認證
resource "google_cloud_scheduler_job" "sync_job" {
  name             = "${local.service_full_name}-sync"
  description      = "${var.service_name} 定期同步工作"
  schedule         = "*/5 * * * *" # 每 5 分鐘執行一次
  time_zone        = "Asia/Taipei"
  attempt_deadline = "300s"

  http_target {
    http_method = "POST"
    uri         = "${google_cloud_run_service.main.status[0].url}/sync"

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      batch_size = 1000
    }))

    # 使用 OIDC token 進行安全認證 - 注意：實際使用時需要替換 service account
    oidc_token {
      service_account_email = "{{REPLACE_WITH_SHARED_SERVICE_ACCOUNT_EMAIL}}"
      audience              = google_cloud_run_service.main.status[0].url
    }
  }

  retry_config {
    retry_count = 3
  }

  depends_on = [
    google_cloud_run_service_iam_member.service_account_invoker
  ]
}

# 監控 - Uptime 檢查
resource "google_monitoring_uptime_check_config" "main" {
  display_name = "${local.service_full_name}-uptime"
  timeout      = "10s"
  period       = "60s"

  http_check {
    path         = "/health"
    port         = "443"
    use_ssl      = true
    validate_ssl = true
  }

  monitored_resource {
    type = "uptime_url"
    labels = {
      project_id = local.shared_outputs.project_id
      host       = replace(google_cloud_run_service.main.status[0].url, "https://", "")
    }
  }
}

# ====== 成本監控 ======
# 基本的成本告警 - 當月費用超過 $30 時通知（模板默認值較小）
resource "google_billing_budget" "service_budget" {
  count = var.environment == "prod" && var.billing_account_id != null ? 1 : 0

  billing_account = var.billing_account_id
  display_name    = "${var.service_name} - Monthly Budget"

  budget_filter {
    projects = ["projects/${local.shared_outputs.project_id}"]
    labels = {
      service = var.service_name
    }
  }

  amount {
    specified_amount {
      currency_code = "USD"
      units         = "30"
    }
  }

  threshold_rules {
    threshold_percent = 0.8 # 80% 時警告
    spend_basis       = "CURRENT_SPEND"
  }

  threshold_rules {
    threshold_percent = 1.0 # 100% 時警告
    spend_basis       = "FORECASTED_SPEND"
  }
}

# 輸出
output "service_url" {
  description = "Cloud Run 服務 URL"
  value       = google_cloud_run_service.main.status[0].url
}

output "service_name" {
  description = "Cloud Run 服務名稱"
  value       = google_cloud_run_service.main.name
}

output "deployment_version" {
  description = "Currently deployed version of the service."
  value       = var.deployment_version
}

output "revision" {
  description = "目前部署的版本"
  value       = google_cloud_run_service.main.status[0].latest_ready_revision_name
}

output "security_status" {
  description = "Security configuration status."
  value = {
    public_access_removed = "✅ Service requires authentication"
    scheduler_auth        = "✅ Cloud Scheduler uses OIDC authentication"
    cost_monitoring       = var.environment == "prod" && var.billing_account_id != null ? "✅ Cost monitoring enabled" : "⚠️ Cost monitoring disabled"
    template_notes        = "⚠️ Remember to replace {{REPLACE_WITH_SHARED_SERVICE_ACCOUNT_EMAIL}} with actual service account"
  }
}
