# {{SERVICE_NAME}} Variables
# 所有可配置的變數定義

variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
}

variable "environment" {
  description = "環境名稱 (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev, staging, 或 prod"
  }
}

variable "region" {
  description = "GCP 區域"
  type        = string
  default     = "asia-east1"
}

variable "service_name" {
  description = "服務名稱"
  type        = string
  default     = "{{SERVICE_NAME_KEBAB}}"
}

variable "deployment_version" {
  description = "Deployment version for source code"
  type        = string
  default     = "latest"
}

variable "container_image" {
  description = "Container 映像 URL"
  type        = string
}

variable "min_instances" {
  description = "最小實例數"
  type        = number
  default     = 0
}

variable "max_instances" {
  description = "最大實例數"
  type        = number
  default     = 10
}

variable "cpu_limit" {
  description = "CPU 限制"
  type        = string
  default     = "1000m"
}

variable "memory_limit" {
  description = "記憶體限制"
  type        = string
  default     = "512Mi"
}

# ====== 成本監控變數 ======
variable "billing_account_id" {
  description = "Billing account ID for cost monitoring"
  type        = string
  default     = null # 可選，只在 prod 環境啟用成本監控時需要
}

# ====== 開發者存取設定 ======
variable "developer_email" {
  description = "Developer email for dev environment access"
  type        = string
  default     = "<EMAIL>" # 請替換成實際的開發者 email
}

# ====== 排程設定 ======
variable "sync_schedule" {
  description = "Cron schedule for sync job"
  type        = string
  default     = "*/5 * * * *" # 每 5 分鐘執行一次，請根據實際需求調整
}
