# Legacy Event Sync pytest 配置

[pytest]
# 測試目錄
testpaths = tests

# 測試檔案模式
python_files = test_*.py *_test.py

# 測試類別模式
python_classes = Test*

# 測試函數模式
python_functions = test_*

# 最小版本
minversion = 7.0

# 額外選項
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# 標記定義
markers =
    unit: 單元測試 (快速, 使用 mock)
    integration: 整合測試 (使用真實 BigQuery)
    e2e: 端到端測試
    slow: 較慢的測試
    external: 需要外部服務的測試
    mock: 使用 mock 的測試

# 過濾警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 測試發現
norecursedirs =
    .git
    .tox
    dist
    build
    *.egg
    venv
    env

# 支援真實 BigQuery 測試的環境變數
env =
    PROJECT_ID=tagtoo-tracking
    BIGQUERY_DATASET=temp
    ENVIRONMENT=test

# Coverage 設定
[coverage:run]
# 資料檔案位置 - 使用相對路徑避免權限問題
data_file = .coverage
# 包含測試的檔案
source = src
# 排除不需要測試覆蓋的檔案
omit =
    */tests/*
    */venv/*
    */.*

[coverage:report]
# 報告設定
precision = 2
show_missing = True
skip_covered = False
skip_empty = False
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError

[coverage:html]
# HTML 報告設定
directory = htmlcov
title = Legacy Event Sync Coverage Report

[coverage:xml]
# XML 報告設定
output = coverage.xml
