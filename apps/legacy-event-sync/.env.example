# Legacy Event Sync Service 環境變數配置範例

# 基本服務配置
SERVICE_NAME=legacy-event-sync
ENVIRONMENT=dev
DEBUG=true
LOG_LEVEL=INFO
PORT=8080

# Google Cloud 配置
PROJECT_ID=tagtoo-tracking
BIGQUERY_DATASET=event_test
FIRESTORE_DATABASE=(default)

# 資料來源配置
SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
TARGET_TABLE=tagtoo-tracking.event_test.integrated_event

# 分時段處理配置（基於實際資料量分析）
BATCH_SIZE=200000
HOURS_PER_SEGMENT=4
MAX_CONCURRENT_SEGMENTS=6

# Cloud Function 資源配置
MEMORY_MB=4096
CPU_COUNT=4

# 處理配置
SYNC_INTERVAL_MINUTES=360
MAX_RETRIES=3
RETRY_DELAY_SECONDS=30

# 監控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 安全配置（生產環境請更改）
SECRET_KEY=dev-secret-key-change-in-production
