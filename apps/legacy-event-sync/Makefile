# Legacy Event Sync Service - Makefile
# 常用開發指令

.PHONY: help dev-setup dev-up dev-down dev-clean dev-logs dev-shell test test-unit test-		echo "🧪 執行測試..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages pytest -v --cov=src --cov-report=term-missing";tch lint format type-check build deploy-dev deploy-prod shell status health-check sync-manual check-data check-schema auth-setup auth-check clean clean-all info tf-check tf-validate tf-fmt tf-plan tf-init pre-commit-setup pre-commit-run deploy-shared-infrastructure

# 顯示幫助資訊
help:
	@echo "Legacy Event Sync Service - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  dev-setup     - 設定開發環境"
	@echo "  dev-up        - 啟動開發環境（使用真實 BigQuery + 測試表格）"
	@echo "  dev-down      - 停止開發環境"
	@echo "  dev-clean     - 清理本地環境"
	@echo "  dev-logs      - 查看服務日誌"
	@echo "  dev-shell     - 進入容器 shell"
	@echo ""
	@echo "Testing:"
	@echo "  test          - 運行所有測試"
	@echo "  test-unit     - 運行單元測試"
	@echo "  test-integration - 運行整合測試（真實 BigQuery）"
	@echo "  test-env-check - 檢查測試環境設定"
	@echo "  test-watch    - 監看模式運行測試"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint          - 代碼檢查"
	@echo "  format        - 格式化代碼"
	@echo "  type-check    - 型別檢查"
	@echo "  pre-commit-setup - 設定 pre-commit hooks"
	@echo "  pre-commit-run   - 執行 pre-commit 檢查"
	@echo ""
	@echo "Terraform:"
	@echo "  tf-check      - 完整 Terraform 檢查（推薦）"
	@echo "  tf-validate   - Terraform 語法驗證"
	@echo "  tf-fmt        - Terraform 格式化"
	@echo "  tf-plan       - Terraform 計畫檢查"
	@echo "  tf-init       - Terraform 初始化"
	@echo ""
	@echo "Infrastructure:"
	@echo "  deploy-shared-infrastructure - 部署共用基礎設施（必須在應用部署前執行）"
	@echo ""
	@echo "Build & Deploy:"
	@echo "  build         - 建置 Docker 映像"
	@echo "  deploy-dev    - 部署到開發環境"
	@echo "  deploy-prod   - 部署到生產環境"
	@echo ""
	@echo "Database & Sync:"
	@echo "  sync-manual                      - 手動觸發同步"
	@echo "  check-sync-status                - 檢查今天的同步狀態"
	@echo "  check-sync-status-cost           - 檢查今天的同步狀態（僅成本分析）"
	@echo "  check-sync-status-date           - 檢查特定日期的同步狀態 (需要 DATE=YYYY-MM-DD)"
	@echo "  check-sync-status-date-cost      - 檢查特定日期的成本分析 (需要 DATE=YYYY-MM-DD)"
	@echo "  check-data                       - 檢查資料狀態（基本健康檢查）"
	@echo "  check-schema                     - 檢查 BigQuery Schema"
	@echo ""
	@echo "Tools:"
	@echo "  shell         - 進入容器 shell"
	@echo "  status        - 查看容器狀態"
	@echo "  health-check  - 健康檢查"
	@echo ""
	@echo "Authentication:"
	@echo "  auth-setup    - 設定本地認證（ADC）"
	@echo "  auth-check    - 檢查認證狀態"
	@echo ""
	@echo "Cleanup:"
	@echo "  clean         - 清理開發環境"
	@echo "  clean-all     - 完全清理（包括映像）"
	@echo ""
	@echo "Info:"
	@echo "  info          - 顯示專案資訊"
	@echo ""
	@echo "Use 'make <command>' to run a specific command."

# 設定開發環境
dev-setup:
	@echo "Setting up development environment..."
	@docker --version || (echo "Please install Docker Desktop" && exit 1)
	@docker-compose --version || (echo "Please install Docker Compose" && exit 1)
	@echo "Development environment setup complete!"

# 啟動本地服務
dev-up:  ## 啟動開發環境（使用真實 BigQuery）
	@echo "🚀 啟動開發環境（真實 BigQuery + 測試表格）..."
	@$(MAKE) auth-check
	docker-compose up -d
	@echo "✅ 開發環境已啟動:"
	@echo "   - 應用程式: http://localhost:8080"
	@echo "   - 來源表格: tagtoo-tracking.event_prod.tagtoo_event"
	@echo "   - 測試表格: tagtoo-tracking.event_test.integrated_event"
	@echo ""
	@echo "🔍 服務健康檢查:"
	@sleep 3 && curl -f http://localhost:8080/health 2>/dev/null && echo "✅ 服務運行正常" || echo "❌ 服務啟動中..."

# 停止本地服務
dev-down:
	@echo "Stopping local development services..."
	@docker-compose down
	@echo "Services stopped!"

# 清理本地環境
dev-clean:
	@echo "Cleaning up local environment..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "Local environment cleaned!"

# 查看服務日誌
dev-logs:
	@docker-compose logs -f legacy-event-sync

# 進入容器 shell
dev-shell:
	@docker-compose exec legacy-event-sync bash

# 運行所有測試
test:
	@echo "Running all tests..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		echo "📋 建置映像..."; \
		DOCKER_BUILDKIT=1 BUILDKIT_PROGRESS=plain docker-compose -f docker-compose.yml -f docker-compose.ci.yml build --no-cache --parallel; \
		echo "📋 啟動服務..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d; \
		echo "📊 檢查容器狀態..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps; \
		echo "📝 顯示容器日誌..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs --tail=50; \
		echo "⏰ 等待服務啟動..."; \
		sleep 30; \
		echo "🔍 再次檢查容器狀態..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps; \
		if ! docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps | grep -q "Up"; then \
			echo "❌ 容器啟動失敗，顯示詳細日誌:"; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
			exit 1; \
		fi; \
		echo "🧪 執行測試..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages pytest -v --cov=src --cov-report=term-missing --cov-report=xml --cov-report=html --junitxml=pytest-report.xml"; \
		TEST_EXIT_CODE=$$?; \
		echo "📋 測試完成，清理容器..."; \
		if [ $$TEST_EXIT_CODE -ne 0 ]; then \
			echo "❌ 測試失敗，顯示最終日誌:"; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs --tail=100; \
		fi; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
		exit $$TEST_EXIT_CODE; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T legacy-event-sync pytest -v --cov=src --cov-report=term-missing --cov-report=xml --cov-report=html --junitxml=pytest-report.xml; \
	fi

# 運行單元測試
test-unit:
	@echo "Running unit tests..."
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		echo "📋 啟動服務..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d --build; \
		echo "📊 檢查容器狀態..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps; \
		echo "⏰ 等待服務啟動..."; \
		sleep 30; \
		if ! docker-compose -f docker-compose.yml -f docker-compose.ci.yml ps | grep -q "Up"; then \
			echo "❌ 容器啟動失敗，顯示詳細日誌:"; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
			exit 1; \
		fi; \
		echo "🧪 執行單元測試..."; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages pytest -v -m unit --tb=short"; \
		TEST_EXIT_CODE=$$?; \
		if [ $$TEST_EXIT_CODE -ne 0 ]; then \
			echo "❌ 測試失敗，顯示最終日誌:"; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml logs --tail=100; \
		fi; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
		exit $$TEST_EXIT_CODE; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T legacy-event-sync pytest -v -m unit --tb=short; \
	fi

# 監看模式運行測試
test-watch:
	@echo "Running tests in watch mode..."
	@docker-compose exec legacy-event-sync pytest-watch --runner "pytest -v"

# 運行整合測試（使用真實 BigQuery）
test-integration:
	@echo "🔍 執行整合測試（使用真實 BigQuery temp dataset）..."
	@echo "⚠️  注意：這些測試會連接真實的 BigQuery 並在 temp dataset 中建立/刪除測試表格"
	@$(MAKE) auth-check
	@if [ -n "$$GITHUB_ACTIONS" ] || [ -n "$$CI" ]; then \
		echo "🐳 CI 環境：使用 CI 適配的 Docker Compose"; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml up -d --build; \
		sleep 30; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages python tests/test_environment.py"; \
		if [ $$? -eq 0 ]; then \
			echo "📊 環境檢查通過，執行整合測試..."; \
			docker-compose -f docker-compose.yml -f docker-compose.ci.yml exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages pytest tests/test_integration.py -v --tb=short"; \
		else \
			echo "❌ 環境檢查失敗，略過整合測試"; \
		fi; \
		docker-compose -f docker-compose.yml -f docker-compose.ci.yml down; \
	else \
		echo "🖥️  本地環境：使用標準 Docker Compose"; \
		docker-compose exec -T legacy-event-sync python tests/test_environment.py; \
		if [ $$? -eq 0 ]; then \
			echo "📊 環境檢查通過，執行整合測試..."; \
			docker-compose exec -T legacy-event-sync pytest tests/test_integration.py -v --tb=short; \
		else \
			echo "❌ 環境檢查失敗，略過整合測試"; \
		fi; \
	fi

# 檢查測試環境設定
test-env-check:
	@echo "🔍 檢查測試環境設定..."
	@$(MAKE) auth-check
	@echo "🐳 在容器中執行環境檢查..."
	@docker-compose exec -T legacy-event-sync su appuser -c "PATH=/home/<USER>/.local/bin:\$$PATH PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages python tests/test_environment.py"

# 代碼檢查
lint:
	@echo "Running code quality checks..."
	@docker-compose exec -T legacy-event-sync flake8 src/ tests/
	@docker-compose exec -T legacy-event-sync pylint src/ tests/ || true
	@echo "Code quality checks passed!"

# 格式化代碼
format:
	@echo "Formatting code..."
	@docker-compose exec -T legacy-event-sync black src/ tests/
	@docker-compose exec -T legacy-event-sync isort src/ tests/
	@echo "Code formatted!"

# 型別檢查
type-check:
	@echo "Running type checks..."
	@docker-compose exec -T legacy-event-sync mypy src/

# 建置 Docker 映像
build:
	@echo "Building Docker image..."
	@docker build -t legacy-event-sync:latest .
	@echo "Docker image built successfully!"

# 檢查 BigQuery Schema
check-schema:
	@echo "Checking BigQuery schema files..."
	@echo "=================================="
	@echo ""
	@echo "📁 Schema files location: ../../infrastructure/terraform/schema/"
	@ls -la ../../infrastructure/terraform/schema/ 2>/dev/null || (echo "❌ Schema directory not found" && exit 1)
	@echo ""
	@echo "🔍 Validating schema files..."
	@echo ""
	@if [ -f "../../infrastructure/terraform/schema/tagtoo_event.json" ]; then \
		echo "✅ tagtoo_event.json (source schema):"; \
		echo "   Fields: $$(cat ../../infrastructure/terraform/schema/tagtoo_event.json | jq '. | length') fields"; \
		echo "   Format: $$(cat ../../infrastructure/terraform/schema/tagtoo_event.json | jq . > /dev/null 2>&1 && echo 'Valid JSON' || echo 'Invalid JSON')"; \
		echo "   Size: $$(wc -c < ../../infrastructure/terraform/schema/tagtoo_event.json) bytes"; \
	else \
		echo "❌ tagtoo_event.json not found"; \
	fi
	@echo ""
	@if [ -f "../../infrastructure/terraform/schema/integrated_event.json" ]; then \
		echo "✅ integrated_event.json (target schema):"; \
		echo "   Fields: $$(cat ../../infrastructure/terraform/schema/integrated_event.json | jq '. | length') fields"; \
		echo "   Format: $$(cat ../../infrastructure/terraform/schema/integrated_event.json | jq . > /dev/null 2>&1 && echo 'Valid JSON' || echo 'Invalid JSON')"; \
		echo "   Size: $$(wc -c < ../../infrastructure/terraform/schema/integrated_event.json) bytes"; \
	else \
		echo "❌ integrated_event.json not found"; \
	fi
	@echo ""
	@echo "📊 Schema comparison:"
	@echo "   Source table: tagtoo-tracking.event_prod.tagtoo_event"
	@echo "   Target table: tagtoo-tracking.event_test.integrated_event"
	@echo ""
	@echo "🔧 Comparing with actual BigQuery tables..."
	@echo ""
	@echo "📋 Source table (tagtoo_event) detailed comparison:"
	@if command -v bq >/dev/null 2>&1; then \
		echo "   Fetching actual schema from BigQuery..."; \
		if bq show --schema --format=prettyjson tagtoo-tracking:event_prod.tagtoo_event > /tmp/bq_tagtoo_event.json 2>/dev/null; then \
			ACTUAL_FIELDS=$$(cat /tmp/bq_tagtoo_event.json | jq '. | length'); \
			LOCAL_FIELDS=$$(cat ../../infrastructure/terraform/schema/tagtoo_event.json | jq '. | length'); \
			echo "   ✅ BigQuery connection successful"; \
			echo "   📊 Field count - Local: $$LOCAL_FIELDS, BigQuery: $$ACTUAL_FIELDS"; \
			if [ "$$LOCAL_FIELDS" = "$$ACTUAL_FIELDS" ]; then \
				echo "   ✅ Field count matches!"; \
				python3 scripts/compare_schema.py ../../infrastructure/terraform/schema/tagtoo_event.json /tmp/bq_tagtoo_event.json; \
			else \
				echo "   ⚠️  Field count mismatch!"; \
			fi; \
			rm -f /tmp/bq_tagtoo_event.json; \
		else \
			echo "   ❌ Failed to fetch schema (check permissions/table exists)"; \
		fi; \
	else \
		echo "   ⚠️  bq CLI not installed - install with: gcloud components install bq"; \
	fi
	@echo ""
	@echo "📋 Target table (integrated_event) detailed comparison:"
	@if command -v bq >/dev/null 2>&1; then \
		echo "   Fetching actual schema from BigQuery..."; \
		if bq show --schema --format=prettyjson tagtoo-tracking:event_test.integrated_event > /tmp/bq_integrated_event.json 2>/dev/null; then \
			ACTUAL_FIELDS=$$(cat /tmp/bq_integrated_event.json | jq '. | length'); \
			LOCAL_FIELDS=$$(cat ../../infrastructure/terraform/schema/integrated_event.json | jq '. | length'); \
			echo "   ✅ BigQuery connection successful"; \
			echo "   📊 Field count - Local: $$LOCAL_FIELDS, BigQuery: $$ACTUAL_FIELDS"; \
			if [ "$$LOCAL_FIELDS" = "$$ACTUAL_FIELDS" ]; then \
				echo "   ✅ Field count matches!"; \
				python3 scripts/compare_schema.py ../../infrastructure/terraform/schema/integrated_event.json /tmp/bq_integrated_event.json; \
			else \
				echo "   ⚠️  Field count mismatch!"; \
			fi; \
			rm -f /tmp/bq_integrated_event.json; \
		else \
			echo "   ❌ Failed to fetch schema (check permissions/table exists)"; \
		fi; \
	else \
		echo "   ⚠️  bq CLI not installed - install with: gcloud components install bq"; \
	fi
	@echo ""
	@echo "💡 Manual commands for detailed comparison:"
	@echo "   bq show --schema --format=prettyjson tagtoo-tracking:event_prod.tagtoo_event"
	@echo "   bq show --schema --format=prettyjson tagtoo-tracking:event_test.integrated_event"

# 手動觸發同步
sync-manual:
	@echo "Triggering manual sync (starting coordinator)..."
	@curl -X POST http://localhost:8080/start-sync \
		-H "Content-Type: application/json" \
		-d '{"start_date": "2024-01-01T00:00:00", "end_date": "2024-01-01T05:00:00"}'

# 檢查同步狀態
check-sync-status:
	@echo "Checking sync status..."
	@curl -s http://localhost:8080/sync-status | jq . || echo "Make sure services are running"

# 檢查同步狀態並顯示成本分析
check-sync-status-cost:
	@echo "Checking sync status with cost analysis..."
	@curl -s http://localhost:8080/sync-status | jq '.bigquery_analysis' || echo "Make sure services are running"

# 檢查特定日期的同步狀態
check-sync-status-date:
	@echo "Checking sync status for specific date..."
	@if [ -z "$(DATE)" ]; then \
		echo "Usage: make check-sync-status-date DATE=YYYY-MM-DD"; \
		echo "Example: make check-sync-status-date DATE=2024-01-15"; \
		exit 1; \
	fi
	@curl -s "http://localhost:8080/sync-status?date=$(DATE)" | jq . || echo "Make sure services are running"

# 檢查特定日期的成本分析
check-sync-status-date-cost:
	@echo "Checking sync status cost analysis for specific date..."
	@if [ -z "$(DATE)" ]; then \
		echo "Usage: make check-sync-status-date-cost DATE=YYYY-MM-DD"; \
		echo "Example: make check-sync-status-date-cost DATE=2024-01-15"; \
		exit 1; \
	fi
	@curl -s "http://localhost:8080/sync-status?date=$(DATE)" | jq '.bigquery_analysis' || echo "Make sure services are running"

# 檢查資料狀態（保留向後相容性）
check-data:
	@echo "Checking data status..."
	@curl -s http://localhost:8080/health | jq . || echo "Make sure services are running"

# ====== Terraform 相關指令 ======

# 完整 Terraform 檢查（推薦使用）
tf-check:
	@echo "🔍 執行完整 Terraform 檢查..."
	@$(MAKE) tf-init
	@$(MAKE) tf-fmt
	@$(MAKE) tf-validate
	@echo "✅ Terraform 檢查全部通過！"

# Terraform 初始化
tf-init:
	@echo "🚀 初始化 Terraform..."
	@cd terraform && terraform init -backend=false
	@echo "✅ Terraform 初始化完成"

# Terraform 格式化
tf-fmt:
	@echo "🎨 格式化 Terraform 檔案..."
	@cd terraform && terraform fmt -recursive -check || (echo "❌ 格式化檢查失敗，執行自動格式化..." && terraform fmt -recursive)
	@echo "✅ Terraform 格式化完成"

# Terraform 語法驗證
tf-validate:
	@echo "🔍 驗證 Terraform 語法..."
	@cd terraform && terraform validate
	@echo "✅ Terraform 語法驗證通過"

# Terraform 計畫檢查（乾跑）
tf-plan:
	@echo "📋 執行 Terraform 計畫檢查..."
	@cd terraform && terraform init
	@echo "🔍 開發環境計畫檢查:"
	@cd terraform && terraform plan -var="environment=dev" -out=tfplan-dev
	@echo "✅ Terraform 計畫檢查完成，計畫檔案已儲存為 tfplan-dev"

# ====== Pre-commit 相關指令 ======

# 設定 pre-commit hooks
pre-commit-setup:
	@echo "🔧 設定 pre-commit hooks..."
	@if ! command -v pre-commit >/dev/null 2>&1; then \
		echo "⚠️  pre-commit 未安裝，正在安裝..."; \
		pip install pre-commit; \
	fi
	@pre-commit install
	@pre-commit install --hook-type commit-msg
	@echo "✅ Pre-commit hooks 設定完成"
	@echo ""
	@echo "💡 現在當你執行 git commit 時，會自動執行："
	@echo "   - 程式碼格式化 (black, isort)"
	@echo "   - 程式碼品質檢查 (flake8)"
	@echo "   - Terraform 格式化和驗證"
	@echo "   - JSON/YAML 格式檢查"

# 手動執行 pre-commit 檢查
pre-commit-run:
	@echo "🔍 執行 pre-commit 檢查..."
	@if ! command -v pre-commit >/dev/null 2>&1; then \
		echo "❌ pre-commit 未安裝，請執行: make pre-commit-setup"; \
		exit 1; \
	fi
	@pre-commit run --all-files
	@echo "✅ Pre-commit 檢查完成"

# ====== 基礎設施部署指令 ======

# 部署共用基礎設施
deploy-shared-infrastructure:
	@echo "🚀 部署共用基礎設施..."
	@echo "⚠️  注意：這會部署 shared infrastructure，包含 Service Account、BigQuery Dataset 等資源"
	@echo ""
	@$(MAKE) auth-check
	@if [ ! -d "../../infrastructure/terraform/shared" ]; then \
		echo "❌ 找不到 shared infrastructure 目錄"; \
		exit 1; \
	fi
	@echo "📁 切換到 shared infrastructure 目錄..."
	@cd ../../infrastructure/terraform/shared && \
		if [ -x "./deploy.sh" ]; then \
			echo "🔧 使用部署腳本..."; \
			./deploy.sh prod; \
		else \
			echo "🔧 手動執行 Terraform..."; \
			terraform init && \
			terraform workspace select prod 2>/dev/null || terraform workspace new prod && \
			terraform plan -var="environment=prod" && \
			terraform apply -var="environment=prod"; \
		fi
	@echo ""
	@echo "✅ 共用基礎設施部署完成！"
	@echo ""
	@echo "🔗 接下來可以部署應用服務："
	@echo "  make deploy-prod"

# ====== 部署相關指令 ======

# 部署到開發環境
deploy-dev:
	@echo "🚀 部署到開發環境..."
	@echo "🔍 執行部署前檢查..."
	@$(MAKE) tf-check
	@echo "📋 檢查共用基礎設施是否存在..."
	@if ! cd ../../infrastructure/terraform/shared && terraform init > /dev/null 2>&1 && terraform workspace select dev > /dev/null 2>&1 && terraform show > /dev/null 2>&1; then \
		echo "⚠️  共用基礎設施未部署，正在部署..."; \
		cd ../../infrastructure/terraform/shared && \
		(terraform workspace select dev 2>/dev/null || terraform workspace new dev) && \
		terraform plan -var="environment=dev" && \
		terraform apply -var="environment=dev" -auto-approve; \
	else \
		echo "✅ 共用基礎設施已存在"; \
	fi
	@echo "📋 執行 Terraform 部署..."
	@cd terraform && terraform init
	@cd terraform && terraform workspace select dev 2>/dev/null || terraform workspace new dev
	@cd terraform && terraform plan -var="environment=dev"
	@cd terraform && terraform apply -var="environment=dev" -auto-approve
	@echo "✅ 開發環境部署完成"

# 部署到生產環境
deploy-prod:
	@echo "🚀 部署到生產環境..."
	@echo "🔍 執行部署前檢查..."
	@$(MAKE) tf-check
	@echo "📋 檢查共用基礎設施是否存在..."
	@if ! cd ../../infrastructure/terraform/shared && terraform init > /dev/null 2>&1 && terraform workspace select prod > /dev/null 2>&1 && terraform show > /dev/null 2>&1; then \
		echo "❌ 共用基礎設施未部署！"; \
		echo "請先執行: make deploy-shared-infrastructure"; \
		exit 1; \
	else \
		echo "✅ 共用基礎設施已存在"; \
	fi
	@echo "📋 執行 Terraform 計畫..."
	@cd terraform && terraform init
	@cd terraform && terraform workspace select prod 2>/dev/null || terraform workspace new prod
	@cd terraform && terraform plan -var="environment=prod"
	@echo ""
	@echo "⚠️  請仔細檢查上述計畫，確認無誤後手動執行："
	@echo "   cd terraform && terraform apply -var=\"environment=prod\""
	@echo ""
	@echo "�� 生產環境部署需要手動確認以確保安全性"

# 查看容器狀態
status:
	@docker-compose ps
	@docker-compose top

# 健康檢查
health-check:
	@echo "Checking service health..."
	@curl -s http://localhost:8080/health | jq . || echo "Make sure services are running with 'make dev-up'"

# 設定本地認證
auth-setup:
	@echo "🔐 設定 Google Cloud 認證..."
	@echo "請執行以下指令進行認證:"
	@echo ""
	@echo "  gcloud auth application-default login"
	@echo ""
	@echo "認證完成後，ADC 檔案會自動掛載到容器中。"

# 檢查認證狀態
auth-check:
	@echo "🔍 檢查認證狀態..."
	@if [ ! -f ~/.config/gcloud/application_default_credentials.json ]; then \
		echo "❌ 未找到 ADC 檔案"; \
		echo "請執行: make auth-setup"; \
		exit 1; \
	else \
		echo "✅ ADC 檔案存在: ~/.config/gcloud/application_default_credentials.json"; \
	fi
	@if command -v gcloud >/dev/null 2>&1; then \
		echo "📋 當前 gcloud 狀態:"; \
		gcloud auth list --filter=status:ACTIVE --format="table(account,status)" || true; \
	else \
		echo "⚠️  gcloud CLI 未安裝，無法檢查詳細狀態"; \
	fi

# 清理開發環境
clean:
	@echo "🧹 清理開發環境..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "✅ 清理完成"

# 完全清理環境
clean-all:
	@echo "🧹 完全清理環境..."
	$(MAKE) clean
	@docker rmi legacy-event-sync:latest 2>/dev/null || true
	@echo "✅ 完全清理完成"

# 顯示專案資訊
info:
	@echo "📚 Legacy Event Sync Service"
	@echo ""
	@echo "🎯 用途: 同步 tagtoo_event 到 integrated_event"
	@echo "🐳 容器: 基於 Python 3.11"
	@echo "🗃️  資料庫: BigQuery"
	@echo ""
	@echo "🚀 快速開始:"
	@echo "  1. make auth-setup     # 設定認證"
	@echo "  2. make dev-up         # 啟動開發環境"
	@echo "  3. make test           # 執行測試"
	@echo "  4. make sync-manual    # 測試同步功能"
