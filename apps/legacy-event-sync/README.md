# Legacy Event Sync Service

> 此服務用於將現有 BigQuery table `tagtoo-tracking.event_prod.tagtoo_event` 資料同步到整合事件表格 `tagtoo-tracking.event_prod.integrated_event` BigQuery table。

## 📋 專案概述

此服務負責定期（預設每日）將 `tagtoo-tracking.event_prod.tagtoo_event` 的資料轉換並同步到 `tagtoo-tracking.event_prod.integrated_event` BigQuery table。

服務採用 **Cloud Run + Cloud Tasks** 架構：

- **Coordinator (`/start-sync`)**: 由 Cloud Scheduler 觸發，負責計算需同步的時間區間，並為每個區間建立一個 Cloud Task。
- **Worker (`/process-segment`)**: 由 Cloud Task 觸發，負責處理單一時間區間的資料同步。

### 🎯 主要功能

- ✅ **定期同步**: 透過 Cloud Scheduler 定期觸發
- ✅ **非同步任務處理**: 使用 Cloud Tasks 進行分段同步，提高穩定性
- ✅ **資料轉換**: 將舊格式轉換為新的統一格式
- ✅ **資料品質**: 包含基本的資料品質檢查
- ✅ **錯誤處理**: 具備重試機制
- ✅ **監控**: 內建健康檢查端點
- ✅ **同步狀態查詢**: 提供執行結果確認 API

## � **目前部署狀態 (Production)**

### ✅ **基礎設施狀態：**

- **Cloud Run 服務**: `legacy-event-sync-prod` - 運行正常
- **Cloud Scheduler**: 每 4 小時執行一次 (`0 2/4 * * *`) - 已啟用
- **Cloud Tasks 佇列**: `legacy-event-sync-segments-prod` - 運行中
- **認證配置**: 已移除公開存取，使用 Service Account 認證

### 📋 **資料同步狀況：**

- **Partner Source**: `legacy-tagtoo-event`
- **來源表格**: `tagtoo-tracking.event_prod.tagtoo_event` (28,336 萬筆記錄，持續更新中)
- **目標表格**: `tagtoo-tracking.event_prod.integrated_event`
- **同步狀態**: 🔄 服務已部署，待首次完整同步啟動

### 🔧 **待處理項目：**

1. 執行 terraform apply 以套用最新的安全和監控配置
2. 確認首次同步作業正常執行
3. 驗證 `legacy-tagtoo-event` 資料出現在目標表格

### 📈 **監控端點：**

- 健康檢查: `/health` (需要認證)
- 同步狀態: `/sync-status` (需要認證)
- 手動觸發: `/start-sync` (POST, 需要認證)

## 🗂️ 資料欄位對應（摘錄）

來源 `tagtoo_event` ➜ 目標 `integrated_event` 主要欄位對照：
| ---------------------------- | ----------------------- | -------------------------------------- |
| `permanent` | `permanent` | 使用者永久 ID |
| `ec_id` | `ec_id` | 電商平台 ID |
| `event_time` | `event_time` | 事件發生時間 (ISO) |
| `event.name` | `event` | 事件名稱 |
| `event.value` | `value` | 事件價值 |
| `event.currency` | `currency` | 貨幣 |
| `event.custom_data.order_id` | `order_id` | 訂單編號 |
| `event.items[]` | `items[]` | 商品陣列 |
| `location.country_code` | `location.country_code` | 國碼 |
| ... | ... | 其餘完整映射請見 `docs/DEVELOPMENT.md` |

完整的欄位對應與設計原則請參閱 [`docs/DEVELOPMENT.md`](docs/DEVELOPMENT.md)。

## 🚀 快速開始

### 本地開發

```bash
# 進入服務目錄
cd apps/legacy-event-sync

# 設定認證（一次性）
make auth-setup

# 啟動開發環境（使用真實 BigQuery + 測試表格）
make dev-up

# 執行測試
make test

# 測試同步功能
make sync-manual

# 查看服務日誌
make dev-logs

# 停止服務
make dev-down
```

## 🚀 部署

### 手動觸發部署 (GitHub Actions)

現在支援直接從 GitHub Actions 手動觸發部署，無需修改代碼：

1. **前往 GitHub Actions**: [https://github.com/Tagtoo/integrated-event/actions](https://github.com/Tagtoo/integrated-event/actions)
2. **選擇 Workflow**: 點擊 "Apps CI/CD Pipeline"
3. **手動觸發**: 點擊 "Run workflow" 按鈕
4. **配置選項**:
   - **Service**: 輸入 `legacy-event-sync`（或留空部署所有服務）
   - **Environment**: 選擇 `prod` 或 `dev`
5. **開始部署**: 點擊 "Run workflow"

### 其他部署方法

```bash
# 本地部署到開發環境
make deploy-dev

# 本地部署到生產環境（需要手動確認）
make deploy-prod

# 空提交觸發 CI/CD
git commit --allow-empty -m "[deploy] 觸發 legacy-event-sync 部署"
git push origin main
```

### 本地測試 CI/CD (使用 Act)

```bash
# 測試特定 job
act push --env-file apps/legacy-event-sync/act.env -j detect-changes

# 測試完整 workflow
act push --env-file apps/legacy-event-sync/act.env
```

詳細的部署說明請參閱 [`docs/DEPLOYMENT.md`](docs/DEPLOYMENT.md)。

## 📊 API 端點

### 健康檢查

```bash
# 檢查服務健康狀態
curl http://localhost:8080/health
```

### 同步狀態查詢

```bash
# 查詢今天的同步狀態
curl http://localhost:8080/sync-status

# 查詢特定日期的同步狀態
curl "http://localhost:8080/sync-status?date=2024-01-15"
```

**回應範例：**

```json
{
  "date": "2024-01-15",
  "status": "healthy",
  "last_sync_time": "2024-01-15T10:30:00",
  "source_events_count": 12500,
  "target_events_count": 12500,
  "sync_coverage_percent": 100.0,
  "latest_data_timestamp": "2024-01-15T10:25:00",
  "warnings": [],
  "table_info": {
    "source_table": "tagtoo-tracking.event_prod.tagtoo_event",
    "target_table": "tagtoo-tracking.event_test.integrated_event"
  },
  "bigquery_analysis": {
    "total_cost_usd": 0.0125,
    "total_tb_processed": 0.0025,
    "total_execution_time_seconds": 3.2,
    "source_query_stats": {
      "bytes_processed": **********,
      "tb_processed": 0.00125,
      "estimated_cost_usd": 0.00625,
      "execution_time_seconds": 1.8
    },
    "target_query_stats": {
      "bytes_processed": **********,
      "tb_processed": 0.00125,
      "estimated_cost_usd": 0.00625,
      "execution_time_seconds": 1.4
    },
    "optimization_suggestions": [
      "Consider adding date partitioning filters to reduce data scan"
    ]
  }
}
```

**狀態說明：**

- `healthy`: 同步正常，覆蓋率 ≥ 95%，最近 6 小時內有同步，查詢成本 < $0.50
- `warning`: 同步覆蓋率 < 95% 或超過 6 小時未同步或查詢成本過高
- `error`: 發生錯誤無法取得狀態

**BigQuery 成本分析說明：**

- `total_cost_usd`: 本次狀態查詢的總預估成本（美元）
- `total_tb_processed`: 總掃描資料量（TB）
- `total_execution_time_seconds`: 總查詢執行時間（秒）
- `optimization_suggestions`: 自動產生的查詢優化建議
- `source_query_stats` / `target_query_stats`: 個別查詢的詳細統計資訊

**成本控制警告：**

- 查詢成本 > $0.10: 顯示 moderate cost warning
- 查詢成本 > $0.50: 狀態變為 warning，顯示 high cost warning
- 執行時間 > 30 秒: 顯示 slow query warning

### 手動觸發同步

```bash
# 觸發手動同步（時間範圍可選）
curl -X POST http://localhost:8080/start-sync \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-01-15T00:00:00", "end_date": "2024-01-15T05:00:00"}'
```
