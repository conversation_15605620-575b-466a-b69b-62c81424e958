# Act 測試環境變數
# 模擬 GitHub Actions 環境

# GitHub 相關
GITHUB_TOKEN=fake-token-for-testing
GITHUB_REPOSITORY=tagtoo/integrated-event
GITHUB_REF=refs/heads/main
GITHUB_SHA=fake-sha-for-testing

# GCP 相關 (測試用假值)
GOOGLE_CLOUD_PROJECT=test-project
GCP_WORKLOAD_IDENTITY_PROVIDER=fake-provider
GCP_SERVICE_ACCOUNT=<EMAIL>
CLOUD_STORAGE_BUCKET=test-bucket

# 測試用 Secrets (假值)
SECRET_KEY_LEGACY_EVENT_SYNC=test-secret-key
API_KEY_REURL=test-api-key
WEBHOOK_SECRET_SHOPIFY=test-webhook-secret
