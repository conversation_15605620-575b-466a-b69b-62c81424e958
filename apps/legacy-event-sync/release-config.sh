# =============================================================================
# Legacy Event Sync 版本管理配置
# =============================================================================
#
# 用途：legacy-event-sync 服務專屬的版本發佈配置
#
# =============================================================================

# === 基本設定 ===
SERVICE_NAME="legacy-event-sync"
VERSION_FILE="pyproject.toml"
VERSION_PATTERN='version = "([^"]+)"'
DOCKER_REGISTRY="gcr.io/tagtoo-tracking"

# === 驗證設定 ===
REQUIRE_CHANGELOG=false                          # 暫時不強制要求 CHANGELOG
REQUIRE_TESTS=true                               # 要求測試通過
REQUIRE_CLEAN_WORKING_DIR=true

# === Git 設定 ===
GIT_REMOTE="origin"
DEFAULT_BRANCH="main"

# === Terraform 設定 ===
TERRAFORM_DIR="terraform"
TERRAFORM_VARS_FILE=""                           # 使用預設值

# === 客製化驗證鉤子 ===
# 發版前確保程式碼品質和功能正常
CUSTOM_PRE_RELEASE_VALIDATIONS=(
    "make test"                                  # 執行測試套件
    "make lint"                                  # 程式碼風格檢查
    "terraform fmt -check terraform/"           # Terraform 格式檢查
)

# === 客製化後續動作 ===
# 發版完成後的通知和記錄
CUSTOM_POST_RELEASE_ACTIONS=(
    "echo '✅ Legacy Event Sync v${NEW_VERSION} 發版完成'"
    "echo '📊 Docker Image: ${DOCKER_REGISTRY}/${SERVICE_NAME}:${NEW_VERSION}'"
    # 未來可以加入 Slack 通知或其他整合
    # "./scripts/notify-team.sh ${NEW_VERSION}"
)
