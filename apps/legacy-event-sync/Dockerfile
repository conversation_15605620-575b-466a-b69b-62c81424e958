# {{SERVICE_NAME}} Dockerfile
# 多階段建置，優化映像大小和安全性

# 建置階段
FROM python:3.11.11-slim-bookworm AS builder

WORKDIR /app

# 安裝系統依賴 - 使用重試機制處理網路問題
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    for i in 1 2 3; do \
        apt-get update --fix-missing --allow-releaseinfo-change && \
        apt-get install -y --fix-missing --no-install-recommends \
            gcc \
            g++ \
            build-essential \
        && break || { \
            echo "Attempt $i failed, retrying..."; \
            sleep 5; \
            apt-get clean; \
            rm -rf /var/lib/apt/lists/*; \
        }; \
    done && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# 複製需求檔案
COPY requirements.txt .

# 安裝 Python 依賴 - 使用重試機制
RUN pip install --no-cache-dir --user --upgrade pip setuptools wheel && \
    for i in 1 2 3; do \
        pip install --no-cache-dir --user --retries 5 --timeout 120 -r requirements.txt && break || { \
            echo "Pip install attempt $i failed, retrying..."; \
            sleep 10; \
        }; \
    done

# 運行階段
FROM python:3.11.11-slim-bookworm

# 建立非 root 使用者
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app

# 安裝運行時依賴 - 使用重試機制處理網路問題
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    for i in 1 2 3; do \
        apt-get update --fix-missing --allow-releaseinfo-change && \
        apt-get install -y --fix-missing --no-install-recommends \
            curl \
            ca-certificates \
        && break || { \
            echo "Attempt $i failed, retrying..."; \
            sleep 5; \
            apt-get clean; \
            rm -rf /var/lib/apt/lists/*; \
        }; \
    done && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# 從建置階段複製 Python 套件
COPY --from=builder /root/.local /home/<USER>/.local

# 複製應用程式碼
COPY src/ ./src/
COPY *.py ./

# 設定環境變數
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8080

# 給 appuser 對 /app 目錄的完整權限（排除可能在執行時掛載的檔案）
RUN chown -R appuser:appuser /app && \
    chmod -R 755 /app

# 切換到非 root 使用者
USER appuser

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 啟動應用 - 使用環境變數控制 timeout 和 worker 數量
CMD ["sh", "-c", "gunicorn --bind 0.0.0.0:8080 --workers ${GUNICORN_WORKERS:-4} --timeout ${GUNICORN_TIMEOUT:-1740} src.main:app"]
