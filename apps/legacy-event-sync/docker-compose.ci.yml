# CI 環境的 docker-compose 覆蓋設定
# 用於 GitHub Actions 等 CI 環境

services:
  legacy-event-sync:
    # CI 環境需要掛載認證檔案
    volumes:
      - ./pytest.ini:/app/pytest.ini
      - ./.env:/app/.env
      # 修復：從工作區中一個權限正確的位置掛載 ADC 檔案，而不是從 runner 的 home 目錄
      - ../../.gcloud-creds/adc.json:/app/adc.json:ro
      # Schema 檔案掛載 - CI 環境也需要存取 schema 檔案
      - ../../infrastructure/terraform/schema:/app/terraform/schema:ro
    # 修復權限問題：由於 ADC 檔案在 CI 環境中的擁有者可能是 runner，需要確保 appuser 可以讀取
    user: "0:0" # 臨時使用 root 權限啟動，然後在 command 中切換使用者
    environment:
      # CI 環境認證設定 - 使用掛載的 ADC 檔案
      - GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json
      - GOOGLE_CLOUD_PROJECT=tagtoo-tracking
      # CI 環境設定
      - ENVIRONMENT=ci
      - DEBUG=false
      - LOG_LEVEL=INFO
      # 使用測試表格避免影響生產資料
      - BIGQUERY_DATASET=event_test
      - SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
      - TARGET_TABLE=tagtoo-tracking.event_test.integrated_event
      # 測試環境的批次設定（較小的批次大小）
      - BATCH_SIZE=10000
      - HOURS_PER_SEGMENT=1
      # 修復 PATH 問題 - 確保在 root 環境下也能找到 appuser 的 Python 套件
      - PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin
      - PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages
    # 直接以 appuser 身份啟動服務，不嘗試修改只讀檔案權限
    command:
      [
        "sh",
        "-c",
        "su appuser -c 'gunicorn --bind 0.0.0.0:8080 --workers 1 --timeout 180 src.main:app'",
      ]
    # CI 環境的健康檢查 - 延長等待時間
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s
