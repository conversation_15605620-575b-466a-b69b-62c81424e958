# CI 環境的 docker-compose 覆蓋設定
# 用於 GitHub Actions 等 CI 環境

services:
  legacy-event-sync:
    # CI 環境建置設定 - 使用較穩定的建置參數
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
      network: host
    # CI 環境需要掛載認證檔案
    volumes:
      - ./pytest.ini:/app/pytest.ini
      - ./.env:/app/.env
      # CI 環境掛載認證檔案
      - ../../.gcloud-creds/adc.json:/app/adc.json:ro
      # Schema 檔案掛載
      - ../../infrastructure/terraform/schema:/app/terraform/schema:ro
    # 為了簡化 CI 環境的權限問題，暫時使用 root 權限
    # 這不是最佳實踐，但可以避免複雜的權限設定
    user: "0:0"
    environment:
      # CI 環境認證設定 - 使用掛載的 ADC 檔案
      - GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json
      - GOOGLE_CLOUD_PROJECT=tagtoo-tracking
      # CI 環境設定
      - ENVIRONMENT=ci
      - DEBUG=false
      - LOG_LEVEL=INFO
      # 使用測試表格避免影響生產資料
      - BIGQUERY_DATASET=event_test
      - SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
      - TARGET_TABLE=tagtoo-tracking.event_test.integrated_event
      # 測試環境的批次設定（較小的批次大小）
      - BATCH_SIZE=5000
      - HOURS_PER_SEGMENT=1
      # CI 環境的環境變數設定
      - PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin
      - PYTHONPATH=/app:/home/<USER>/.local/lib/python3.11/site-packages
    # CI 環境啟動命令 - 雖然容器以 root 執行，但實際服務切換到 appuser
    command:
      [
        "sh",
        "-c",
        "su appuser -c 'gunicorn --bind 0.0.0.0:8080 --workers 1 --timeout 180 src.main:app'",
      ]
    # CI 環境的健康檢查 - 延長等待時間
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s
