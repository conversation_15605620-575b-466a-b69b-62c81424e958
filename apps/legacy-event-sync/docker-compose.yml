# Legacy Event Sync 開發環境
# 使用真實 BigQuery + 測試表格進行開發

services:
  # 主要應用服務
  legacy-event-sync:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    # 直接使用 appuser 執行，遵循 Dockerfile 的設計
    # 開發環境使用 --reload 實現熱重載
    command:
      [
        "gunicorn",
        "--bind",
        "0.0.0.0:8080",
        "--workers",
        "4",
        "--timeout",
        "1800",
        "--reload",
        "src.main:app",
      ]
    environment:
      - PROJECT_ID=tagtoo-tracking
      - ENVIRONMENT=dev
      - DEBUG=true
      - LOG_LEVEL=INFO
      - PORT=8080
      # BigQuery 設定 - 使用真實 BigQuery
      - BIGQUERY_DATASET=event_test
      - SECRET_KEY=a-very-secret-key-for-dev
      # Google Cloud 認證設定 - 使用 ADC
      - GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json
      - GOOGLE_CLOUD_PROJECT=tagtoo-tracking

      # 效能優化開關
      - USE_OPTIMIZED_MERGE=true
      # 分時段處理配置
      - BATCH_SIZE=5000
      - MINUTES_PER_SEGMENT=5 # 每 5 分鐘一個 segment，提升同步穩定性，降低 timeout 風險
      - MAX_CONCURRENT_SEGMENTS=20
      - MEMORY_MB=4096
      - CPU_COUNT=4
      # 開發環境表格名稱 - 使用測試表格
      - SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
      - TARGET_TABLE=tagtoo-tracking.event_test.integrated_event
    mem_limit: 2g
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
      - ./scripts:/app/scripts
      - ./config.py:/app/config.py
      - ./pytest.ini:/app/pytest.ini
      - ./.env:/app/.env
      # Google Cloud ADC 檔案掛載
      - ~/.config/gcloud/application_default_credentials.json:/app/adc.json:ro
      # Schema 檔案掛載 - 解決上層目錄存取問題
      - ../../infrastructure/terraform/schema:/app/terraform/schema:ro
    restart: unless-stopped
    networks:
      - legacy-event-sync-network

networks:
  legacy-event-sync-network:
    driver: bridge
