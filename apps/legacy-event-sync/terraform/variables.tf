# Legacy Event Sync Service Variables

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "deployment_version" {
  description = "Deployment version for source code"
  type        = string
  default     = "latest"
}

variable "service_name" {
  description = "Service name, used for Docker image and other resources"
  type        = string
  default     = "legacy-event-sync"
}

variable "sync_schedule" {
  description = "Cron schedule for sync job"
  type        = string
  default     = "0 2/4 * * *" # Every 4 hours, starting at 2 AM (Asia/Taipei)
}

# ====== 成本監控變數 ======
variable "billing_account_id" {
  description = "Billing account ID for cost monitoring"
  type        = string
  default     = null # 可選，只在 prod 環境啟用成本監控時需要
}

# ====== 敏感配置變數 ======
variable "secret_key" {
  description = "Secret key for application security (目前服務未使用)"
  type        = string
  default     = null
  sensitive   = true
}

variable "api_key" {
  description = "API key for external services (optional)"
  type        = string
  default     = null
  sensitive   = true
}

variable "webhook_secret" {
  description = "Webhook secret for validation (optional)"
  type        = string
  default     = null
  sensitive   = true
}

# ====== 應用配置變數 ======
variable "log_level" {
  description = "Log level for the application"
  type        = string
  default     = "INFO"

  validation {
    condition     = contains(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], var.log_level)
    error_message = "Log level must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL."
  }
}

variable "batch_size" {
  description = "Batch size for data processing"
  type        = number
  default     = 50000 # 減少批次大小避免記憶體不足
}

variable "hours_per_segment" {
  description = "Hours per processing segment"
  type        = number
  default     = 4
}

variable "max_concurrent_segments" {
  description = "Maximum concurrent processing segments"
  type        = number
  default     = 6
}

# ====== Cloud Run 資源配置 ======
variable "cloud_run_cpu" {
  description = "CPU allocation for Cloud Run service"
  type        = string
  default     = "4"
}

variable "cloud_run_memory" {
  description = "Memory allocation for Cloud Run service"
  type        = string
  default     = "8Gi" # 增加記憶體分配處理大量資料
}

variable "cloud_run_timeout" {
  description = "Timeout in seconds for Cloud Run service"
  type        = number
  default     = 1800 # 30 minutes
}

variable "cloud_run_max_instances" {
  description = "Maximum number of Cloud Run instances"
  type        = number
  default     = 10
}

variable "cloud_run_min_instances" {
  description = "Minimum number of Cloud Run instances"
  type        = number
  default     = 0
}

# ====== Cloud Tasks Queue 配置 ======
variable "task_queue_max_concurrent_dispatches" {
  description = "The maximum number of concurrent tasks that Cloud Tasks allows to be dispatched for this queue."
  type        = number
  default     = 10
}

variable "task_queue_max_dispatches_per_second" {
  description = "The maximum rate at which tasks are dispatched from this queue."
  type        = number
  default     = 10
}
