# CI/CD 設定指南

## 🎯 **概述**

本指南說明如何為 Legacy Event Sync 服務設定 GitHub Actions CI/CD 管道，採用雙 Service Account 架構提供權限分離。

## 🏗️ **架構設計**

```
┌─────────────────────────────────────────────────────────┐
│                    GitHub Actions                        │
├─────────────────────────────────────────────────────────┤
│  CI 測試階段                    │  CD 部署階段           │
│  ├─ WIF 認證                   │  ├─ WIF 認證           │
│  ├─ integrated-event-runner    │  ├─ integrated-event-cicd │
│  ├─ 執行測試                   │  ├─ Terraform 部署     │
│  └─ BigQuery 存取              │  └─ 基礎設施管理       │
└─────────────────────────────────────────────────────────┘
```

### **權限分離原則**

1. **應用執行層級** (`integrated-event-runner`)：

   - CI 測試階段使用
   - 服務實際執行時使用
   - 作為 `/apps` 下所有服務的預設執行帳號
   - 權限：BigQuery 資料存取、Cloud Functions/Run 執行

2. **基礎設施層級** (`integrated-event-cicd`)：
   - CD 部署階段使用
   - Terraform 基礎設施管理
   - 權限：完整的部署和資源管理權限

## 🔐 **GitHub Repository 設定清單**

在 GitHub Repository > Settings > Secrets and variables > Actions 中設定以下項目：

### **📋 Variables (非敏感配置)**

進入 **Variables** 標籤，設定以下變數：

| Variable 名稱                | 說明                                 | 範例值                                                                                         |
| ---------------------------- | ------------------------------------ | ---------------------------------------------------------------------------------------------- |
| `WIF_PROVIDER`               | Workload Identity Provider 完整路徑  | `projects/************/locations/global/workloadIdentityPools/github-actions/providers/github` |
| `INTEGRATED_EVENT_RUNNER_SA` | 應用執行 Service Account Email       | `<EMAIL>`                              |
| `TERRAFORM_USER_SA`          | Terraform 部署 Service Account Email | `<EMAIL>`                                |

### **🔐 Secrets (敏感資訊)**

進入 **Secrets** 標籤，設定以下機密資訊：

| Secret 名稱                         | 說明                 | 必要性      |
| ----------------------------------- | -------------------- | ----------- |
| `LEGACY_EVENT_SYNC_PROD_SECRET_KEY` | 生產環境應用程式密鑰 | ❌ 暫不需要 |
| `LEGACY_EVENT_SYNC_PROD_API_KEY`    | 生產環境 API 金鑰    | ⚪ 可選     |

## 🏗️ **Service Accounts 和 WIF 設定**

### **自動設定腳本**

執行根目錄的設定腳本：

```bash
# 請先確認並修改腳本中的 REPO 變數為你的實際 GitHub repo
chmod +x setup-service-accounts.sh
./setup-service-accounts.sh
```

### **手動設定步驟**

如果需要手動設定，請按照以下步驟：

#### **步驟 1: 建立應用執行 Service Account**

```bash
# 設定變數
PROJECT_ID="tagtoo-tracking"
RUNNER_SA_EMAIL="<EMAIL>"

# 建立 Service Account
gcloud iam service-accounts create integrated-event-runner \
    --project=$PROJECT_ID \
    --display-name="Integrated Event Runner" \
    --description="用於 integrated-event 專案的應用執行和 CI 測試"

# 設定 BigQuery 權限
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/bigquery.jobUser"

# 設定執行權限
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/cloudfunctions.invoker"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/run.invoker"

# 設定監控權限
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/monitoring.metricWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$RUNNER_SA_EMAIL" \
    --role="roles/logging.logWriter"
```

#### **步驟 2: 設定 Workload Identity Federation**

```bash
# 建立 Workload Identity Pool (如果不存在)
gcloud iam workload-identity-pools create github-actions \
    --project=$PROJECT_ID \
    --location="global" \
    --display-name="GitHub Actions Pool"

# 建立 OIDC Provider (如果不存在)
gcloud iam workload-identity-pools providers create-oidc github \
    --project=$PROJECT_ID \
    --location="global" \
    --workload-identity-pool=github-actions \
    --display-name="GitHub Actions Provider" \
    --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner" \
    --attribute-condition="assertion.repository_owner == 'your-github-username'" \
    --issuer-uri="https://token.actions.githubusercontent.com"

# 綁定兩個 Service Accounts 到 WIF
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
REPO="your-username/integrated-event"

# 綁定應用執行 Service Account
gcloud iam service-accounts add-iam-policy-binding $RUNNER_SA_EMAIL \
    --project=$PROJECT_ID \
    --role="roles/iam.workloadIdentityUser" \
    --member="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/github-actions/attribute.repository/$REPO"

# 綁定 Terraform 部署 Service Account
gcloud iam service-accounts add-iam-policy-binding <EMAIL> \
    --project=$PROJECT_ID \
    --role="roles/iam.workloadIdentityUser" \
    --member="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/github-actions/attribute.repository/$REPO"
```

## 🔄 **CI/CD 工作流程說明**

### **權限設定要求**

⚠️ **重要**: GitHub Actions 中使用 Workload Identity Federation 必須明確設定權限：

```yaml
permissions:
  contents: read
  id-token: write # ← 這個權限是關鍵！
```

**原因說明**：

- GitHub Actions 預設 `id-token` 權限為 `none`
- 必須設定為 `write` 才能注入以下環境變數：
  - `$ACTIONS_ID_TOKEN_REQUEST_TOKEN`
  - `$ACTIONS_ID_TOKEN_REQUEST_URL`
- 這些變數是 `google-github-actions/auth@v2` 進行 OIDC 認證的必要條件
- 詳細說明請參考: [GitHub 官方文檔](https://docs.github.com/en/actions/security-guides/automatic-token-authentication#permissions-for-the-github_token)

### **測試階段流程**

1. **認證設定**：使用 `integrated-event-runner` Service Account
2. **權限驗證**：測試 BigQuery 連線和基本權限
3. **測試執行**：
   - 自動偵測變動的服務（或手動指定服務）
   - 使用 `make test` 執行完整測試套件
   - 連接真實 BigQuery 進行整合測試
4. **結果上傳**：儲存測試覆蓋率報告

### **🚀 手動觸發功能 (新增)**

CI/CD 管道現已支援手動觸發，提供更靈活的部署選項：

#### **觸發方式**

- **GitHub Actions UI**: 直接從網頁介面觸發
- **指定服務**: 可選擇特定服務進行部署
- **環境選擇**: 支援 `prod` 和 `dev` 環境

#### **手動觸發配置**

在 GitHub Actions 手動觸發時，可設定以下參數：

- **Service** (可選): 指定要部署的服務名稱（如 `legacy-event-sync`）
- **Environment** (必選): 選擇目標環境 (`prod` 或 `dev`)

#### **使用場景**

- ✅ **測試部署**: 在開發環境快速測試新功能
- ✅ **熱修復**: 緊急修復需要立即部署
- ✅ **版本驗證**: 驗證特定版本的部署流程
- ✅ **CI/CD 測試**: 測試 CI/CD 管道本身的功能

### **部署階段流程**

1. **觸發條件**：僅在 `main` 分支的 push 事件觸發
2. **認證設定**：使用 `integrated-event-cicd` Service Account
3. **權限驗證**：確認 Terraform 部署權限
4. **部署執行**：
   - 初始化 Terraform
   - 執行 `terraform plan`
   - 自動應用部署 (`terraform apply`)
5. **部署驗證**：確認服務成功部署

## 🔍 **故障排除**

### **快速檢核清單**

遇到 CI/CD 問題時，請按順序檢查：

1. ✅ **權限設定**: workflow 中有設定 `id-token: write` 權限嗎？
2. ✅ **Variables 設定**: GitHub Repository Variables 是否正確設定？
3. ✅ **Service Account**: WIF 綁定是否正確？
4. ✅ **權限**: Service Account 是否有必要的 GCP 權限？

### **常見錯誤及解決方法**

#### **1. WIF 認證權限錯誤 (最常見)**

```
Error: google-github-actions/auth failed with: GitHub Actions did not inject
$ACTIONS_ID_TOKEN_REQUEST_TOKEN or $ACTIONS_ID_TOKEN_REQUEST_URL into this job.
```

**解決方法：**

- ✅ **在 workflow 中添加 `id-token: write` 權限**
- 檢查 job 層級是否包含以下設定：
  ```yaml
  permissions:
    contents: read
    id-token: write
  ```
- 這是使用 WIF 的必要權限設定

#### **2. 測試階段認證失敗**

```
Error: Failed to generate access token for integrated-event-runner
```

**解決方法：**

- 檢查 `INTEGRATED_EVENT_RUNNER_SA` 是否正確設定
- 確認 Service Account 有 BigQuery 相關權限
- 檢查 WIF 綁定是否正確

#### **3. 部署階段認證失敗**

```
Error: Failed to generate access token for integrated-event-cicd
```

**解決方法：**

- 檢查 `TERRAFORM_USER_SA` 是否正確設定
- 確認 `integrated-event-cicd` Service Account 有部署權限
- 檢查 WIF 綁定是否包含此 Service Account

#### **4. BigQuery 連線失敗**

```
Error: Could not automatically determine credentials
```

**解決方法：**

- 確認 `integrated-event-runner` 有 BigQuery 權限
- 檢查 `create_credentials_file: true` 設定是否正確
- 驗證 ADC 檔案是否正確掛載到 Docker 容器

#### **5. Terraform 部署失敗**

```
Error: insufficient permissions
```

**解決方法：**

- 確認 `integrated-event-cicd` 有必要的部署權限
- 檢查專案層級 IAM 設定
- 驗證 Terraform 後端配置

#### **6. 手動觸發相關問題**

**手動觸發找不到選項**

```
Run workflow 按鈕不可見或無法點擊
```

**解決方法：**

- 確認你有 repository 的 `Actions: write` 權限
- 檢查 workflow 檔案中的 `workflow_dispatch` 配置是否正確
- 確認 workflow 檔案已推送到 `main` 分支

**手動觸發的服務沒有被偵測**

```
detect-changes job 顯示 "services=" 空值
```

**解決方法：**

- 檢查輸入的服務名稱是否正確（如：`legacy-event-sync`）
- 確認服務目錄存在於 `apps/` 下
- 檢查 workflow 中的 `detect-changes` job 邏輯

#### **7. ensure-shared-infrastructure git diff 錯誤 (已修正)**

```
fatal: Invalid symmetric difference expression xxx...yyy
```

**根本原因：**
`ensure-shared-infrastructure` job 預設只 fetch 最近 1 個 commit，無法訪問 `github.event.before` SHA，導致 `git diff` 失敗。

**症狀：**

- Push 事件觸發時出現 "Invalid symmetric difference expression" 錯誤
- 手動觸發部署正常，但 push 觸發失敗
- 錯誤出現在 "Check if shared infrastructure changes exist" 步驟

**解決方案 (已實施)：**

1. **✅ 添加 `fetch-depth: 0`** 到 `ensure-shared-infrastructure` job 的 checkout 步驟
2. **✅ 增強 SHA 存在性檢查** 使用 `git cat-file -e` 驗證 SHA 有效性
3. **✅ 改善錯誤處理邏輯** 提供備用機制處理無效 SHA
4. **✅ 專門處理手動觸發** 避免不必要的 git diff 操作
5. **✅ 增強除錯輸出** 便於問題排查和監控

**預防措施：**

- 任何使用 `git diff` 比較不同 commit 的 job 都應該設定 `fetch-depth: 0`
- 在比較 SHA 前先檢查其有效性
- 為不同的觸發事件提供專門的處理邏輯

**相關 commit：**

- `a046776` - [fix](ci): 徹底修正 ensure-shared-infrastructure git diff 錯誤

### **除錯指令**

```bash
# 檢查 Service Account 權限
gcloud projects get-iam-policy tagtoo-tracking \
    --flatten="bindings[].members" \
    --filter="bindings.members:<EMAIL>"

gcloud projects get-iam-policy tagtoo-tracking \
    --flatten="bindings[].members" \
    --filter="bindings.members:<EMAIL>"

# 檢查 WIF 設定
gcloud iam workload-identity-pools describe github-actions \
    --project=tagtoo-tracking --location="global"

gcloud iam workload-identity-pools providers describe github \
    --project=tagtoo-tracking --location="global" \
    --workload-identity-pool=github-actions

# 檢查 Service Account IAM 政策
gcloud iam service-accounts get-iam-policy <EMAIL> \
    --project=tagtoo-tracking

gcloud iam service-accounts get-iam-policy <EMAIL> \
    --project=tagtoo-tracking
```

## 🚀 **CI/CD 工作流程驗證**

推送程式碼到 Repository 後，檢查以下項目：

### **✅ 測試階段驗證**

- [ ] 變動服務偵測正確
- [ ] `integrated-event-runner` 認證成功
- [ ] BigQuery 連線測試通過
- [ ] 測試執行完成
- [ ] 測試覆蓋率報告生成

### **✅ 部署階段驗證** (僅 main 分支)

- [ ] `integrated-event-cicd` 認證成功
- [ ] Terraform 初始化成功
- [ ] Terraform plan 執行
- [ ] Terraform apply 完成
- [ ] 部署驗證通過

## 🎯 **安全優勢**

這個雙 Service Account 架構提供了以下安全優勢：

1. **權限分離**：測試和部署使用不同的權限範圍
2. **最小權限原則**：每個 Service Account 只有必要的權限
3. **統一管理**：`integrated-event-runner` 可供所有 `/apps` 服務使用
4. **職責清晰**：應用執行與基礎設施管理職責分離
5. **風險降低**：測試階段無法意外修改基礎設施

## 📖 **相關資源**

- [Google Cloud WIF 官方文檔](https://cloud.google.com/iam/docs/workload-identity-federation)
- [GitHub Actions Google Auth](https://github.com/google-github-actions/auth)
- [BigQuery IAM 角色說明](https://cloud.google.com/bigquery/docs/access-control)

---

💡 **提示：** 完成設定後，建議先建立一個測試 PR 來驗證整個 CI/CD 管道是否正常運作。雙 Service Account 架構確保了更好的安全性和權限控制。
