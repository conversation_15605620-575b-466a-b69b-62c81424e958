# Legacy Event Sync Service - 開發文件

## 🚨 最新架構變更 (2025-07-04)

### 架構遷移：從 Cloud Function 到 Cloud Run + Cloud Tasks

為了提升服務的穩定性、可擴展性和處理長時間任務的能力，我們已將此服務從原本的 **Cloud Function + Pub/Sub** 架構遷移至 **Cloud Run + Cloud Tasks**。

- **Cloud Run**: 提供更長的請求超時時間（最長 60 分鐘），適合處理大規模資料同步任務，並能更好地控制容器環境。
- **Cloud Tasks**: 提供可靠的非同步任務分派和重試機制，讓我們能將大型同步任務拆分為多個獨立、可管理的時間區段。

---

## 專案背景

這個服務是 Integrated Event Platform 專案的第一個里程碑，負責將現有的 `tagtoo_event` 資料同步到新的統一格式 `integrated_event` 表格中。

### 為什麼需要這個服務？

1.  **資料整合**: 將現有的 tagtoo_event 資料整合到新的統一格式中
2.  **向後相容**: 確保現有資料不遺失，能夠在新系統中使用
3.  **逐步遷移**: 提供一個過渡期，讓系統可以逐漸從舊格式遷移到新格式

## 技術選擇分析

### 為什麼選擇 Cloud Run + Cloud Tasks？

我們最初使用 Cloud Function + Pub/Sub 的架構，但在大規模資料測試中遇到了一些挑戰。新的架構提供了以下優勢：

| 評估項目       | Cloud Function + Pub/Sub (舊架構)                                                         | Cloud Run + Cloud Tasks (新架構)                                               | 選擇原因                                                                                                 |
| -------------- | ----------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------- |
| **執行時間**   | HTTP 觸發最長 9 分鐘，背景觸發最長 60 分鐘，但受 Pub/Sub Ack Deadline 限制 (最長 10 分鐘) | HTTP 請求最長 60 分鐘                                                          | **勝出**。Cloud Run 提供了更長的執行時間，足以處理數小時的資料量，避免因超時而導致的任務失敗和重試風暴。 |
| **任務管理**   | Pub/Sub 提供「至少一次」的傳遞，但缺乏內建的排程、速率控制和精細的重試策略。              | Cloud Tasks 提供精確的任務排程、速率限制、自訂重試次數和指數退避策略。         | **勝出**。Cloud Tasks 提供了更強大、更可靠的任務管理能力，非常適合我們的 Coordinator/Worker 模式。       |
| **部署與環境** | 部署單位是單一函式，環境配置較為受限。                                                    | 部署單位是標準的 Docker 容器，可以完全自訂執行環境、依賴項和系統工具。         | **勝出**。容器化讓我們能統一開發、測試和生產環境，降低環境不一致帶來的風險。                             |
| **成本**       | 依執行次數和時間計費，對於大量短任務可能較具成本效益。                                    | 依容器實例的 vCPU 和記憶體使用時間計費。在處理長時間任務時，成本結構更可預測。 | **持平**。兩者成本模型不同，但對於我們的使用場景，總成本在可接受範圍內，而穩定性是更重要的考量。         |
| **本地開發**   | 模擬器 (Functions Framework) 功能有限，難以模擬真實的 Pub/Sub 觸發。                      | 可直接在本機運行 Docker 容器，與雲端環境高度一致。                             | **勝出**。簡化了本地開發和測試流程。                                                                     |

### 最終選擇：Cloud Run + Cloud Tasks

新架構提供了處理大規模、長時間同步任務所需的穩定性和彈性，是此服務演進的必然選擇。

---

## 同步策略

服務採用 **Coordinator/Worker** 模式，將同步邏輯拆分為兩個主要部分。

### 角色分工

- **Coordinator (`/start-sync`)**: 協調者。由 Cloud Scheduler 定期觸發，不處理實際資料。其職責是：
  1.  從 Firestore 獲取上次同步的結束時間。
  2.  計算需要同步的完整時間範圍（例如：從上次結束到現在）。
  3.  將完整時間範圍切割成多個較小的時間區段（例如，每 4 小時一段）。
  4.  為每一個時間區段建立一個 Cloud Task，並將任務發送到佇列中。
- **Worker (`/process-segment`)**: 工人。由 Cloud Tasks 觸發，每個 Worker 只負責一個時間區段的資料同步。其職責是：
  1.  從任務 payload 中獲取時間區段的開始和結束時間。
  2.  從來源 BigQuery 表格中查詢該時間區段的所有資料。
  3.  對每一筆資料進行轉換。
  4.  將轉換後的資料分批寫入目標 BigQuery 表格。

### 日常同步流程

```mermaid
flowchart TD
    A[Cloud Scheduler<br/>(每日觸發)] --> B(Cloud Run: /start-sync<br/>Coordinator)
    B --> C{計算時間範圍<br/>(e.g., 24小時)}
    C --> D[切割為 N 個時間區段<br/>(e.g., 6 個 4H 區段)]
    D --> E(為每個區段建立 Cloud Task)

    subgraph "Cloud Tasks Queue"
        F1(Task 1: 00:00-04:00)
        F2(Task 2: 04:00-08:00)
        F3(...)
        F4(Task N: 20:00-24:00)
    end

    E --> F1
    E --> F2
    E --> F3
    E --> F4

    F1 --> G1(Cloud Run: /process-segment<br/>Worker 1)
    F2 --> G2(Cloud Run: /process-segment<br/>Worker 2)
    F4 --> G4(Cloud Run: /process-segment<br/>Worker N)

    subgraph "Worker 處理流程"
        H[查詢來源 BigQuery] --> I[轉換資料]
        I --> J[分批寫入目標 BigQuery]
    end

    G1 --> H
    G2 --> H
    G4 --> H

    J --> K[任務完成]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style E fill:#fff3e0
    style G1 fill:#e8f5e8
    style G2 fill:#e8f5e8
    style G4 fill:#e8f5e8
```

---

## 資料結構分析

### 完整欄位對應表

| tagtoo_event (來源)          | integrated_event (目標) | 資料類型               | 說明                          |
| ---------------------------- | ----------------------- | ---------------------- | ----------------------------- |
| `permanent`                  | `permanent`             | STRING                 | 使用者永久 ID                 |
| `ec_id`                      | `ec_id`                 | INTEGER                | 電商平台 ID                   |
| `event_time`                 | `event_time`            | TIMESTAMP (ISO String) | 事件發生時間                  |
| `link`                       | `link`                  | STRING                 | 當前頁面連結                  |
| `event.name`                 | `event`                 | STRING                 | 事件名稱                      |
| `event.value`                | `value`                 | FLOAT                  | 事件價值                      |
| `event.currency`             | `currency`              | STRING                 | 貨幣類型                      |
| `event.custom_data.order_id` | `order_id`              | STRING                 | 從 custom_data 提取           |
| `event.items[]`              | `items[]`               | RECORD (Array)         | 商品陣列轉換                  |
| `user.em`                    | `user.em`               | STRING                 | 使用者 Email (雜湊)           |
| `user.ph`                    | `user.ph`               | STRING                 | 使用者電話 (雜湊)             |
| `location.country_code`      | `location.country_code` | STRING                 | 直接搬移                      |
| `location.region_name`       | `location.region_name`  | STRING                 | 直接搬移                      |
| `location.city_name`         | `location.city_name`    | STRING                 | 直接搬移                      |
| (N/A)                        | `partner_source`        | STRING                 | 固定值: 'legacy-tagtoo-event' |
| (N/A)                        | `partner_id`            | INTEGER                | 保留，暫為 NULL               |
| (N/A)                        | `page`                  | RECORD                 | 保留，暫為 NULL               |
| (N/A)                        | `raw_json`              | RECORD                 | 保留，暫為 NULL               |
| (N/A)                        | `create_time`           | TIMESTAMP (ISO String) | 資料寫入時間 (UTC)            |

_注意：來源資料中其他未明確對應的欄位將被捨棄。_

---

## 開發最佳實踐

### 程式碼結構

服務採用了簡單的單一檔案結構，所有邏輯都封裝在 `src/main.py` 中，並透過 `LegacyEventSyncProcessor` 類別進行組織。

### 程式碼結構

```
src/
└── main.py # Flask 應用程式主體
├── /health # 健康檢查端點
├── /start-sync # Coordinator 端點
└── /process-segment # Worker 端點
│
└── LegacyEventSyncProcessor # 核心邏輯類別
├── coordinate_sync() # Coordinator 邏輯
└── sync_time_segment() # Worker 邏輯
```

### 測試策略

請參考 `docs/TESTING.md` 獲取詳細的測試策略。

---

## 部署指南

### 容器化部署

此服務以 Docker 容器的形式部署到 Cloud Run。CI/CD 流程 (`.github/workflows/apps-ci-cd.yml`) 會自動處理以下步驟：

1.  **建置映像**: 使用 `apps/legacy-event-sync/Dockerfile` 建置 Docker 映像。
2.  **推送映像**: 將建置好的映像推送到 Artifact Registry。
3.  **Terraform 部署**: `apps/legacy-event-sync/terraform/main.tf` 中的 Cloud Run 資源會引用剛推送到 Artifact Registry 的最新映像進行部署。

### 手動部署 (本地)

```bash
# 進入服務目錄
cd apps/legacy-event-sync

# 部署到開發環境
make deploy-dev

# 部署到生產環境 (需要手動確認 apply)
make deploy-prod
```

### 部署驗證檢查清單

- [ ] Cloud Run 服務部署成功。
- [ ] Cloud Tasks Queue 已建立。
- [ ] Cloud Scheduler 已設定並指向 `/start-sync` 端點。
- [ ] BigQuery 目標表格 Schema 正確。
- [ ] IAM 權限已設定 (Cloud Run, Cloud Tasks, BigQuery, Firestore)。
- [ ] 手動觸發 `make sync-manual` 測試本地 Coordinator/Worker 流程。

---

## 故障排除指南

### 常見問題與解決方案

#### 1. Coordinator 任務成功，但沒有 Worker 執行

**症狀**: `/start-sync` 回應成功，但 Cloud Tasks 佇列中沒有任務，或 Cloud Run Worker 沒有日誌。
**可能原因**:

- **權限問題**: Cloud Run 服務帳戶沒有權限建立 Cloud Tasks。
- **Worker URL 錯誤**: 環境變數 `WORKER_URL` 設定不正確。
- **佇列不存在**: `TASK_QUEUE_NAME` 不正確或 Terraform 未成功建立佇列。
  **解決步驟**:

1. 檢查 Cloud Run 服務帳戶是否具有 `Cloud Tasks Enqueuer` 角色。
2. 檢查 Cloud Run 服務的環境變數，確認 `WORKER_URL` 和 `TASK_QUEUE_NAME`。
3. 前往 GCP 主控台確認 Cloud Tasks 佇列是否存在且位於正確的地區。

#### 2. Worker 任務不斷重試

**症狀**: 在 Cloud Tasks 中看到同一個任務反覆執行並失敗。
**可能原因**:

- **資料轉換錯誤**: 某筆來源資料格式異常，導致 `transform_event_data` 函式拋出未處理的例外。
- **Schema 不匹配**: 轉換後的資料與目標 BigQuery 表格的 Schema 不符。
- **權限不足**: Cloud Run 服務帳戶沒有權限寫入目標 BigQuery 表格。

**解決步驟**:

1.  **檢查 Worker 日誌**: 這是最重要的步驟。在 Cloud Logging 中篩選對應的 Cloud Run 服務日誌，找到錯誤訊息和堆疊追蹤。
2.  **檢查來源資料**: 如果錯誤與特定資料相關，手動查詢該時間區段的來源資料。
3.  **驗證 Schema**: 使用 `bq show` 指令檢查目標表格的 Schema 是否與預期相符。
