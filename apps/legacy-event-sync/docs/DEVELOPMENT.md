# Legacy Event Sync Service - 開發文件

## 🚨 最新架構變更 (2025-07-04)

### 架構遷移：從 Cloud Function 到 Cloud Run + Cloud Tasks

為了提升服務的穩定性、可擴展性和處理長時間任務的能力，我們已將此服務從原本的 **Cloud Function + Pub/Sub** 架構遷移至 **Cloud Run + Cloud Tasks**。

- **Cloud Run**: 提供更長的請求超時時間（最長 60 分鐘），適合處理大規模資料同步任務，並能更好地控制容器環境。
- **Cloud Tasks**: 提供可靠的非同步任務分派和重試機制，讓我們能將大型同步任務拆分為多個獨立、可管理的時間區段。

---

## 專案背景

這個服務是 Integrated Event Platform 專案的第一個里程碑，負責將現有的 `tagtoo_event` 資料同步到新的統一格式 `integrated_event` 表格中。

### 為什麼需要這個服務？

1.  **資料整合**: 將現有的 tagtoo_event 資料整合到新的統一格式中
2.  **向後相容**: 確保現有資料不遺失，能夠在新系統中使用
3.  **逐步遷移**: 提供一個過渡期，讓系統可以逐漸從舊格式遷移到新格式
4.  **效能最佳化**: 透過分散式處理和智能去重，確保高效率的資料同步

## 技術選擇分析

### 為什麼選擇 Cloud Run + Cloud Tasks？

我們最初使用 Cloud Function + Pub/Sub 的架構，但在大規模資料測試中遇到了一些挑戰。新的架構提供了以下優勢：

| 評估項目       | Cloud Function + Pub/Sub (舊架構)                                                         | Cloud Run + Cloud Tasks (新架構)                                               | 選擇原因                                                                                                 |
| -------------- | ----------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------- |
| **執行時間**   | HTTP 觸發最長 9 分鐘，背景觸發最長 60 分鐘，但受 Pub/Sub Ack Deadline 限制 (最長 10 分鐘) | HTTP 請求最長 60 分鐘                                                          | **勝出**。Cloud Run 提供了更長的執行時間，足以處理數小時的資料量，避免因超時而導致的任務失敗和重試風暴。 |
| **任務管理**   | Pub/Sub 提供「至少一次」的傳遞，但缺乏內建的排程、速率控制和精細的重試策略。              | Cloud Tasks 提供精確的任務排程、速率限制、自訂重試次數和指數退避策略。         | **勝出**。Cloud Tasks 提供了更強大、更可靠的任務管理能力，非常適合我們的 Coordinator/Worker 模式。       |
| **部署與環境** | 部署單位是單一函式，環境配置較為受限。                                                    | 部署單位是標準的 Docker 容器，可以完全自訂執行環境、依賴項和系統工具。         | **勝出**。容器化讓我們能統一開發、測試和生產環境，降低環境不一致帶來的風險。                             |
| **成本**       | 依執行次數和時間計費，對於大量短任務可能較具成本效益。                                    | 依容器實例的 vCPU 和記憶體使用時間計費。在處理長時間任務時，成本結構更可預測。 | **持平**。兩者成本模型不同，但對於我們的使用場景，總成本在可接受範圍內，而穩定性是更重要的考量。         |
| **本地開發**   | 模擬器 (Functions Framework) 功能有限，難以模擬真實的 Pub/Sub 觸發。                      | 可直接在本機運行 Docker 容器，與雲端環境高度一致。                             | **勝出**。簡化了本地開發和測試流程。                                                                     |

### 最終選擇：Cloud Run + Cloud Tasks

新架構提供了處理大規模、長時間同步任務所需的穩定性和彈性，是此服務演進的必然選擇。

---

## 🏗️ 系統架構詳解

### 整體架構圖

```mermaid
graph TB
    subgraph "觸發層"
        CS[Cloud Scheduler<br/>每 60 分鐘]
        Manual[手動觸發]
    end

    subgraph "協調層"
        CR[Cloud Run<br/>Coordinator]
        CR --> TG[時間分段計算]
        TG --> TC[任務建立]
    end

    subgraph "執行層"
        CT[Cloud Tasks<br/>任務佇列]
        W1[Worker 1]
        W2[Worker 2]
        Wn[Worker N]
        CT --> W1
        CT --> W2
        CT --> Wn
    end

    subgraph "資料層"
        BQ1[(BigQuery<br/>來源表格)]
        BQ2[(BigQuery<br/>目標表格)]
        FS[(Firestore<br/>狀態追蹤)]
    end

    subgraph "監控層"
        API[監控 API]
        Health[健康檢查]
        Cost[成本分析]
    end

    CS --> CR
    Manual --> CR
    TC --> CT
    W1 --> BQ1
    W1 --> BQ2
    W2 --> BQ1
    W2 --> BQ2
    Wn --> BQ1
    Wn --> BQ2
    CR --> FS
    W1 --> FS
    CR --> API
    API --> Health
    API --> Cost

    style CS fill:#fff3e0
    style CR fill:#f3e5f5
    style CT fill:#e8f5e8
    style BQ1 fill:#e1f5fe
    style BQ2 fill:#e8f5e8
    style API fill:#fff8e1
```

### 核心組件詳解

#### 1. **協調器 (Coordinator) - `/start-sync`**

**職責範圍：**

- 計算需要同步的時間範圍
- 標準化時間到整點邊界 (冪等性保證)
- 分割時間範圍為小段 (預設 5 分鐘/段)
- 建立對應的 Cloud Tasks
- 監控任務佇列狀態

**核心邏輯：**

```python
def coordinate_sync(start_date, end_date, worker_url):
    # 1. 時間標準化
    start_date = normalize_to_hour_boundary(start_date)
    end_date = normalize_to_hour_boundary(end_date)

    # 2. 時間分段
    segments = generate_time_segments(start_date, end_date)

    # 3. 任務建立
    for segment_start, segment_end in segments:
        create_cloud_task(segment_start, segment_end, worker_url)

    # 4. 狀態更新
    update_sync_status(start_date, end_date)
```

**安全機制：**

- 最大分段數限制 (50 個) 防止任務爆炸
- 時間範圍驗證避免意外的大範圍同步
- 智能檢測時間空隙並發出警告

#### 2. **工作者 (Worker) - `/process-segment`**

**職責範圍：**

- 處理單一時間段的資料同步
- 串流讀取來源資料
- 批次轉換和去重複處理
- MERGE 寫入目標表格
- 進度監控和錯誤處理

**處理流程：**

```python
def sync_time_segment(start_time, end_time):
    # 1. 串流讀取
    events_iterator = get_events_to_sync(start_time, end_time)

    # 2. 批次處理
    current_batch = []
    for event in events_iterator:
        current_batch.append(event)

        if len(current_batch) >= BATCH_SIZE:
            # 3. 資料轉換
            transformed_data = [transform_event_data(e) for e in current_batch]

            # 4. 記憶體去重 (可選)
            if ENABLE_DEDUPLICATION:
                deduplicated_data = deduplicate_in_memory(transformed_data)
            else:
                deduplicated_data = transformed_data

            # 5. 寫入目標
            merge_to_bigquery(deduplicated_data)

            # 6. 批次清理
            current_batch = []
```

**效能優化：**

- 串流處理避免記憶體溢出
- 批次大小動態調整
- 記憶體內去重減少 BigQuery 負荷
- 進度監控 (每 5 分鐘報告)

#### 3. **監控系統 - `/sync-status`**

**功能特色：**

- 即時同步率計算
- BigQuery 成本分析
- 健康狀態評估
- 優化建議生成

**狀態判定邏輯：**

```python
def determine_status(source_count, target_count, last_sync_time, query_cost):
    status = "healthy"
    warnings = []

    # 覆蓋率檢查
    coverage = (target_count / source_count * 100) if source_count > 0 else 0
    if coverage < 95:
        status = "warning"
        warnings.append(f"同步覆蓋率僅 {coverage:.1f}%")

    # 時間檢查
    if last_sync_time:
        hours_since = (datetime.utcnow() - last_sync_time).total_seconds() / 3600
        if hours_since > 6:
            status = "warning"
            warnings.append(f"距離上次同步已 {hours_since:.1f} 小時")

    # 成本檢查
    if query_cost > 0.5:
        status = "warning"
        warnings.append(f"查詢成本過高: ${query_cost:.4f}")

    return status, warnings
```

## 同步策略

服務採用 **Coordinator/Worker** 模式，將同步邏輯拆分為兩個主要部分。

### 角色分工

- **Coordinator (`/start-sync`)**: 協調者。由 Cloud Scheduler 定期觸發，不處理實際資料。其職責是：

  - 計算需要同步的時間範圍
  - 將時間範圍分割成多個小段 (例如每 5 分鐘一段)
  - 為每個時間段建立一個 Cloud Task
  - 追蹤同步狀態和進度

- **Worker (`/process-segment`)**: 工作者。由 Cloud Task 觸發，專門處理單一時間段的資料同步：
  - 從 BigQuery 讀取指定時間範圍的來源資料
  - 轉換資料格式
  - 寫入目標表格
  - 回報處理結果

### 時間分段策略

```python
# 配置參數
MINUTES_PER_SEGMENT = 5        # 每個分段處理 5 分鐘資料
SCHEDULER_INTERVAL_MINUTES = 60 # 每 60 分鐘觸發一次
MAX_SEGMENTS_PER_RUN = 50      # 單次最多 50 個分段
```

**分段優勢：**

1. **容錯性**: 單一分段失敗不影響其他分段
2. **可觀測性**: 細粒度的進度追蹤
3. **資源管理**: 避免長時間佔用資源
4. **並行處理**: 多個分段可同時執行

### 冪等性設計

系統設計為完全冪等，重複執行不會產生重複資料：

1. **時間標準化**: 所有時間都標準化到整點邊界
2. **去重複邏輯**: 使用複合主鍵防止重複插入
3. **MERGE 操作**: 使用 BigQuery MERGE 確保資料唯一性
4. **狀態追蹤**: Firestore 記錄同步狀態避免重複處理

## 🔄 資料轉換邏輯

### 來源資料結構 (`tagtoo_event`)

```json
{
  "permanent": "user-12345",
  "ec_id": 67890,
  "event_time": "2024-01-15T10:30:00.000Z",
  "link": "https://example.com/product/123",
  "event": "{\"name\":\"purchase\",\"value\":99.99,\"currency\":\"USD\",\"custom_data\":{\"order_id\":\"ORD-123\"},\"items\":[{\"id\":\"PROD-456\",\"name\":\"Product Name\",\"price\":99.99,\"quantity\":1}]}",
  "user": "{\"em\":\"hashed_email\",\"ph\":\"hashed_phone\"}",
  "location": {
    "country_code": "TW",
    "region_name": "Taipei",
    "city_name": "Taipei"
  }
}
```

### 目標資料結構 (`integrated_event`)

```json
{
  "permanent": "user-12345",
  "ec_id": 67890,
  "partner_source": "legacy-tagtoo-event",
  "event_time": "2024-01-15T10:30:00.000Z",
  "create_time": "2024-01-15T11:00:00.000Z",
  "link": "https://example.com/product/123",
  "event": "purchase",
  "value": 99.99,
  "currency": "USD",
  "order_id": "ORD-123",
  "items": [
    {
      "id": "PROD-456",
      "name": "Product Name",
      "description": null,
      "price": 99.99,
      "quantity": 1
    }
  ],
  "user": {
    "em": "hashed_email",
    "ph": "hashed_phone"
  },
  "partner_id": null,
  "page": null,
  "location": {
    "country_code": "TW",
    "region_name": "Taipei",
    "city_name": "Taipei"
  },
  "raw_json": null
}
```

### 轉換規則詳解

#### 1. **基本欄位對應**

| 來源欄位     | 目標欄位         | 轉換邏輯   | 備註                    |
| ------------ | ---------------- | ---------- | ----------------------- |
| `permanent`  | `permanent`      | 直接對應   | 使用者永久識別碼        |
| `ec_id`      | `ec_id`          | 直接對應   | 電商平台 ID             |
| `event_time` | `event_time`     | ISO 格式化 | 確保時間格式一致        |
| `link`       | `link`           | 空值處理   | `None` → `""`           |
| `N/A`        | `partner_source` | 固定值     | `"legacy-tagtoo-event"` |
| `N/A`        | `create_time`    | 當前時間   | 同步建立時間 (UTC)      |

#### 2. **JSON 欄位解析**

**事件資料解析 (`event` 欄位):**

```python
def parse_event_data(event_json_str):
    event_data = json.loads(event_json_str)
    return {
        "event": event_data.get("name"),
        "value": event_data.get("value"),
        "currency": event_data.get("currency"),
        "order_id": event_data.get("custom_data", {}).get("order_id"),
        "items": transform_items(event_data.get("items", []))
    }
```

**使用者資料解析 (`user` 欄位):**

```python
def parse_user_data(user_json_str):
    user_data = json.loads(user_json_str)
    return {
        "em": user_data.get("em"),
        "ph": user_data.get("ph")
    }
```

#### 3. **商品陣列轉換**

```python
def transform_items(source_items):
    return [
        {
            "id": item.get("id"),
            "name": item.get("name"),
            "description": None,  # 來源資料中無此欄位
            "price": item.get("price"),
            "quantity": item.get("quantity")
        }
        for item in source_items
        if isinstance(item, dict)
    ]
```

#### 4. **地理位置資料**

```python
def transform_location(source_location):
    if not isinstance(source_location, dict):
        return None

    return {
        "country_code": source_location.get("country_code"),
        "region_name": source_location.get("region_name"),
        "city_name": source_location.get("city_name")
    }
```

### 資料品質保證

#### 1. **型別驗證**

- JSON 解析錯誤處理
- 必要欄位存在性檢查
- 資料型別轉換驗證

#### 2. **空值處理**

- `link` 欄位: `None` → `""`
- JSON 解析失敗: 設為空物件 `{}`
- 陣列欄位: 空陣列而非 `None`

#### 3. **錯誤處理**

```python
def transform_event_data(event):
    try:
        # 資料轉換邏輯
        return transformed_data
    except json.JSONDecodeError as e:
        logger.error(f"JSON 解析失敗: {e}")
        # 回傳部分資料或預設值
    except Exception as e:
        logger.error(f"資料轉換錯誤: {e}")
        # 錯誤處理邏輯
```

## 🎛️ Feature Toggle 系統

### 去重複邏輯控制

系統支援動態開關去重複功能，以平衡資料品質與效能：

```python
# 環境變數控制
ENABLE_DEDUPLICATION = os.environ.get("ENABLE_DEDUPLICATION", "false").lower() == "true"

# 去重複邏輯
if self.enable_deduplication:
    # 複雜的 9 欄位複合主鍵去重
    deduplicated_data = self.deduplicate_in_memory(transformed_data)
else:
    # 直接使用轉換後的資料，依賴 BigQuery 自然去重
    deduplicated_data = transformed_data
```

### 複合主鍵策略

當啟用去重複時，使用以下 9 個欄位組成複合主鍵：

```python
DEDUPLICATION_KEY_FIELDS = [
    "permanent",      # 使用者永久 ID
    "ec_id",         # 電商平台 ID
    "event_time",    # 事件時間 (精確到毫秒)
    "event",         # 事件類型
    "partner_source", # 來源標識 (固定為 "legacy-tagtoo-event")
    "link",          # 頁面連結
    "value",         # 事件價值
    "currency",      # 貨幣
    "order_id"       # 訂單編號
]
```

### Phase 1 優化功能

```bash
# Phase 1 優化開關
USE_OPTIMIZED_MERGE=true       # 簡化 MERGE 語法
DYNAMIC_BATCH_SIZE=true        # 動態批次大小調整
ENABLE_MEMORY_MONITORING=true  # 記憶體監控
```

**優化效果：**

- 處理速度提升 2-3 倍
- BigQuery 查詢成本降低 40-60%
- 記憶體使用更穩定

## 🎯 效能調優

### 批次處理策略

```python
# 基礎配置
BATCH_SIZE = 5000              # 基礎批次大小
MAX_MEMORY_MB = 4096           # 記憶體限制
TARGET_PROCESSING_RATE = 210   # 目標處理速度 (筆/秒)

# 動態調整邏輯
def adjust_batch_size(current_memory_usage, processing_rate):
    if current_memory_usage > MAX_MEMORY_MB * 0.8:
        # 記憶體壓力高，減小批次
        return max(BATCH_SIZE // 2, 1000)
    elif processing_rate < TARGET_PROCESSING_RATE * 0.8:
        # 處理速度慢，可能需要調整
        return min(BATCH_SIZE * 1.2, 10000)
    else:
        return BATCH_SIZE
```

### BigQuery 最佳化

#### 1. **簡化 MERGE 語法**

```sql
-- Phase 1 優化: 簡化版 MERGE (只插入，不更新)
MERGE `target_table` T
USING (SELECT * FROM `temp_table`) S
ON T.permanent = S.permanent
   AND T.ec_id = S.ec_id
   AND T.event_time = S.event_time
   AND T.event = S.event
   AND IFNULL(T.link, '') = IFNULL(S.link, '')
   AND T.value IS NOT DISTINCT FROM S.value
   AND T.currency IS NOT DISTINCT FROM S.currency
   AND T.order_id IS NOT DISTINCT FROM S.order_id
   AND T.partner_source = S.partner_source
WHEN NOT MATCHED THEN
   INSERT (...)
   VALUES (...)
```

#### 2. **成本監控**

```python
def estimate_query_cost(query, job_config):
    # 執行 dry run 估算成本
    job = bigquery_client.query(query, job_config=job_config)

    return {
        "bytes_processed": job.total_bytes_processed,
        "tb_processed": job.total_bytes_processed / (1024**4),
        "estimated_cost_usd": (job.total_bytes_processed / (1024**4)) * 5.0,  # $5 per TB
        "execution_time_seconds": job.ended - job.started
    }
```

### 記憶體管理

#### 1. **串流處理**

```python
def process_events_stream(events_iterator):
    current_batch = []

    for event in events_iterator:
        current_batch.append(event)

        if len(current_batch) >= BATCH_SIZE:
            # 處理批次
            process_batch(current_batch)

            # 立即清理記憶體
            current_batch.clear()
            gc.collect()  # 強制垃圾回收
```

#### 2. **記憶體監控**

```python
import psutil

def monitor_memory_usage():
    process = psutil.Process()
    memory_info = process.memory_info()

    return {
        "rss_mb": memory_info.rss / 1024 / 1024,
        "vms_mb": memory_info.vms / 1024 / 1024,
        "percent": process.memory_percent()
    }
```

## 📊 監控與可觀測性

### 日誌結構

系統使用結構化日誌，便於查詢和分析：

```python
import structlog

logger = structlog.get_logger()

# 處理開始
logger.info("segment_processing_started",
    segment_start=start_time.isoformat(),
    segment_end=end_time.isoformat(),
    batch_size=BATCH_SIZE
)

# 進度報告
logger.info("processing_progress",
    processed_events=total_processed,
    batch_count=batch_count,
    processing_rate=events_per_second,
    memory_usage_mb=current_memory_mb
)

# 處理完成
logger.info("segment_processing_completed",
    segment_start=start_time.isoformat(),
    segment_end=end_time.isoformat(),
    total_events=total_events,
    synced_events=synced_events,
    error_events=error_events,
    duration_seconds=duration,
    status=status
)
```

### 指標收集

```python
from prometheus_client import Counter, Histogram, Gauge

# 計數器
events_processed_total = Counter('events_processed_total', 'Total events processed')
events_synced_total = Counter('events_synced_total', 'Total events synced')
events_error_total = Counter('events_error_total', 'Total events with errors')

# 直方圖
processing_duration = Histogram('processing_duration_seconds', 'Processing duration')
batch_size_histogram = Histogram('batch_size', 'Batch size distribution')

# 量規
current_memory_usage = Gauge('memory_usage_mb', 'Current memory usage in MB')
bigquery_cost = Gauge('bigquery_cost_usd', 'BigQuery query cost in USD')
```

### 警報規則

```yaml
# Cloud Monitoring 警報規則範例
alerts:
  - name: "同步率低"
    condition: sync_coverage_percent < 90
    duration: 10m
    severity: WARNING

  - name: "記憶體使用過高"
    condition: memory_usage_mb > 3500
    duration: 5m
    severity: CRITICAL

  - name: "BigQuery 成本過高"
    condition: bigquery_cost_usd > 1.0
    duration: 1m
    severity: WARNING

  - name: "同步延遲"
    condition: hours_since_last_sync > 8
    duration: 1m
    severity: CRITICAL
```

## 🧪 測試策略

### 測試金字塔

```
         /\
        /  \
       /    \
      / E2E  \     ← 整合測試 (10%)
     /________\
    /          \
   /   整合測試   \   ← API 測試 (20%)
  /______________\
 /                \
/      單元測試      \  ← 單元測試 (70%)
/____________________\
```

### 單元測試

**涵蓋範圍：**

- 資料轉換邏輯
- 時間分段計算
- 去重複演算法
- 錯誤處理機制

```python
# 測試範例
def test_transform_event_data():
    # 準備測試資料
    source_event = create_mock_event()

    # 執行轉換
    result = processor.transform_event_data(source_event)

    # 驗證結果
    assert result["partner_source"] == "legacy-tagtoo-event"
    assert result["event"] == "purchase"
    assert len(result["items"]) == 1
```

### 整合測試

**測試範圍：**

- BigQuery 連接和查詢
- Firestore 狀態管理
- Cloud Tasks 建立
- 端對端資料流

```python
@pytest.mark.integration
def test_full_sync_workflow():
    # 建立測試資料
    test_data = create_test_events()

    # 執行同步
    result = processor.coordinate_sync(start_date, end_date)

    # 驗證結果
    assert result["status"] == "success"
    verify_data_in_target_table()
```

### 效能測試

```python
def test_processing_performance():
    # 大量資料測試
    large_dataset = create_large_test_dataset(size=100000)

    start_time = time.time()
    result = processor.sync_time_segment(start_time, end_time)
    duration = time.time() - start_time

    # 驗證效能指標
    events_per_second = len(large_dataset) / duration
    assert events_per_second >= 200  # 最低效能要求
```

## 🚨 故障排除指南

### 常見問題診斷

#### 1. **同步率低於預期**

**症狀：** 目標表格資料量明顯少於來源表格

**診斷步驟：**

```bash
# 1. 檢查 Cloud Tasks 佇列狀態
gcloud tasks queues describe QUEUE_NAME --location=LOCATION

# 2. 查看 Cloud Run 日誌
gcloud logging read "resource.type=cloud_run_revision"

# 3. 驗證資料同步狀態
curl -s "https://SERVICE_URL/sync-status" | jq .

# 4. 手動執行同步驗證
python scripts/validate_smart_sync_rate.py --start-time "..." --end-time "..."
```

**可能原因：**

- Cloud Tasks 任務失敗或重試
- BigQuery 查詢超時
- 記憶體不足導致 OOM
- 來源資料格式變更

#### 2. **記憶體使用過高**

**症狀：** Cloud Run 實例 OOM 或效能下降

**診斷步驟：**

```python
# 記憶體使用分析
import tracemalloc

tracemalloc.start()
# 執行可疑操作
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")
```

**解決方案：**

- 調降 `BATCH_SIZE` 參數
- 啟用記憶體監控和動態批次調整
- 檢查是否有記憶體洩漏

#### 3. **BigQuery 成本過高**

**症狀：** 查詢成本警告或超出預算

**分析工具：**

```bash
# 查看 BigQuery 使用統計
bq query --use_legacy_sql=false --dry_run \
'SELECT COUNT(*) FROM `project.dataset.table` WHERE ...'

# 成本分析
curl -s "https://SERVICE_URL/sync-status" | jq '.bigquery_analysis'
```

**優化策略：**

- 添加更精確的時間過濾條件
- 使用分區表減少掃描資料量
- 調整查詢邏輯避免全表掃描

#### 4. **同步延遲問題**

**症狀：** 資料同步明顯落後實際時間

**檢查清單：**

- [ ] Cloud Scheduler 觸發正常
- [ ] Cloud Tasks 佇列積壓情況
- [ ] Cloud Run 實例擴展配置
- [ ] BigQuery streaming buffer 延遲

## 📈 效能基準測試

### 測試環境配置

```yaml
# Cloud Run 配置
cpu: 4 vCPU
memory: 4GB
max_instances: 10
timeout: 3600s

# BigQuery 配置
location: asia-east1
source_table_size: ~283M rows
target_table_partitioned: true
```

### 效能指標

| 指標          | Phase 0 (原始) | Phase 1 (優化) | 改善幅度 |
| ------------- | -------------- | -------------- | -------- |
| 處理速度      | 85 筆/秒       | 210+ 筆/秒     | +147%    |
| BigQuery 成本 | $0.15/查詢     | $0.09/查詢     | -40%     |
| 記憶體使用    | 95% 峰值       | 70% 峰值       | -25%     |
| 錯誤率        | 2.3%           | 0.8%           | -65%     |
| 同步延遲      | 2-4 小時       | 1-2 小時       | -50%     |

### 負載測試結果

```
測試條件: 24 小時歷史資料回填 (約 200 萬筆記錄)

Phase 0:
- 執行時間: 6.5 小時
- 記憶體峰值: 3.8GB (95% 使用率)
- 失敗率: 2.3%
- 總成本: $12.50

Phase 1:
- 執行時間: 2.6 小時 (-60%)
- 記憶體峰值: 2.8GB (70% 使用率)
- 失敗率: 0.8% (-65%)
- 總成本: $7.80 (-38%)
```

## 🔄 版本歷程

### v2.0.0 - Phase 1 優化 (2025-07-14)

**重大改進：**

- ✅ 簡化 MERGE 操作，效能提升 2-3 倍
- ✅ 記憶體內去重，減少 BigQuery 負荷
- ✅ 串流處理防止記憶體溢出
- ✅ 成本監控和優化建議
- ✅ Feature Toggle 系統

**技術債務清理：**

- 移除複雜的 SQL 去重邏輯
- 優化批次處理策略
- 改善錯誤處理機制

### v1.0.0 - 初始版本 (2025-07-04)

**核心功能：**

- Cloud Run + Cloud Tasks 架構
- 時間分段處理
- 基本資料轉換
- 監控 API

## 📚 參考資料

### 官方文檔

- [Cloud Run 文檔](https://cloud.google.com/run/docs)
- [Cloud Tasks 文檔](https://cloud.google.com/tasks/docs)
- [BigQuery 文檔](https://cloud.google.com/bigquery/docs)
- [Firestore 文檔](https://cloud.google.com/firestore/docs)

### 最佳實踐指南

- [BigQuery 最佳實踐](https://cloud.google.com/bigquery/docs/best-practices)
- [Cloud Run 效能調優](https://cloud.google.com/run/docs/tips/performance)
- [Python 記憶體最佳化](https://docs.python.org/3/library/gc.html)

### 相關專案文件

- [`README.md`](../README.md) - 專案概述和快速開始
- [`DEPLOYMENT.md`](DEPLOYMENT.md) - 部署流程指南
- [`AUTHENTICATION.md`](AUTHENTICATION.md) - 認證設定說明
- [`TESTING.md`](TESTING.md) - 測試策略文件
- [`PERFORMANCE_OPTIMIZATION_PLAN.md`](PERFORMANCE_OPTIMIZATION_PLAN.md) - 效能優化計畫

---

**總結**: 此文件提供了 Legacy Event Sync Service 的完整技術架構說明，涵蓋系統設計、實作細節、效能調優和故障排除。透過這些資訊，開發團隊可以深入理解系統運作原理，並有效地進行維護和優化。
