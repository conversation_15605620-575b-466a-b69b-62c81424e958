# Google Cloud 認證設定指南

## 📍 憑證位置說明

**容器內統一路徑**: `/app/adc.json`

- 本機 ADC 檔案自動掛載到此位置
- 環境變數 `GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json`
- 無需手動管理 JSON 檔案

## 概述

此專案使用真實 Google Cloud BigQuery 進行開發和生產。根據[Google Cloud ADC 文檔](https://cloud.google.com/docs/authentication/provide-credentials-adc)，我們採用最安全的認證方式。

## 🚀 快速開始

### 開發環境設定

```bash
# 1. 設定認證（一次性）
make auth-setup

# 2. 啟動開發環境
make dev-up

# 3. 執行測試
make test
```

## 🔐 本地開發環境設定

### 使用 ADC（推薦且安全）

1. **設定認證**

   ```bash
   # 設定應用程式預設認證
   gcloud auth application-default login
   ```

2. **確認設定**

   ```bash
   # 檢查認證狀態
   make auth-check
   ```

3. **啟動開發環境**
   ```bash
   # 使用真實 BigQuery 與測試表格
   make dev-up
   ```

**優點**：

- ✅ 安全：憑證自動更新，不會洩露
- ✅ 簡單：一次設定，長期使用
- ✅ 官方推薦：符合 Google Cloud 最佳實踐
- ✅ 真實環境：與生產環境一致，減少部署問題

## 🎯 開發/生產表格設定

### 開發環境

- **來源表格**: `tagtoo-tracking.event_prod.tagtoo_event` (真實資料)
- **目標表格**: `tagtoo-tracking.event_prod.integrated_event_test` (測試表格)

### 生產環境

- **來源表格**: `tagtoo-tracking.event_prod.tagtoo_event`
- **目標表格**: `tagtoo-tracking.event_prod.integrated_event` (正式表格)

## 🏭 生產環境設定

### Cloud Function 部署

```bash
# 建立生產 Service Account
gcloud iam service-accounts create legacy-event-sync-prod \
  --description="Legacy Event Sync Production" \
  --display-name="Legacy Event Sync Prod"

# 授予最小權限
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
  --member="serviceAccount:legacy-event-sync-prod@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
  --member="serviceAccount:legacy-event-sync-prod@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/bigquery.jobUser"

# 部署時指定 Service Account
gcloud functions deploy legacy-event-sync \
  --service-account=legacy-event-sync-prod@YOUR_PROJECT_ID.iam.gserviceaccount.com \
  --runtime=python311 \
  --trigger-topic=legacy-event-sync \
  --source=.
```

## 🛠️ 技術細節

### 認證檔案路徑

| 環境 | 本機路徑                                                | 容器內路徑      | 說明           |
| ---- | ------------------------------------------------------- | --------------- | -------------- |
| 本機 | `~/.config/gcloud/application_default_credentials.json` | `/app/adc.json` | 自動掛載       |
| 容器 | -                                                       | `/app/adc.json` | 程式碼讀取位置 |

### 環境變數

```bash
# 容器內自動設定
GOOGLE_APPLICATION_CREDENTIALS=/app/adc.json
GOOGLE_CLOUD_PROJECT=tagtoo-tracking

# 表格設定
SOURCE_TABLE=tagtoo-tracking.event_prod.tagtoo_event
TARGET_TABLE=tagtoo-tracking.event_prod.integrated_event_test  # 開發環境
```

### 程式碼認證邏輯

程式碼使用簡化的認證邏輯：

1. **預設認證** → 自動偵測 ADC 或 Service Account
2. **專案 ID** → 明確指定 `tagtoo-tracking`
3. **自動重試** → 內建錯誤處理和重試機制

## 🚨 疑難排解

### 常見問題

1. **ADC 檔案不存在**

   ```bash
   ❌ 未找到 ADC 檔案
   ```

   **解決**: `gcloud auth application-default login`

2. **權限不足**

   ```bash
   403 Access Denied
   ```

   **解決**: 檢查 GCP 專案權限設定

3. **容器連不到 BigQuery**

   ```bash
   DefaultCredentialsError
   ```

   **解決**: 確認 ADC 檔案正確掛載

4. **專案 ID 警告**
   ```bash
   No project ID could be determined
   ```
   **解決**: 已自動設定 `GOOGLE_CLOUD_PROJECT` 環境變數

### 除錯指令

```bash
# 檢查認證狀態
make auth-check

# 檢查容器內檔案
make dev-shell
ls -la /app/adc.json
echo $GOOGLE_APPLICATION_CREDENTIALS

# 檢查環境變數
make dev-up
make dev-logs
```

## ✅ 最佳實踐

### ✅ 建議做法

1. **使用真實 BigQuery + 測試表格**

   ```bash
   make dev-up  # 使用 integrated_event_test 表格
   ```

2. **使用 ADC 進行認證**

   ```bash
   make auth-setup  # 一次性設定
   ```

3. **分離開發/生產表格**

   - 開發：`integrated_event_test`
   - 生產：`integrated_event`

4. **生產環境使用專用 Service Account**
   - 每個環境使用獨立的 Service Account
   - 遵循最小權限原則

### ❌ 避免做法

1. **❌ 下載 Service Account Key 檔案**

   - 安全風險高
   - 管理複雜
   - 不符合最佳實踐

2. **❌ 硬編碼認證資訊**

   - 絕對不要放在程式碼中

3. **❌ 過度授權**

   - 只給必要的最小權限

4. **❌ 直接寫入正式表格**
   - 開發時應使用測試表格

## 🔄 開發工作流程

```bash
# 開發環境設定（一次性）
make auth-setup      # 設定認證

# 日常開發
make dev-up          # 啟動開發環境
make test            # 執行測試
make dev-down        # 停止服務

# 同步測試
curl -X POST http://localhost:8080/sync \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-12-20T00:00:00Z", "end_date": "2024-12-20T01:00:00Z"}'

# 清理
make clean           # 清理環境
```

## 📊 資料流向圖

```
┌─────────────────────┐    ┌──────────────────────────┐    ┌─────────────────────────┐
│   tagtoo_event      │───▶│   Legacy Event Sync      │───▶│  integrated_event_test  │
│  (生產資料來源)      │    │     (開發環境)           │    │     (測試目標)          │
└─────────────────────┘    └──────────────────────────┘    └─────────────────────────┘
                                        │
                                        ▼
                           ┌──────────────────────────┐
                           │   integrated_event       │
                           │     (生產目標)           │
                           └──────────────────────────┘
```

---

**總結**: 使用真實 BigQuery + 測試表格的組合是最安全且實用的開發方式，確保開發環境與生產環境一致性。
