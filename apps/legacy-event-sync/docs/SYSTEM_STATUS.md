# Legacy Event Sync - 系統狀態檢查

## 📊 **生產環境狀態報告 (2025-07-09)**

### 🏗️ **基礎設施狀態**

#### Cloud Run 服務

```bash
# 檢查服務狀態
gcloud run services describe legacy-event-sync-prod --region=asia-east1

# 服務資訊
- 名稱: legacy-event-sync-prod
- 狀態: Ready
- URL: https://legacy-event-sync-prod-shiddvur4q-de.a.run.app
- 映像: asia-east1-docker.pkg.dev/tagtoo-tracking/integrated-event-apps/legacy-event-sync:2aee454c3be19ccf8277533ea2dd32c6dec72898
```

#### Cloud Scheduler

```bash
# 檢查排程任務
gcloud scheduler jobs describe legacy-event-sync-prod --location=asia-east1

# 排程設定
- 名稱: legacy-event-sync-prod
- 排程: 0 2/4 * * * (每 4 小時執行，從凌晨 2 點開始)
- 時區: Asia/Taipei
- 狀態: ENABLED
- 最近執行: 2025-07-08 18:00 (HTTP 200 成功)
```

#### Cloud Tasks

```bash
# 檢查任務佇列
gcloud tasks queues describe legacy-event-sync-segments-prod --location=asia-east1

# 佇列狀態
- 名稱: legacy-event-sync-segments-prod
- 狀態: RUNNING
```

### 📊 **資料同步狀況**

#### 來源表格 (tagtoo_event)

```sql
-- 最新資料統計 (2025-07-09)
SELECT
  COUNT(*) as total_records,
  MAX(event_time) as latest_event
FROM `tagtoo-tracking.event_prod.tagtoo_event`
WHERE DATE(event_time) >= '2025-07-01';

-- 結果: 28,336 萬筆記錄，持續更新至 2025-07-09 07:03:16
```

#### 目標表格 (integrated_event)

```sql
-- 檢查 legacy-tagtoo-event 同步狀況
SELECT
  partner_source,
  COUNT(*) as record_count,
  MAX(create_time) as latest_sync
FROM `tagtoo-tracking.event_prod.integrated_event`
WHERE partner_source = 'legacy-tagtoo-event'
GROUP BY partner_source;

-- 目前狀態: 尚無 legacy-tagtoo-event 資料 (待首次同步)
```

#### 其他 partner_source 狀況

```sql
-- 目前表格中的資料來源
SELECT
  partner_source,
  COUNT(*) as record_count,
  MAX(create_time) as latest_sync
FROM `tagtoo-tracking.event_prod.integrated_event`
GROUP BY partner_source;

-- 結果:
-- reurl: 2,318 萬筆記錄 (最新: 2025-07-08 19:00:02)
```

### 🔍 **診斷檢查**

#### 服務日誌檢查

```bash
# 檢查最近的錯誤或警告
gcloud logging read 'resource.type="cloud_run_revision" AND resource.labels.service_name="legacy-event-sync-prod" AND timestamp>="2025-07-08T00:00:00Z" AND severity>=WARNING' --limit=5
```

#### 認證狀態

- ✅ 服務已移除 allUsers 權限
- ✅ 使用 Service Account 認證
- ⚠️ 健康檢查端點返回 403 (符合預期的安全配置)

### 🎯 **待處理動作項目**

1. **套用 Terraform 更新**:

   ```bash
   cd apps/legacy-event-sync/terraform
   terraform apply -var="environment=prod"
   ```

   預期變更:

   - 新增 Service Account invoker 權限
   - 新增錯誤監控告警
   - 更新服務標籤

2. **驗證首次同步**:

   - 監控下次排程執行 (預計: 今日 18:00)
   - 確認 `legacy-tagtoo-event` 資料出現
   - 檢查同步作業日誌

3. **設定監控**:
   - 確認錯誤告警正常運作
   - 驗證 Cloud Monitoring 指標收集

### 🚨 **潛在問題與解決方案**

#### 同步作業未產生資料

可能原因：

1. 認證問題 - Service Account 權限不足
2. Firestore 連線問題
3. BigQuery 寫入權限問題

解決步驟：

1. 執行 terraform apply 更新權限配置
2. 手動觸發同步測試 (需要認證 token)
3. 檢查 Firestore 中的同步狀態記錄

#### 認證配置

目前服務需要 OIDC 認證才能存取端點：

```bash
# 取得認證 token (範例)
gcloud auth print-identity-token

# 使用 token 存取服務
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
     https://legacy-event-sync-prod-shiddvur4q-de.a.run.app/health
```

### 📞 **支援聯絡**

**維護團隊**: Data Team
**最後檢查**: 2025-07-09
**下次檢查**: 2025-07-10 (確認首次同步結果)
