# 2025-07-14 資料重複事件完整記錄

## 📋 事件概要

**事件類型**: 資料重複問題
**影響等級**: 嚴重
**發生時間**: 2025-07-14
**解決時間**: 2025-07-15 10:52
**狀態**: ✅ 已解決 + 🚀 技術改進中

## 🔍 問題發現與分析

### 初始問題

- **重複率**: 79% (嚴重重複)
- **原因**: Cloud Tasks 重試 5 次 × Worker Timeout → 每筆資料被處理 5 次
- **影響**: ~150M 筆記錄中有 ~118M 筆是重複的

### 根本原因

1. **Worker Timeout**: Gunicorn 300s < 實際處理時間 (~25分鐘)
2. **Cloud Tasks 重試**: max_attempts=5，每次 timeout 都重試
3. **非冪等操作**: `insert_rows_json()` 直接插入，無防重複機制

### 🔍 深度分析：原始資料也有重複

**重大發現**: 經過 BigQuery 分析發現，原始 `tagtoo_event` 表本身就存在大量重複：

- 單一秒內同一 user 的同一事件可重複多達 80 次
- 例：`permanent + ec_id + event_time + event.name = 'focus'` 組合可重複 80 筆
- **這些是完全相同的記錄**，不是業務邏輯的正常重複

## ✅ 緊急修復方案

### 第一階段：立即止血 (2025-07-14 17:06)

```yaml
修復項目:
  - Cloud Tasks max_attempts: 5 → 1 ✅
  - GUNICORN_TIMEOUT: 300s → 1740s ✅
  - Docker CMD 動態環境變數: ✅

效果:
  - 重複率: 79% → 1.07% (改善 98.6%) ✅
  - WORKER TIMEOUT: 完全消除 ✅
```

### 第二階段：技術改進 (2025-07-15 14:30)

**MERGE 操作實作**: 實現完全冪等性

#### 原始設計問題

```sql
-- 錯誤的主鍵設計（會覆蓋合法記錄）
ON T.permanent = S.permanent
   AND T.ec_id = S.ec_id
   AND T.event_time = S.event_time
```

#### 修正後設計

```sql
-- 使用完整內容比對 + 源頭去重
MERGE target T
USING (
  SELECT * EXCEPT(row_num) FROM (
    SELECT *, ROW_NUMBER() OVER (
      PARTITION BY permanent, ec_id, event_time, event,
                   partner_source, link, value, currency, order_id
      ORDER BY create_time
    ) as row_num
    FROM temp_table
  ) WHERE row_num = 1
) S
ON T.permanent = S.permanent AND ... -- 完整比對條件
```

## 📊 修復效果驗證

### 數據對比

| 階段                    | 重複率     | 說明             |
| ----------------------- | ---------- | ---------------- |
| 修復前                  | 79%        | 嚴重重複         |
| 緊急修復後              | 1.07%      | 98.6% 改善       |
| **MERGE 實作後 (預期)** | **< 0.1%** | **接近完全冪等** |

### 服務穩定性

- **處理時間**: 25 分鐘 (< 29 分鐘限制)
- **錯誤率**: 0%
- **WORKER TIMEOUT**: 完全消除

## 🎯 技術改進要點

### 冪等性設計

1. **多層次去重**:

   - 源頭去重：臨時表內去重
   - 目標去重：MERGE 比對現有資料
   - 完整比對：使用所有關鍵欄位

2. **容錯機制**:

   - MERGE 失敗 → 自動回退到 insert_rows_json
   - 臨時表自動過期 (1小時)
   - 完整錯誤日誌

3. **資源管理**:
   - 自動清理臨時表
   - 執行時間監控
   - 影響行數統計

## 📚 關鍵學習

### 技術層面

1. **原始資料品質**: 需要在源頭就處理重複問題
2. **冪等性設計**: 所有可重試操作都必須冪等
3. **完整測試**: 需要針對邊界情況進行充分測試

### 運維層面

1. **監控覆蓋**: 資料品質監控同樣重要
2. **分階段修復**: 立即止血 + 長期改進
3. **文件記錄**: 完整記錄問題分析和解決過程

## 🔗 相關資源

- **緊急修復詳細記錄**: `emergency-fix-details.md`
- **MERGE 操作技術實作**: `merge-implementation.md`
- **監控腳本**: `monitoring-scripts.md`
- **後續改進計畫**: `improvement-roadmap.md`

---

**事件負責人**: GitHub Copilot
**技術審查**: [待補充]
**最後更新**: 2025-07-15 14:30
