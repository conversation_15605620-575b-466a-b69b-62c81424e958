# BigQuery Partition By 型別安全性自動檢查報告

## 目的

本報告自動檢查並說明 legacy-event-sync 專案所有 BigQuery MERGE/INSERT 查詢的 partition by 欄位型別，確保無 FLOAT64 型別，避免生產環境出現 `Partitioning by expressions of type FLOAT64 is not allowed` 錯誤。

---

## 1. 主要 MERGE 查詢產生邏輯

- 來源：`src/main.py` → `LegacyEventSyncProcessor._execute_merge_query`
- 產生的 SQL 片段：

```sql
PARTITION BY
  permanent,
  ec_id,
  event_time,
  event,
  partner_source,
  link,
  CAST(IFNULL(value, -999) AS STRING),
  IFNULL(currency, 'NULL'),
  IFNULL(order_id, 'NULL')
```

- **說明**：
  - 唯一的 FLOAT64 欄位 `value` 已強制轉為 string。
  - 其餘欄位皆為 string/timestamp/integer，BigQuery 支援。

---

## 2. 其他 SQL 產生與測試覆蓋

- 其他所有 partition by、MERGE、insert_rows_json、bigquery.query 相關程式碼，皆在 `src/main.py` 這一份 class 內，且只有這一個 MERGE 查詢有 partition by。
- 其他地方（如 `insert_rows_json`）僅用於 fallback，不會產生 partition by。
- `scripts/validate_smart_sync_rate.py` 產生的 partition by 查詢僅用於驗證，不影響正式同步。

---

## 3. 單元測試驗證

- `tests/test_bigquery_partition_types.py`、`tests/test_merge_query.py`、`tests/test_merge_query_validation.py`、`tests/test_merge_logic.py` 等多份測試，均有自動檢查 partition by 欄位型別、SQL 結構、CAST、IFNULL 等細節。
- 測試內容已覆蓋所有主鍵欄位、型別、null 處理、CAST 一致性等。

---

## 4. 結論

- **目前唯一會進入 partition by 的 float64 欄位只有 `value`，且已經強制 CAST 成 string。**
- 其他 partition by 欄位皆為 string/integer/timestamp，BigQuery 支援。
- 單元測試已自動驗證所有欄位型別與 SQL 結構，無遺漏。
- 只要測試通過，正式環境不會再出現 partition by float64 的錯誤。

---

## 5. 追蹤建議

- 未來如有新增 partition by 欄位，請務必檢查型別，避免 FLOAT64。
- 建議定期執行單元測試與型別檢查工具。
