# 監控腳本與驗證指令

## 🔍 重複率監控

### 日常重複率檢查

```bash
# 檢查今日重複率
bq query --use_legacy_sql=false "
SELECT
  DATE(create_time) as date,
  COUNT(*) as total_records,
  COUNT(DISTINCT CONCAT(permanent, '_', CAST(ec_id AS STRING), '_',
                       CAST(event_time AS STRING), '_', event, '_', partner_source)) as unique_records,
  ROUND(COUNT(*) / COUNT(DISTINCT CONCAT(permanent, '_', CAST(ec_id AS STRING), '_',
                                        CAST(event_time AS STRING), '_', event, '_', partner_source)), 2) as duplication_ratio
FROM \`tagtoo-tracking.event_prod.integrated_event\`
WHERE partner_source = 'legacy-tagtoo-event'
  AND DATE(create_time) = CURRENT_DATE()
GROUP BY DATE(create_time)
"
```

### 週期性重複率趨勢

```bash
# 檢查過去7天的重複率趨勢
bq query --use_legacy_sql=false "
SELECT
  DATE(create_time) as date,
  ROUND(COUNT(*) / COUNT(DISTINCT CONCAT(permanent, '_', CAST(ec_id AS STRING), '_',
                                        CAST(event_time AS STRING), '_', event, '_', partner_source)), 2) as duplication_ratio,
  COUNT(*) as total_records
FROM \`tagtoo-tracking.event_prod.integrated_event\`
WHERE partner_source = 'legacy-tagtoo-event'
  AND DATE(create_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
GROUP BY DATE(create_time)
ORDER BY date DESC
"
```

## 🚀 服務健康監控

### Cloud Run 服務狀態

```bash
# 檢查服務健康
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  "https://legacy-event-sync-prod-shiddvur4q-de.a.run.app/health"

# 檢查服務版本
gcloud run services describe legacy-event-sync-prod \
  --region=asia-east1 \
  --format="value(spec.template.metadata.name,spec.template.spec.containers[0].image)"
```

### 日誌監控

```bash
# 檢查是否有 WORKER TIMEOUT 錯誤（應該沒有）
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"
  AND textPayload:"WORKER TIMEOUT"' \
  --limit=10 --freshness=2h

# 檢查 MERGE 操作日誌
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"
  AND textPayload:"MERGE"' \
  --limit=10 --freshness=1h

# 檢查錯誤日誌
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"
  AND severity>=ERROR' \
  --limit=20 --freshness=2h
```

## 📊 資料同步監控

### 同步完整性檢查

```bash
# 比較來源與目標資料量
bq query --use_legacy_sql=false "
WITH source_count AS (
  SELECT COUNT(*) as source_total
  FROM \`tagtoo-tracking.event_prod.tagtoo_event\`
  WHERE DATE(event_time) = CURRENT_DATE()
),
target_count AS (
  SELECT COUNT(*) as target_total
  FROM \`tagtoo-tracking.event_prod.integrated_event\`
  WHERE partner_source = 'legacy-tagtoo-event'
    AND DATE(event_time) = CURRENT_DATE()
)
SELECT
  source_total,
  target_total,
  ROUND(target_total / source_total * 100, 2) as sync_coverage_percent
FROM source_count, target_count
"
```

### 最新同步時間檢查

```bash
# 檢查最後同步時間
bq query --use_legacy_sql=false "
SELECT
  MAX(create_time) as last_sync_time,
  DATETIME_DIFF(CURRENT_DATETIME(), MAX(create_time), MINUTE) as minutes_since_last_sync
FROM \`tagtoo-tracking.event_prod.integrated_event\`
WHERE partner_source = 'legacy-tagtoo-event'
"
```

## ⚠️ 告警閾值

### 自動告警條件

```bash
# 重複率告警（> 1.2）
DUPLICATION_RATIO_THRESHOLD=1.2

# 同步延遲告警（> 120 分鐘）
SYNC_DELAY_THRESHOLD=120

# 錯誤率告警（> 1%）
ERROR_RATE_THRESHOLD=0.01
```

### 週期性檢查腳本

```bash
#!/bin/bash
# 檔案：monitor-legacy-sync.sh

# 檢查重複率
DUPLICATION_RATIO=$(bq query --use_legacy_sql=false --format=csv --quiet "
SELECT ROUND(COUNT(*) / COUNT(DISTINCT CONCAT(permanent, '_', CAST(ec_id AS STRING), '_',
                                             CAST(event_time AS STRING), '_', event, '_', partner_source)), 2)
FROM \`tagtoo-tracking.event_prod.integrated_event\`
WHERE partner_source = 'legacy-tagtoo-event' AND DATE(create_time) = CURRENT_DATE()
" | tail -n 1)

# 檢查同步延遲
SYNC_DELAY=$(bq query --use_legacy_sql=false --format=csv --quiet "
SELECT DATETIME_DIFF(CURRENT_DATETIME(), MAX(create_time), MINUTE)
FROM \`tagtoo-tracking.event_prod.integrated_event\`
WHERE partner_source = 'legacy-tagtoo-event'
" | tail -n 1)

# 告警邏輯
if (( $(echo "$DUPLICATION_RATIO > 1.2" | bc -l) )); then
  echo "🚨 ALERT: Duplication ratio is $DUPLICATION_RATIO (> 1.2)"
fi

if (( $SYNC_DELAY > 120 )); then
  echo "🚨 ALERT: Sync delay is $SYNC_DELAY minutes (> 120)"
fi
```

## 🔧 MERGE 操作特定監控

### MERGE 執行統計

```bash
# 檢查 MERGE 操作影響的行數
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"
  AND textPayload:"MERGE query executed"' \
  --limit=10 --freshness=2h --format="value(textPayload)"
```

### 臨時表清理監控

```bash
# 檢查是否有臨時表清理失敗
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"
  AND textPayload:"Failed to cleanup temp table"' \
  --limit=10 --freshness=4h
```

## 📈 效能監控

### BigQuery 查詢成本

```bash
# 監控 BigQuery 查詢成本（每日）
bq query --use_legacy_sql=false "
SELECT
  DATE(creation_time) as date,
  SUM(total_bytes_processed) / POW(10, 12) as tb_processed,
  SUM(total_bytes_processed) / POW(10, 12) * 5 as estimated_cost_usd
FROM \`tagtoo-tracking.region-asia-east1.INFORMATION_SCHEMA.JOBS_BY_PROJECT\`
WHERE DATE(creation_time) = CURRENT_DATE()
  AND job_type = 'QUERY'
  AND user_email LIKE '%legacy-event-sync%'
GROUP BY DATE(creation_time)
"
```

### Cloud Run 資源使用

```bash
# 檢查 CPU 和記憶體使用率
gcloud logging read 'resource.type="cloud_run_revision"
  AND resource.labels.service_name="legacy-event-sync-prod"' \
  --filter="textPayload:cpu OR textPayload:memory" \
  --limit=5 --freshness=1h
```

---

**使用方式**: 將這些腳本加入 crontab 或 Cloud Scheduler 進行定期執行
**告警整合**: 可與 Slack、Email 或 PagerDuty 整合
