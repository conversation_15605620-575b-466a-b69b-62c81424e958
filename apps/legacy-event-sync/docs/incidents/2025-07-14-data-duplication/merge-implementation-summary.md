# MERGE 冪等性實作總結

## 🎯 實作完成狀態

### ✅ 已完成項目

1. **詳細程式碼註解**

   - `_merge_to_bigquery()`: 主流程控制與錯誤處理
   - `_create_temp_table_and_load_data()`: 臨時表建立與資料載入
   - `_execute_merge_query()`: 核心 MERGE 邏輯實作

2. **業務邏輯分析**

   - 深入分析原始 tagtoo_event 表重複情況
   - 發現每秒80筆「重複」focus事件實際具有業務差異
   - 決定保留複雜主鍵設計以避免錯誤合併

3. **完整註解說明**
   - 🔍 問題背景說明
   - 🎯 設計策略解釋
   - 🔑 主鍵邏輯詳述
   - ⚡ 冪等性保證機制
   - 🚫 設計決策說明

### 🧪 測試準備

1. **測試腳本**: `tests/test_merge_idempotency.py`

   - 完整冪等性測試案例
   - 多種重複情況驗證
   - 自動化結果評估

2. **部署腳本**: `scripts/deploy_merge_implementation.sh`
   - 自動化部署流程
   - 服務狀態檢查
   - 重複率監控

## 🔍 去重邏輯澄清（方案 A：智慧保留策略）

### 最終決策：保留有業務價值的差異記錄

經過深入討論，我們確認採用**方案 A：智慧保留策略**

### 策略說明

**integrated_event 的資料量會少於 tagtoo_event，但保留所有有業務價值的記錄**

我們的 MERGE 實作會在兩個層面進行智慧去重：

1. **批次內部去重**（`WHERE row_num = 1`）

   ```sql
   SELECT * EXCEPT(row_num)
   FROM (
     SELECT *,
       ROW_NUMBER() OVER (
         PARTITION BY [完整主鍵組合]  -- 8個欄位的完整比對
         ORDER BY create_time  -- 保留最早建立的記錄
       ) as row_num
     FROM temp_table
   )
   WHERE row_num = 1  -- ✅ 只保留每組重複中的第一筆
   ```

2. **與目標表智慧比對**（`WHEN NOT MATCHED`）
   - 只插入目標表中不存在的記錄
   - 使用相同的完整主鍵邏輯進行比對

### 什麼會被去重？什麼會被保留？

✅ **會被去重（合併為一筆）**：

- 所有 8 個主鍵欄位完全相同的記錄
- 同步過程中產生的真正重複
- 網路重試或故障恢復產生的重複

✅ **會被保留（視為不同記錄）**：

- 不同 `link` 的 focus 事件（用戶可能快速切換頁面）
- 不同 `order_id` 的交易事件（不同訂單的重要區別）
- 不同 `value` 或 `currency` 的事件（重要業務資訊）
- 任何一個主鍵欄位有差異的記錄

### 實際範例

假設原始 tagtoo_event 同一秒內有 80 筆 focus 事件：

**情況 1**：78 筆完全相同 + 2 筆有不同 link

```
結果：3 筆記錄（78→1 + 1 + 1）
去重率：77/80 = 96.25%
```

**情況 2**：80 筆完全相同

```
結果：1 筆記錄（80→1）
去重率：79/80 = 98.75%
```

**情況 3**：80 筆各有不同 link

```
結果：80 筆記錄（全部保留）
去重率：0%
```

## 🔑 核心設計決策

### 複合主鍵策略

```sql
PARTITION BY
  permanent,           -- 用戶 ID
  ec_id,              -- 電商平台 ID
  event_time,         -- 事件時間（秒級精度）
  event,              -- 事件名稱
  partner_source,     -- 資料來源標識
  link,               -- 當前頁面 URL（重要差異點）
  IFNULL(value, -999),      -- 事件價值
  IFNULL(currency, 'NULL'), -- 貨幣類型
  IFNULL(order_id, 'NULL')  -- 訂單 ID
```

### 業務邏輯保護

- **不過度簡化去重**：保留有業務價值的「重複」記錄
- **精確比對**：確保只有真正重複的記錄才會去重
- **NULL 值處理**：避免 NULL 比較導致的邏輯錯誤

## 📊 預期效果

### 冪等性保證

- ✅ 相同資料重複處理 → 不產生重複記錄
- ✅ 部分重複資料 → 只插入真正新的記錄
- ✅ 網路重試 → 結果完全一致
- ✅ 故障恢復 → 資料狀態一致

### 效能影響

- **輕微延遲增加**：每批約2-3秒（臨時表操作）
- **記憶體使用**：臨時表暫時佔用
- **穩定性大幅提升**：完全冪等性

## 🚀 GitHub Actions 部署計畫

### 建議的部署流程

由於沒有 dev 環境，建議直接使用 GitHub Actions 部署到生產環境：

1. **本地測試**

   ```bash
   cd /Users/<USER>/tagtoo/integrated-event/apps/legacy-event-sync
   python -m pytest tests/test_merge_idempotency.py -v
   ```

2. **提交變更**

   ```bash
   git add .
   git commit -m "feat: 實作 MERGE 冪等性邏輯以防止資料重複"
   git push origin main
   ```

3. **GitHub Actions 自動部署**

   - 檢測 `apps/legacy-event-sync/` 變更
   - 建置 Docker 映像
   - 部署到 Cloud Run
   - 執行健康檢查

4. **部署後驗證**
   ```bash
   make logs     # 查看部署日誌
   make status   # 檢查服務狀態
   ```

### 測試檔案重新整理

- ✅ 移動測試到正確位置：`tests/test_merge_idempotency.py`
- ✅ 清理重複的文件檔案
- ✅ 更新部署腳本適應生產環境

### 立即可執行

1. **執行本地測試**

   ```bash
   cd /Users/<USER>/tagtoo/integrated-event/apps/legacy-event-sync
   python -m pytest tests/test_merge_idempotency.py -v
   ```

2. **準備 GitHub 部署**

   ```bash
   ./scripts/deploy_merge_implementation.sh  # 檢查並準備部署
   ```

3. **透過 Git 觸發部署**
   ```bash
   git add .
   git commit -m "feat: 實作 MERGE 冪等性邏輯"
   git push origin main
   ```

### 驗證指標

- **重複率**：應維持在 1.07% 左右（不再增加）
- **同步效能**：批次處理時間增加但在可接受範圍
- **錯誤率**：應該為 0（完全冪等性）

## 💡 關鍵技術亮點

### 智能去重邏輯

```sql
-- 兩階段去重：臨時表內部 + 與目標表比對
SELECT * EXCEPT(row_num)
FROM (
  SELECT *,
    ROW_NUMBER() OVER (
      PARTITION BY [完整主鍵組合]
      ORDER BY create_time  -- 保留最早記錄
    ) as row_num
  FROM temp_table
)
WHERE row_num = 1
```

### 完整錯誤處理

- 臨時表建立失敗 → 拋出異常
- 資料載入失敗 → 詳細錯誤記錄
- MERGE 執行失敗 → 回滾並記錄
- 清理失敗 → 警告但不影響主流程

### 業務邏輯保護

- 不會錯誤合併不同 link 的事件
- 不會覆蓋不同 order_id 的交易
- 保留原始資料的業務複雜性

## 🔍 程式碼品質

### 註解完整性

- **中文註解**：便於團隊理解
- **Emoji 標示**：快速識別操作類型
- **業務邏輯說明**：解釋設計決策原因
- **技術實作細節**：幫助維護和除錯

### 可維護性

- 清晰的方法分離
- 完整的錯誤處理
- 詳細的日誌記錄
- 自動化測試支援

這個實作為 legacy-event-sync 服務提供了企業級的資料同步穩定性，確保在任何情況下都能維持資料一致性和完整性。
