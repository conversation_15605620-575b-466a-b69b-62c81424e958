# Legacy Event Sync - 測試指南

## 🧪 測試架構概覽

Legacy Event Sync 服務採用**明確分離**的測試策略，以確保測試既快速又可靠，同時控制雲端資源成本。

- **純單元測試 (`tests/test_main.py`)**: 完全不接觸任何外部 GCP 服務。使用 Mock 技術隔離所有外部依賴（BigQuery, Firestore, Cloud Tasks），專注於驗證核心業務邏輯的正確性。
- **真實整合測試 (`tests/test_integration.py`)**: 連接至真實的 GCP 環境，測試服務與 BigQuery 和 Firestore 的完整互動流程。此測試成本極低，並透過自動清理機制確保環境整潔。

## 🔧 測試環境設定

所有測試都在 Docker 容器中執行，以確保環境一致性。

### 前置需求

1. **Google Cloud 認證**:

   ```bash
   # 為本地開發設定 ADC（Application Default Credentials）
   make auth-setup
   ```

   此指令會引導您完成 `gcloud auth application-default login`。

2. **Docker 環境**:
   ```bash
   # 啟動本地開發與測試容器
   make dev-up
   ```

### 🎯 智慧表格命名系統 (整合測試)

為避免並行測試衝突並自動清理資源，整合測試採用以下機制：

- **唯一命名**: 測試表格以時間戳命名 (`..._test_YYYYMMDD_HHMMSS_fff`)。
- **自動過期**: 所有測試表格設定 1 小時的 TTL，超時後 BigQuery 會自動刪除。
- **即時清理**: 測試完成後，`pytest` 的 `fixture` 會主動刪除所建立的表格。

此設計確保測試不會在 `temp` 資料集中留下任何垃圾資料。

## 🚀 執行測試

### 執行所有測試

```bash
# 執行所有測試（單元 + 整合）
# CI/CD 環境與本地開發環境均可使用此指令
make test
```

### 執行特定類型的測試

```bash
# 僅執行快速的單元測試
make test-unit

# 僅執行需要真實 GCP 連線的整合測試
make test-integration

# 在容器中直接執行 pytest
docker-compose exec legacy-event-sync pytest -v

# 監看模式（檔案變動時自動重新執行測試）
make test-watch
```

## 📋 測試執行流程

### 單元測試流程 (`test_main.py`)

1. **完全隔離**: 使用 `@patch` 和 `MagicMock` 模擬所有 GCP 客戶端。
2. **邏輯驗證**: 測試 Coordinator/Worker 邏輯、Flask 端點行為和資料轉換函式。
3. **快速反饋**: 執行速度極快，適合在開發過程中頻繁運行。

### 整合測試流程 (`test_integration.py`)

1. **環境準備**: `processor` fixture 在測試開始前，於 BigQuery 的 `temp` 資料集中建立來源和目標表格。
2. **資料注入**: `test_data` fixture 向來源表格插入少量（2筆）測試資料。
3. **流程驗證**: 執行完整的同步工作流程，包括讀取、轉換和寫入，並驗證新增欄位（partner_id/page/location/raw_json）對應。
4. **狀態斷言**: 驗證目標表格中的資料是否符合預期。
5. **自動清理**: `processor` fixture 在測試結束後，刪除所有建立的表格。
