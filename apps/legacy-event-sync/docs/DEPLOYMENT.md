# Legacy Event Sync - 部署指南

## 🎯 **當前生產環境狀態 (2025-07-09)**

### ✅ **已完成部署：**

- **共享基礎設施**: 已部署並運行正常
- **Cloud Run 服務**: `legacy-event-sync-prod` 已上線
- **Cloud Scheduler**: 已配置定期執行 (每 4 小時)
- **Cloud Tasks**: 佇列已建立並運行
- **服務認證**: 已移除公開存取，使用 Service Account

### 🔄 **待執行項目：**

1. **套用最新 Terraform 配置**:
   ```bash
   cd apps/legacy-event-sync/terraform
   terraform apply -var="environment=prod"
   ```
2. **驗證首次同步執行**
3. **確認監控告警設定**

---

## 📋 **總覽**

本文件說明如何部署 Legacy Event Sync 服務，包括開發環境設定和生產環境部署。

⚠️ **重要**：在部署應用服務之前，必須先部署 **Shared Infrastructure**，否則會遇到 remote state 讀取錯誤。

## 🏗️ **部署順序**

### 1. 部署 Shared Infrastructure（必須先執行）

Shared Infrastructure 包含所有應用服務共用的資源：

- Service Accounts
- BigQuery Datasets
- Artifact Registry
- Monitoring 設定

```bash
# 方法 1: 使用 Makefile（推薦）
cd apps/legacy-event-sync
make deploy-shared-infrastructure

# 方法 2: 直接使用腳本
cd infrastructure/terraform/shared
./deploy.sh prod

# 方法 3: 手動執行 Terraform
cd infrastructure/terraform/shared
terraform init
terraform workspace select prod || terraform workspace new prod
terraform plan -var="environment=prod"
terraform apply -var="environment=prod"
```

### 2. 部署應用服務

```bash
cd apps/legacy-event-sync
make deploy-prod
```

## 🔧 **GitHub Actions CI/CD 設定**

### **必要的 GitHub Repository Variables**

在 GitHub Repository Settings > Secrets and variables > Actions > **Variables** 中設定：

| Variable 名稱                | 說明                                 | 範例值                                                                                         |
| ---------------------------- | ------------------------------------ | ---------------------------------------------------------------------------------------------- |
| `WIF_PROVIDER`               | Workload Identity Provider 完整路徑  | `projects/************/locations/global/workloadIdentityPools/github-actions/providers/github` |
| `INTEGRATED_EVENT_RUNNER_SA` | 應用執行 Service Account Email       | `<EMAIL>`                              |
| `TERRAFORM_USER_SA`          | Terraform 部署 Service Account Email | `<EMAIL>`                                |

### **🚀 手動觸發部署 (新功能)**

現在支援直接從 GitHub Actions 手動觸發部署，無需修改代碼：

#### **使用方法**

1. **前往 GitHub Actions**: `https://github.com/Tagtoo/integrated-event/actions`
2. **選擇 Workflow**: 點擊 "Apps CI/CD Pipeline"
3. **手動觸發**: 點擊 "Run workflow" 按鈕
4. **配置選項**:
   - **Service**: 輸入 `legacy-event-sync`（或留空部署所有服務）
   - **Environment**: 選擇 `prod` 或 `dev`
5. **開始部署**: 點擊 "Run workflow"

#### **支援的觸發方式**

- **指定服務部署**: 在 Service 欄位輸入 `legacy-event-sync`
- **環境選擇**: 支援 `prod` 和 `dev` 環境
- **靈活配置**: 可留空 Service 欄位以部署所有變動的服務

#### **優勢**

- ✅ **無代碼污染**: 不需要修改代碼來觸發部署
- ✅ **環境選擇**: 可選擇部署到開發或生產環境
- ✅ **服務指定**: 可精確指定要部署的服務
- ✅ **即時觸發**: 立即開始部署流程

### **CI/CD 工作流程改進**

我們已經在 GitHub Actions 中增加了 `ensure-shared-infrastructure` 步驟，確保：

1. **自動檢查**：在部署應用服務前，自動檢查 shared infrastructure 是否存在
2. **自動部署**：如果不存在，會自動部署 shared infrastructure
3. **依賴管理**：應用服務部署依賴於 shared infrastructure 完成

### **CI/CD 流程圖**

```
觸發方式：
├── 🔄 Code Push (自動)         ← push 到 main 分支時自動觸發
├── 🎯 Manual Trigger (手動)    ← GitHub Actions 手動觸發 ✨新功能
└── 📥 Pull Request (自動)      ← PR 時觸發測試

CI/CD 流程：
1. 📋 detect-changes          ← 偵測變動的服務 (支援手動指定)
2. 🔍 terraform-validation    ← 早期 Terraform 驗證
3. 🧪 test                    ← 應用程式測試
4. 🏗️ ensure-shared-infrastructure ← 🆕 確保 shared infrastructure 存在 (已修正 git diff 問題)
5. 🚀 deploy                  ← 部署應用服務
6. 📊 summary                 ← 生成部署報告
```

### **🛠️ 重要修正記錄**

#### **ensure-shared-infrastructure git diff 錯誤修正 (2025-01-19)**

**問題描述：**
在 push 觸發 CI/CD 時，`ensure-shared-infrastructure` job 會出現 "Invalid symmetric difference expression" 錯誤，導致部署失敗。

**解決方案：**

- 添加 `fetch-depth: 0` 確保 git 完整歷史記錄
- 增強 SHA 存在性檢查和錯誤處理
- 改善手動觸發和 push 觸發的邏輯分離

**影響：**
現在所有觸發方式（push、手動觸發、PR）都能穩定運作。

## 🚨 **常見問題與解決方案**

### **問題 1: Remote State 讀取錯誤**

```
Error: Unsupported attribute
on main.tf line 20, in provider "google":
20:   project = local.shared_outputs.project_id
This object does not have an attribute named "project_id".
```

**原因**：Shared infrastructure 尚未部署，remote state 不存在。

**解決方案**：

```bash
# 先部署 shared infrastructure
make deploy-shared-infrastructure

# 然後部署應用服務
make deploy-prod
```

### **問題 2: CI/CD 認證失敗**

檢查以下項目：

- [ ] GitHub Repository Variables 是否正確設定
- [ ] WIF Provider 路徑是否正確
- [ ] Service Account 是否有足夠權限
- [ ] Repository 是否在 Workload Identity 綁定中

### **問題 3: BigQuery 連線失敗**

檢查以下項目：

- [ ] 專案 ID 是否正確
- [ ] Service Account 是否有 BigQuery 權限
- [ ] 表格是否存在且可存取

### **問題 4: Terraform Workspace 錯誤**

```bash
# 檢查 workspace 狀態
cd infrastructure/terraform/shared
terraform workspace list

# 創建缺失的 workspace
terraform workspace new prod
```

## 🏗️ **本地開發環境**

### **認證設定**

```bash
# 設定本地認證
make auth-setup

# 或手動執行
gcloud auth application-default login
```

### **啟動開發環境**

```bash
# 啟動服務
make dev-up

# 查看日誌
make dev-logs

# 執行測試
make test
```

## 🚀 **生產環境部署**

### **完整部署流程**

```bash
# 1. 檢查認證
make auth-check

# 2. 部署 shared infrastructure（如果尚未部署）
make deploy-shared-infrastructure

# 3. 部署應用服務
make deploy-prod
```

### **透過 CI/CD 自動部署**

推送到 `main` 分支會自動觸發部署流程，包括：

1. 自動檢查並部署 shared infrastructure
2. 部署應用服務

## 🔍 **驗證部署**

### **檢查 Shared Infrastructure**

```bash
cd infrastructure/terraform/shared
terraform workspace select prod
terraform output
```

應該看到類似輸出：

```
project_id = "tagtoo-tracking"
region = "asia-east1"
service_account_email = "<EMAIL>"
```

### **檢查應用服務**

```bash
cd apps/legacy-event-sync/terraform
terraform workspace select prod
terraform output
```

### **查看即時日誌**

```bash
# 本地環境
make dev-logs

# 生產環境
gcloud functions logs read legacy-event-sync --project=tagtoo-tracking
```

### **健康檢查**

```bash
# 本地
curl http://localhost:8080/health

# 生產
curl https://YOUR-CLOUD-RUN-URL/health
```

## 📊 **監控與日誌**

部署完成後，可以在以下位置監控服務：

- **Cloud Run**: GCP Console > Cloud Run
- **Cloud Scheduler**: GCP Console > Cloud Scheduler
- **BigQuery**: GCP Console > BigQuery
- **Logs**: GCP Console > Logging

## 🔄 **更新流程**

### **更新 Shared Infrastructure**

```bash
cd infrastructure/terraform/shared
terraform plan -var="environment=prod"
terraform apply -var="environment=prod"
```

### **更新應用服務**

```bash
cd apps/legacy-event-sync
make deploy-prod
```

## 📝 **相關文件**

- [DEVELOPMENT.md](DEVELOPMENT.md) - 開發指南
- [AUTHENTICATION.md](AUTHENTICATION.md) - 認證設定
- [TERRAFORM_VALIDATION.md](TERRAFORM_VALIDATION.md) - Terraform 驗證機制
- [README.md](../README.md) - 專案總覽

---

💡 **提示**：遵循正確的部署順序（先 shared infrastructure，後應用服務）可以避免大部分部署問題。CI/CD 流程已經自動化了這個順序。
