# Legacy Event Sync 記憶體問題分析

## 📊 當前配置 (2025-07-11)

### Cloud Run 規格

- **CPU**: 4 cores
- **Memory**: 8GB (8Gi)
- **Timeout**: 1800s (30分鐘)
- **Max Instances**: 10
- **Min Instances**: 0

### 排程配置

- **Sync Schedule**: `0 2/4 * * *` (每4小時執行一次)
- **Hours per Segment**: 4小時 (原始設定)
- **Batch Size**: 50,000
- **Max Concurrent Segments**: 6

### 資料量分析 (基於實際查詢)

```sql
-- 查詢時間: 2025-07-11
-- 查詢範圍: 4小時區間的資料量
SELECT
  DATE_TRUNC(event_time, HOUR) as hour,
  COUNT(*) as event_count,
  AVG(LENGTH(JSON_EXTRACT_SCALAR(event, '$'))) as avg_event_size,
  AVG(LENGTH(JSON_EXTRACT_SCALAR(user, '$'))) as avg_user_size
FROM `tagtoo-tracking.event_prod.tagtoo_event`
WHERE event_time >= '2025-07-10 14:00:00'
  AND event_time < '2025-07-10 18:00:00'
GROUP BY hour
ORDER BY hour;
```

**結果**:

- **總記錄數**: 5,658,235 筆 (4小時)
- **平均每小時**: ~1,414,559 筆
- **平均事件大小**: ~345 bytes
- **平均用戶資料大小**: ~220 bytes
- **估計記憶體需求**: ~3.2GB (僅原始資料) + Python物件開銷 ≈ **6-8GB**

## 🚨 錯誤發生時間記錄

### 記憶體相關錯誤

```
時間: 2025-07-10 18:23:43 +0000
錯誤: [CRITICAL] WORKER TIMEOUT (pid:10)
後續: [ERROR] Worker (pid:10) was sent SIGKILL! Perhaps out of memory?
處理時間範圍: 2025-07-10 14:00:08 to 2025-07-10 18:00:08 (4小時)
```

```
時間: 2025-07-10 18:23:11 +0000
錯誤: [ERROR] Worker (pid:62) was sent SIGKILL! Perhaps out of memory?
處理時間範圍: 2025-07-09 18:00:08 to 2025-07-09 22:00:08 (4小時)
```

### 錯誤模式分析

- **錯誤頻率**: 每次同步任務都失敗
- **失敗時機**: 在載入大量資料到記憶體時 (`list(query.result())`)
- **Stack Trace**: 指向 BigQuery 查詢結果載入
- **Worker Timeout**: 30秒後被 kill

## 🧪 測試計劃

### 階段1: 記憶體增加測試

**目標**: 確認問題是否為記憶體不足

**變更**:

- Memory: 8GB → 16GB
- 保持其他設定不變
- 記錄錯誤出現時間變化

**預期結果**:

- 如果是記憶體不足: 錯誤應該延後出現或消失
- 如果是代碼問題: 錯誤時間不會有顯著變化

### 階段2: 代碼優化 (如果記憶體增加無效)

**目標**: 實現串流處理，避免大量記憶體使用

**變更**:

- 修改 `get_events_to_sync` 使用迭代器而非 `list()`
- 實現批次串流處理
- 減少單次處理的時間範圍

## 📈 監控指標

### 記錄項目

1. **錯誤發生時間** (相對於任務開始時間)
2. **處理的資料量** (記錄數)
3. **記憶體使用峰值**
4. **處理持續時間**
5. **成功率**

### 記錄格式

```
Date: YYYY-MM-DD
Config: CPU/Memory/Timeout
Data Range: START_TIME to END_TIME
Record Count: N
Error Time: Relative to start (if any)
Error Type: OOM/Timeout/Other
Success: Yes/No
```

## 🎯 決策標準

### 記憶體增加有效

- 繼續使用當前代碼架構
- 考慮是否需要進一步優化批次大小

### 記憶體增加無效

- 立即實施代碼重構
- 採用串流處理架構
- 考慮分段處理策略

---

_建立時間: 2025-07-11_
_記錄者: AI Assistant_
