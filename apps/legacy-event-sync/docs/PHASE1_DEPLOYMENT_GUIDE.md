# Phase 1 優化部署指南

## 🚀 優化內容總覽

### 已實施的優化

1. **優化版 MERGE 操作** - 簡化複雜邏輯，預期 2-3 倍效能提升
2. **動態批次大小調整** - 根據時段調整批次大小，避免超時
3. **記憶體內去重** - 減少 BigQuery 運算負荷
4. **Storage Write API 準備** - 基礎設施已就緒，可選擇啟用

### 預期效能提升

- **目標**: 從 70 筆/秒提升到 210+ 筆/秒（3 倍提升）
- **高流量時段**: 同步率從 61-74% 提升到 90%+
- **低流量時段**: 維持 99%+ 同步率

## 📋 部署步驟

### Step 1: 更新依賴

```bash
cd /Users/<USER>/tagtoo/integrated-event/apps/legacy-event-sync
docker-compose build --no-cache
```

### Step 2: 設定環境變數

將以下設定加入到實際的 `.env` 檔案：

```bash
# Phase 1 優化設定
USE_OPTIMIZED_MERGE=true
DYNAMIC_BATCH_SIZE=true
USE_STORAGE_WRITE_API=false  # 保守部署，先關閉
ENABLE_DEDUPLICATION=false   # 提升效能
```

### Step 3: 測試部署（建議）

```bash
# 測試動態批次大小計算
python test_phase1_optimization.py --test-batch-only

# 測試 1 小時資料同步
python test_phase1_optimization.py \
  --start "2025-07-18 01:00:00" \
  --end "2025-07-18 02:00:00"
```

### Step 4: 部署到生產環境

```bash
# 使用現有的部署腳本
make deploy
```

### Step 5: 監控與驗證

```bash
# 等待部署完成後，驗證同步率
docker-compose exec legacy-event-sync python3 scripts/validate_smart_sync_rate.py \
  --start "$(date -u -d '2 hours ago' '+%Y-%m-%d %H:00:00')" \
  --end "$(date -u '+%Y-%m-%d %H:00:00')" \
  --sample_size 3
```

## 🔍 監控指標

### 關鍵指標

1. **處理速度**: 目標 210+ 筆/秒
2. **同步率**: 高流量時段 90%+，低流量時段 99%+
3. **錯誤率**: < 1%
4. **記憶體使用**: 控制在 2GB 以內

### 監控方法

- **即時監控**: Cloud Run 控制台觀察 CPU/記憶體使用
- **日誌監控**: 搜尋 "Optimized operation completed" 確認使用新邏輯
- **效能驗證**: 使用 `validate_smart_sync_rate.py` 腳本

## ⚠️ 風險評估與緩解

### 潛在風險

1. **記憶體使用增加**: 記憶體內去重需要額外記憶體
2. **複雜邏輯變更**: 新的去重邏輯可能有未預見的問題
3. **環境變數依賴**: 需要正確設定環境變數

### 緩解策略

1. **分階段部署**: 先啟用優化 MERGE，再考慮 Storage Write API
2. **監控記憶體**: 密切觀察 Cloud Run 記憶體使用情況
3. **回滾計畫**: 可隨時設定 `USE_OPTIMIZED_MERGE=false` 回到原始邏輯

## 🔄 回滾計畫

如果出現問題，可立即回滾：

```bash
# 緊急回滾：停用所有優化
export USE_OPTIMIZED_MERGE=false
export DYNAMIC_BATCH_SIZE=false

# 重新部署
make deploy
```

## 📊 驗證成功標準

### 短期驗證（24 小時內）

- [ ] 部署成功，無部署錯誤
- [ ] 日誌顯示 "Optimized operation completed"
- [ ] 同步率在各時段均有改善
- [ ] 無新的錯誤類型出現

### 中期驗證（一週內）

- [ ] 整體同步率達到 85% 以上
- [ ] 高流量時段同步率提升到 90% 以上
- [ ] 記憶體使用穩定在合理範圍
- [ ] 無資料完整性問題

### 長期驗證（一月內）

- [ ] 穩定達到目標效能（210+ 筆/秒）
- [ ] 準備 Phase 2 優化（並行處理）
- [ ] 考慮啟用 Storage Write API

## 🚀 下一階段計畫

### Phase 2: 並行處理優化

- 啟用 `PARALLEL_PROCESSING=true`
- 調整 `MAX_PARALLEL_WORKERS` 根據資源情況
- 進一步提升效能到 300+ 筆/秒

### Phase 3: Storage Write API

- 在穩定後啟用 `USE_STORAGE_WRITE_API=true`
- 達到最終目標 400+ 筆/秒
- 完成所有效能優化

---

_建立日期: 2025-07-18_
_負責人: AI Coding Agent_
_相關文件: PERFORMANCE_OPTIMIZATION_PLAN.md_
