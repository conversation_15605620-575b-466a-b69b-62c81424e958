"""
Legacy Event Sync Service - Cloud Run Worker/Coordinator

This service, running on Cloud Run, synchronizes data from the legacy
`tagtoo_event` table to the unified `integrated_event` table.

It operates in two modes:
- Coordinator (/start-sync): Triggered by Cloud Scheduler, it determines the
  time range to sync and creates tasks for each segment.
- Worker (/process-segment): Triggered by Cloud Tasks, it processes a single
  time segment of data.
"""

import json
import logging
import os
import re
from datetime import datetime, timedelta
import pytz

# 讀取服務版本資訊
try:
    from config import config as app_config  # noqa: E402  # 延遲導入避免循環
    SERVICE_VERSION = getattr(app_config.service, "version", "unknown")
except Exception:  # pragma: no cover
    SERVICE_VERSION = "unknown"

# 儲存於建置階段的 12 位 commit (由 CI/CD 傳入環境變數)
SERVICE_COMMIT = os.getenv("COMMIT_SHA") or os.getenv("SERVICE_COMMIT") or "unknown"

from typing import Any, Dict, List, Optional

from flask import Flask, jsonify, request
from google.cloud import bigquery, firestore, tasks_v2
from google.cloud.exceptions import GoogleCloudError, NotFound

# --- Logging Configuration ---
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# --- Environment Configuration ---
PROJECT_ID = os.environ.get("PROJECT_ID", "tagtoo-tracking")
ENVIRONMENT = os.environ.get("ENVIRONMENT", "dev")
GCP_LOCATION = os.environ.get("GCP_LOCATION", "asia-east1")

SOURCE_TABLE = os.environ.get("SOURCE_TABLE", "tagtoo-tracking.event_prod.tagtoo_event")
TARGET_TABLE_DATASET = os.environ.get("BIGQUERY_DATASET", "event_test")
TARGET_TABLE = os.environ.get(
    "TARGET_TABLE", f"{PROJECT_ID}.{TARGET_TABLE_DATASET}.integrated_event"
)

TASK_QUEUE_NAME = os.environ.get("TASK_QUEUE_NAME")
WORKER_URL = os.environ.get("WORKER_URL")
WORKER_URL_PATH = os.environ.get("WORKER_URL_PATH", "/process-segment")
BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "10000"))  # 進一步減少批次大小避免SSL錯誤

# MINUTES_PER_SEGMENT 支援任意正 float（如 3 代表 3 分鐘）
def _parse_minutes_per_segment():
    val = os.environ.get("MINUTES_PER_SEGMENT", "5")
    try:
        fval = float(val)
        if fval <= 0:
            return 5.0
        return fval
    except Exception:
        return 5.0
MINUTES_PER_SEGMENT = _parse_minutes_per_segment()

def _parse_scheduler_interval_minutes():
    """
    自動推算 Cloud Scheduler cron 間隔（分鐘）。
    若無法自動推算，預設 60 分鐘。
    """
    cron = os.environ.get("SYNC_SCHEDULE")
    if not cron:
        return 60
    # 僅支援常見格式："0 * * * *"、"*/5 * * * *"、"15 2 * * *" 等
    parts = cron.strip().split()
    if len(parts) != 5:
        return 60
    minute, hour, *_ = parts
    # 1. "*/N * * * *" 代表每 N 分鐘
    m = re.match(r"\*/(\d+)", minute)
    if m:
        return int(m.group(1))
    # 2. "0 * * * *" 代表每小時
    if minute == "0" and hour == "*":
        return 60
    # 3. "M H * * *" 代表每天某時刻，間隔 1440 分鐘
    if minute.isdigit() and hour.isdigit():
        return 1440
    # 4. 其他複雜格式 fallback
    return 60

SCHEDULER_INTERVAL_MINUTES = _parse_scheduler_interval_minutes()

def _parse_excluded_event_types():
    """
    解析需要排除的事件類型清單。
    環境變數 EXCLUDED_EVENT_TYPES 應為 JSON 格式的字串陣列。
    範例: ["focus"] 或 ["focus", "scroll"]
    """
    excluded_str = os.environ.get("EXCLUDED_EVENT_TYPES", '["focus"]')
    try:
        excluded_list = json.loads(excluded_str)
        if isinstance(excluded_list, list):
            return excluded_list
        else:
            logger.warning(f"EXCLUDED_EVENT_TYPES 不是陣列格式，使用預設值: {excluded_str}")
            return ["focus"]
    except json.JSONDecodeError as e:
        logger.warning(f"無法解析 EXCLUDED_EVENT_TYPES: {e}，使用預設值")
        return ["focus"]

EXCLUDED_EVENT_TYPES = _parse_excluded_event_types()


class BigQueryCostTracker:
    """
    BigQuery 成本追踪器

    追踪 BigQuery 查詢的成本、執行時間和資源使用情況，
    提供詳細的成本分析和最佳化建議。
    """

    def __init__(self):
        """初始化成本追踪器"""
        self.costs = []
        self.total_cost_usd = 0.0
        self.total_bytes_processed = 0
        self.total_execution_time = 0.0
        self.start_time = datetime.utcnow()

        # 成本閾值設定
        self.cost_thresholds = {
            "warning": 0.1,   # $0.10 USD - 中等成本警告
            "high": 0.5,      # $0.50 USD - 高成本警告
            "critical": 2.0   # $2.00 USD - 關鍵成本警告
        }

    def track_operation(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float, segment_info: Optional[str] = None):
        """
        追踪單個 BigQuery 操作的成本

        Args:
            operation_name: 操作名稱（如 'get_events_to_sync', 'merge_operation'）
            cost_estimate: 成本估算結果（來自 estimate_query_cost）
            execution_time: 實際執行時間（秒）
            segment_info: 時間段資訊（可選，用於識別具體的時間範圍）
        """
        cost_usd = cost_estimate.get("estimated_cost_usd", 0)
        bytes_processed = cost_estimate.get("bytes_processed", 0)
        tb_processed = cost_estimate.get("tb_processed", 0)

        cost_info = {
            "operation": operation_name,
            "segment": segment_info,
            "timestamp": datetime.utcnow().isoformat(),
            "cost_usd": cost_usd,
            "bytes_processed": bytes_processed,
            "tb_processed": tb_processed,
            "execution_time_seconds": execution_time,
            "query_slots": cost_estimate.get("query_slots", 0),
            "cost_level": self._classify_cost_level(cost_usd)
        }

        # 記錄錯誤資訊（如果有）
        if "error" in cost_estimate:
            cost_info["error"] = cost_estimate["error"]

        self.costs.append(cost_info)
        self.total_cost_usd += cost_usd
        self.total_bytes_processed += bytes_processed
        self.total_execution_time += execution_time

        # 即時成本警告
        if cost_usd >= self.cost_thresholds["critical"]:
            logger.error(f"🚨 關鍵成本警告: {operation_name} 花費 ${cost_usd:.4f} USD (超過 ${self.cost_thresholds['critical']} 閾值)")
        elif cost_usd >= self.cost_thresholds["high"]:
            logger.warning(f"⚠️ 高成本警告: {operation_name} 花費 ${cost_usd:.4f} USD")
        elif cost_usd >= self.cost_thresholds["warning"]:
            logger.info(f"💡 中等成本提醒: {operation_name} 花費 ${cost_usd:.4f} USD")

    def _classify_cost_level(self, cost_usd: float) -> str:
        """根據成本金額分類成本等級"""
        if cost_usd >= self.cost_thresholds["critical"]:
            return "critical"
        elif cost_usd >= self.cost_thresholds["high"]:
            return "high"
        elif cost_usd >= self.cost_thresholds["warning"]:
            return "warning"
        else:
            return "low"

    def get_summary(self) -> Dict[str, Any]:
        """
        取得詳細的成本摘要

        Returns:
            包含總成本、操作統計、最佳化建議等資訊的字典
        """
        session_duration = (datetime.utcnow() - self.start_time).total_seconds()

        # 統計各種操作類型
        operation_stats = {}
        cost_by_level = {"low": 0, "warning": 0, "high": 0, "critical": 0}

        for cost_info in self.costs:
            op_type = cost_info["operation"].split("(")[0]  # 取得操作類型（移除參數部分）
            if op_type not in operation_stats:
                operation_stats[op_type] = {
                    "count": 0,
                    "total_cost": 0,
                    "total_time": 0,
                    "total_bytes": 0
                }

            operation_stats[op_type]["count"] += 1
            operation_stats[op_type]["total_cost"] += cost_info["cost_usd"]
            operation_stats[op_type]["total_time"] += cost_info["execution_time_seconds"]
            operation_stats[op_type]["total_bytes"] += cost_info["bytes_processed"]

            cost_by_level[cost_info["cost_level"]] += cost_info["cost_usd"]

        # 生成最佳化建議
        optimization_suggestions = self._generate_optimization_suggestions()

        return {
            "session_info": {
                "start_time": self.start_time.isoformat(),
                "duration_seconds": round(session_duration, 2),
                "total_operations": len(self.costs)
            },
            "cost_summary": {
                "total_cost_usd": round(self.total_cost_usd, 4),
                "total_bytes_processed": self.total_bytes_processed,
                "total_tb_processed": round(self.total_bytes_processed / (1024**4), 6),
                "total_execution_time_seconds": round(self.total_execution_time, 2),
                "average_cost_per_operation": round(self.total_cost_usd / len(self.costs), 4) if self.costs else 0,
                "cost_efficiency_score": self._calculate_efficiency_score()
            },
            "cost_breakdown": {
                "by_level": {level: round(cost, 4) for level, cost in cost_by_level.items()},
                "by_operation": {op: {
                    "count": stats["count"],
                    "total_cost_usd": round(stats["total_cost"], 4),
                    "avg_cost_usd": round(stats["total_cost"] / stats["count"], 4),
                    "total_execution_time": round(stats["total_time"], 2),
                    "total_gb_processed": round(stats["total_bytes"] / (1024**3), 3)
                } for op, stats in operation_stats.items()}
            },
            "operations": self.costs,
            "optimization": {
                "suggestions": optimization_suggestions,
                "estimated_monthly_cost": round(self.total_cost_usd * 30, 2),  # 假設每日執行
                "cost_projection": self._project_costs()
            }
        }

    def _calculate_efficiency_score(self) -> float:
        """
        計算成本效率分數 (0-100)

        基於執行時間、資料處理量和成本的平衡來評估效率
        """
        if not self.costs:
            return 0.0

        # 基準指標（這些數值可以根據實際經驗調整）
        baseline_cost_per_gb = 0.005  # $5 per TB = $0.005 per GB
        baseline_time_per_gb = 2.0    # 2 秒每 GB

        total_gb = self.total_bytes_processed / (1024**3) if self.total_bytes_processed > 0 else 1
        actual_cost_per_gb = self.total_cost_usd / total_gb
        actual_time_per_gb = self.total_execution_time / total_gb

        # 計算效率（越接近基準越好）
        cost_efficiency = min(baseline_cost_per_gb / actual_cost_per_gb, 2.0) if actual_cost_per_gb > 0 else 1.0
        time_efficiency = min(baseline_time_per_gb / actual_time_per_gb, 2.0) if actual_time_per_gb > 0 else 1.0

        # 綜合分數（50% 成本效率 + 50% 時間效率）
        efficiency_score = (cost_efficiency + time_efficiency) / 2 * 50

        return round(min(efficiency_score, 100.0), 1)

    def _generate_optimization_suggestions(self) -> List[str]:
        """根據實際使用情況生成最佳化建議"""
        suggestions = []

        if self.total_cost_usd > self.cost_thresholds["high"]:
            suggestions.append("總成本較高，建議檢查查詢是否有不必要的全表掃描")

        if self.total_bytes_processed > 100 * (1024**3):  # > 100 GB
            suggestions.append("資料掃描量大，建議使用分區表和叢集表來減少掃描範圍")

        high_cost_ops = [op for op in self.costs if op["cost_level"] in ["high", "critical"]]
        if high_cost_ops:
            suggestions.append(f"發現 {len(high_cost_ops)} 個高成本操作，建議優化這些查詢")

        slow_ops = [op for op in self.costs if op["execution_time_seconds"] > 30]
        if slow_ops:
            suggestions.append(f"發現 {len(slow_ops)} 個執行緩慢的操作，建議檢查索引和查詢計畫")

        if len(self.costs) > 20:
            suggestions.append("查詢次數較多，考慮合併相似的查詢以減少 API 呼叫")

        if not suggestions:
            suggestions.append("查詢效率良好，繼續保持現有的最佳化策略")

        return suggestions

    def _project_costs(self) -> Dict[str, Any]:
        """預測未來成本趨勢"""
        if not self.costs:
            return {"daily": 0, "monthly": 0, "yearly": 0}

        # 基於當前會話的平均成本推算
        session_hours = (datetime.utcnow() - self.start_time).total_seconds() / 3600
        hourly_rate = self.total_cost_usd / session_hours if session_hours > 0 else 0

        return {
            "daily_estimate": round(hourly_rate * 24, 4),
            "monthly_estimate": round(hourly_rate * 24 * 30, 2),
            "yearly_estimate": round(hourly_rate * 24 * 365, 2)
        }

    def reset(self):
        """重置追踪器（用於新的會話）"""
        self.costs = []
        self.total_cost_usd = 0.0
        self.total_bytes_processed = 0
        self.total_execution_time = 0.0
        self.start_time = datetime.utcnow()


def create_bigquery_client():
    """Creates and validates a BigQuery client."""
    try:
        logger.info(f"Initializing BigQuery client for project: {PROJECT_ID}")
        client = bigquery.Client(project=PROJECT_ID)
        list(client.list_datasets(max_results=1))  # Test connection
        logger.info("✅ BigQuery client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize BigQuery client: {e}", exc_info=True)
        raise

def create_firestore_client():
    """Creates a Firestore client."""
    try:
        logger.info(f"Initializing Firestore client for project: {PROJECT_ID}")
        client = firestore.Client(project=PROJECT_ID)
        logger.info("✅ Firestore client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Firestore client: {e}", exc_info=True)
        raise

def create_tasks_client():
    """Creates a Cloud Tasks client."""
    try:
        logger.info("Initializing Cloud Tasks client.")
        client = tasks_v2.CloudTasksClient()
        logger.info("✅ Cloud Tasks client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Cloud Tasks client: {e}", exc_info=True)
        raise


class LegacyEventSyncProcessor:
    """Handles the logic for the event synchronization."""

    def __init__(self, source_table: str, target_table: str, task_queue_name: Optional[str] = None, worker_url: Optional[str] = None, enable_deduplication: Optional[bool] = None):
        """Initializes the processor with configuration and lazy-loaded clients."""
        self.source_table = source_table
        self.target_table = target_table
        self.task_queue_name = task_queue_name or TASK_QUEUE_NAME
        self.worker_url = worker_url or WORKER_URL or self._build_worker_url()

        # 🎛️ Feature Toggle: 去重複邏輯控制
        # 預設從環境變數讀取，如果沒有設定則預設為 False (預設停用去重複)
        if enable_deduplication is None:
            self.enable_deduplication = os.environ.get("ENABLE_DEDUPLICATION", "false").lower() in ("true", "1", "yes")
        else:
            self.enable_deduplication = enable_deduplication

        logger.info(f"去重複邏輯狀態: {'啟用' if self.enable_deduplication else '停用'}")

        self._bigquery_client = None
        self._tasks_client = None
        self._firestore_client = None
        self.last_sync_doc_ref = (
            f"legacy_sync_metadata/{self.source_table}-{self.target_table}"
        )

        # 💰 初始化 BigQuery 成本追踪器
        self.cost_tracker = None  # 將在需要時初始化
        self._enable_cost_tracking = os.environ.get("ENABLE_COST_TRACKING", "true").lower() in ("true", "1", "yes")

        if self._enable_cost_tracking:
            logger.info("💰 BigQuery 成本追踪已啟用")
        else:
            logger.info("💰 BigQuery 成本追踪已停用")

    def _build_worker_url(self) -> str:
        """Build worker URL dynamically when running in Cloud Run."""
        # This will be set dynamically in the coordinate_sync method
        # when we have access to the Flask request context
        return None

    def _ensure_cost_tracker_initialized(self):
        """確保成本追踪器已初始化（如果啟用）"""
        if self._enable_cost_tracking and self.cost_tracker is None:
            self.cost_tracker = BigQueryCostTracker()
            logger.debug("💰 初始化 BigQuery 成本追踪器")

    def _track_bigquery_operation(self, operation_name: str, cost_estimate: Dict[str, Any], execution_time: float, segment_info: Optional[str] = None):
        """
        追踪 BigQuery 操作的成本（如果啟用）

        Args:
            operation_name: 操作名稱
            cost_estimate: 成本估算結果
            execution_time: 執行時間
            segment_info: 時間段資訊（可選）
        """
        if self._enable_cost_tracking:
            self._ensure_cost_tracker_initialized()
            self.cost_tracker.track_operation(operation_name, cost_estimate, execution_time, segment_info)

    def get_streaming_buffer_status(self) -> Dict[str, Any]:
        """獲取來源表格的 Streaming Buffer 狀況"""
        try:
            table = self.bigquery_client.get_table(self.source_table)

            if table.streaming_buffer is None:
                return {
                    "has_streaming_buffer": False,
                    "message": "表格沒有 Streaming Buffer 資料",
                    "safe_offset_minutes": 0
                }

            buffer = table.streaming_buffer
            current_time = datetime.utcnow()

            # 計算延遲時間
            oldest_entry = buffer.oldest_entry_time
            if oldest_entry:
                # 確保時區一致性
                if oldest_entry.tzinfo is None:
                    oldest_entry = oldest_entry.replace(tzinfo=pytz.utc)
                if current_time.tzinfo is None:
                    current_time = current_time.replace(tzinfo=pytz.utc)

                delay_seconds = (current_time - oldest_entry).total_seconds()
                delay_minutes = delay_seconds / 60

                # 建議安全偏移時間：延遲時間的 1.5 倍，但至少 30 分鐘
                safe_offset_minutes = max(30, delay_minutes * 1.5)
            else:
                delay_seconds = delay_minutes = 0
                safe_offset_minutes = 30

            return {
                "has_streaming_buffer": True,
                "estimated_bytes": buffer.estimated_bytes,
                "estimated_rows": buffer.estimated_rows,
                "oldest_entry_time": oldest_entry,
                "current_time": current_time,
                "delay_seconds": delay_seconds,
                "delay_minutes": delay_minutes,
                "estimated_size_mb": buffer.estimated_bytes / (1024 * 1024) if buffer.estimated_bytes else 0,
                "safe_offset_minutes": int(safe_offset_minutes),
                "current_offset_minutes": 120,  # 目前設定的 2 小時
                "is_safe": safe_offset_minutes <= 120,
                "recommendation": "當前偏移充足" if safe_offset_minutes <= 120 else f"建議增加偏移至 {int(safe_offset_minutes)} 分鐘"
            }

        except Exception as e:
            logger.warning(f"無法獲取 Streaming Buffer 狀況: {e}")
            return {
                "has_streaming_buffer": False,
                "error": str(e),
                "safe_offset_minutes": 120  # 保守預設值
            }

    @property
    def bigquery_client(self):
        if self._bigquery_client is None:
            self._bigquery_client = create_bigquery_client()
        return self._bigquery_client

    @property
    def tasks_client(self):
        if self._tasks_client is None:
            self._tasks_client = create_tasks_client()
        return self._tasks_client

    @property
    def firestore_client(self):
        if self._firestore_client is None:
            self._firestore_client = create_firestore_client()
        return self._firestore_client

    def get_last_sync_time(self) -> Optional[datetime]:
        """Retrieves the last successful sync time from Firestore."""
        try:
            doc = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}").get()
            if doc.exists:
                return datetime.fromisoformat(doc.to_dict()["last_sync_utc"])
        except Exception as e:
            logger.error(f"Failed to get last sync time: {e}")
        return None

    def set_last_sync_time(self, sync_time: datetime):
        """Stores the last successful sync time in Firestore."""
        try:
            doc_ref = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}")
            doc_ref.set({"last_sync_utc": sync_time.isoformat()})
        except Exception as e:
            logger.error(f"Failed to set last sync time: {e}")

    def get_events_to_sync(self, start_time: datetime, end_time: datetime):
        """Returns a BigQuery result iterator for events within a time range - memory efficient."""
        segment_info = f"{start_time.strftime('%Y%m%d%H%M')}-{end_time.strftime('%Y%m%d%H%M')}"

        # 建構事件類型過濾條件
        event_filter_condition = ""
        if EXCLUDED_EVENT_TYPES:
            excluded_list = [f"'{event_type}'" for event_type in EXCLUDED_EVENT_TYPES]
            excluded_str = ", ".join(excluded_list)
            event_filter_condition = f" AND event.name NOT IN ({excluded_str})"
            logger.info(f"過濾事件類型: {EXCLUDED_EVENT_TYPES}")

        query = f"""
            SELECT *
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time{event_filter_condition}
            ORDER BY event_time
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
            ]
        )

        # 💰 成本估算
        cost_estimate = self.estimate_query_cost(query, job_config)

        try:
            logger.info(f"Querying events from {start_time} to {end_time}")

            # 執行查詢並追踪時間
            execution_start = datetime.utcnow()
            query_job = self.bigquery_client.query(query, job_config=job_config)
            result = query_job.result()  # 返回 RowIterator，不使用 list()
            execution_time = (datetime.utcnow() - execution_start).total_seconds()

            # 💰 追踪成本
            self._track_bigquery_operation(
                "get_events_to_sync",
                cost_estimate,
                execution_time,
                segment_info
            )

            logger.info(f"Query job created: {query_job.job_id}, Cost: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, Time: {execution_time:.2f}s")
            return result

        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            # 即使失敗也追踪成本（dry run 的成本估算）
            self._track_bigquery_operation(
                "get_events_to_sync-TABLE_NOT_FOUND",
                cost_estimate,
                0,
                segment_info
            )
            return iter([])  # 返回空迭代器
        except Exception as e:
            logger.error(f"Failed to get events from BigQuery: {e}", exc_info=True)
            # 即使失敗也追踪成本
            self._track_bigquery_operation(
                "get_events_to_sync-ERROR",
                cost_estimate,
                0,
                segment_info
            )
            raise

    def transform_event_data(self, event: bigquery.Row) -> Dict[str, Any]:
        """Transforms a single event from the source format to the target format."""

        event_details = event.get("event")
        if isinstance(event_details, str):
            try:
                event_details = json.loads(event_details)
            except json.JSONDecodeError:
                logger.error(f"Failed to decode event JSON: {event_details}")
                event_details = {} # or handle error appropriately

        user_details = event.get("user")
        if isinstance(user_details, str):
            try:
                user_details = json.loads(user_details)
            except json.JSONDecodeError:
                logger.error(f"Failed to decode user JSON: {user_details}")
                user_details = {}

        # link 欄位統一為字串，None 轉為 ''
        link_value = event.get("link")
        if link_value is None:
            link_value = ''

        return {
            "permanent": event.get("permanent"),
            "ec_id": event.get("ec_id"),
            "partner_source": "legacy-tagtoo-event",
            "event_time": event.get("event_time").isoformat() if event.get("event_time") else None,
            "create_time": datetime.utcnow().isoformat(),
            "link": link_value,
            "event": event_details.get("name"),
            "value": event_details.get("value"),
            "currency": event_details.get("currency"),
            "order_id": (
                (event_details.get("custom_data") or {}).get("order_id")
                if isinstance(event_details.get("custom_data"), dict) or event_details.get("custom_data") is None
                else None
            ),
            "items": [
                {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "description": None,  # availability 欄位實際資料皆為空，不進行對應
                    "price": item.get("price"),
                    "quantity": item.get("quantity"),
                }
                for item in (event_details.get("items") or [])
                if isinstance(item, dict)
            ],
            "user": {
                "em": user_details.get("em"),
                "ph": user_details.get("ph"),
            },
            "partner_id": None,
            "page": None,
            "location": (
                {
                    "country_code": event.get("location", {}).get("country_code"),
                    "region_name": event.get("location", {}).get("region_name"),
                    "city_name": event.get("location", {}).get("city_name"),
                }
                if isinstance(event.get("location"), dict)
                else None
            ),
            "raw_json": None,
        }

    def _insert_to_bigquery(self, data: List[Dict[str, Any]]):
        """
        使用 MERGE 操作插入資料到 BigQuery，實現完全冪等性。
        MERGE 會自動處理重複記錄，避免資料重複。
        """
        if not data:
            return

        try:
            # 使用 MERGE 操作實現冪等插入
            self._merge_to_bigquery(data)
            logger.info(f"Successfully merged {len(data)} records using MERGE operation")
        except Exception as e:
            logger.error(f"MERGE operation failed, falling back to direct insert: {e}")
            # 後備方案：使用原始的 insert_rows_json
            table = self.bigquery_client.get_table(self.target_table)
            errors = self.bigquery_client.insert_rows_json(table, data)
            if errors:
                logger.error(f"BigQuery insert errors: {errors}")
                raise GoogleCloudError(f"BigQuery insert failed: {errors}")

    def _merge_to_bigquery(self, data: List[Dict[str, Any]]):
        """
        使用 MERGE 語句實現完全冪等性資料插入

        🔍 核心問題解決：
        原始 tagtoo_event 表存在大量重複記錄（每秒 80 筆相同事件），
        我們的同步過程必須確保不會創造額外重複，同時保持資料完整性。

        🚀 MERGE 操作流程：
        1. **臨時表建立**：建立與目標表相同結構的臨時表
        2. **資料載入**：將這批資料載入臨時表
        3. **內部去重**：在臨時表內先進行去重處理
        4. **智能合併**：使用複合主鍵比對，只插入真正新的記錄
        5. **自動清理**：確保臨時資源不會殘留

        🔑 主鍵策略說明：
        我們使用 8 個欄位的組合作為唯一性判斷：
        - permanent, ec_id, event_time, event: 基本事件識別
        - partner_source, link: 確保比對正確的資料來源
        - value, currency, order_id: 避免覆蓋有價值的業務資訊

        💡 為什麼不合併「重複」的 focus 事件？
        分析顯示同秒內的 focus 事件雖然看似重複，但在 link、referrer
        等欄位上有差異，代表可能是不同的真實用戶行為，強制合併會
        損失業務價值。

        ⚡ 冪等性保證：
        - 相同資料重複處理 → 不會產生重複記錄
        - 部分重複資料 → 只插入真正新的記錄
        - 網路重試 → 結果完全一致

        Args:
            data: 要同步的資料列表，已轉換為 BigQuery 格式

        Raises:
            Exception: 當 MERGE 操作失敗時，會記錄詳細錯誤並重新拋出
        """
        if not data:
            # 🚫 空資料直接返回，避免不必要的操作
            return

        # 🎲 生成唯一的臨時表名稱（避免並發衝突）
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 毫秒精度
        temp_table_id = f"temp_legacy_sync_{timestamp}"
        temp_table_ref = f"{PROJECT_ID}.{self.target_table.split('.')[1]}.{temp_table_id}"

        try:
            # 📋 步驟 1: 建立臨時表並載入資料
            # 使用與目標表相同的 Schema，確保資料類型一致
            self._create_temp_table_and_load_data(temp_table_ref, data)

            # 🔄 步驟 2: 執行 MERGE 操作
            # 智能比對並只插入真正新的記錄
            self._execute_merge_query(temp_table_ref)

            # ✅ 操作成功記錄
            logger.info(f"MERGE operation completed successfully for {len(data)} records")

        except Exception as e:
            # ❌ 失敗處理：記錄詳細錯誤資訊
            logger.error(f"MERGE operation failed: {e}", exc_info=True)
            raise
        finally:
            # 🧹 步驟 3: 清理臨時表（無論成功與否都要執行）
            try:
                self.bigquery_client.delete_table(temp_table_ref, not_found_ok=True)
                logger.debug(f"Cleaned up temporary table: {temp_table_ref}")
            except Exception as cleanup_error:
                # ⚠️ 清理失敗不影響主流程，但要記錄警告
                logger.warning(f"Failed to cleanup temp table {temp_table_ref}: {cleanup_error}")

    def _create_temp_table_and_load_data(self, temp_table_ref: str, data: List[Dict[str, Any]]):
        """
        建立臨時表並載入批次資料

        🎯 目的：
        1. 在 BigQuery 中建立臨時表來存放這批要處理的資料
        2. 透過臨時表進行 MERGE 操作，提升效能並減少鎖定時間
        3. 臨時表會在 1 小時後自動清理

        💡 臨時表優勢：
        - 減少主表鎖定時間：MERGE 操作只針對這批資料
        - 支援複雜的去重邏輯：可在臨時表內先進行內部去重
        - 更好的查詢最佳化：BigQuery 可針對小表進行最佳化
        - 自動清理：設定過期時間避免佔用儲存空間

        📊 資料載入策略：
        - 保持與目標表相同的 Schema 結構
        - 使用 JSON 格式批次載入提升效能
        - 錯誤處理確保資料完整性

        Args:
            temp_table_ref: 臨時表的完整參考路徑
            data: 要載入的資料列表（已轉換為字典格式）

        Raises:
            GoogleCloudError: 當臨時表建立或資料載入失敗時
        """
        # 📋 獲取目標表的 Schema 定義
        # 確保臨時表與目標表結構完全一致，避免型別不匹配問題
        target_table = self.bigquery_client.get_table(self.target_table)

        # 🏗️ 建立臨時表（使用相同的 Schema）
        temp_table = bigquery.Table(temp_table_ref, schema=target_table.schema)
        # ⏰ 設定過期時間（避免在測試環境中因 Mock 問題失敗）
        try:
            temp_table.expires = datetime.utcnow() + timedelta(hours=1)  # 1小時後自動過期
        except (ValueError, AttributeError):
            # 在測試環境中 Mock 可能不支援 expires 驗證，跳過設定
            pass
        temp_table = self.bigquery_client.create_table(temp_table)

        # 📤 批次載入資料到臨時表
        # 使用 insert_rows_json 進行高效 JSON 格式載入
        errors = self.bigquery_client.insert_rows_json(temp_table, data)
        if errors:
            # ❌ 載入失敗處理
            raise GoogleCloudError(f"Failed to load data into temp table: {errors}")

        # ✅ 載入成功記錄
        logger.debug(f"Created temp table {temp_table_ref} and loaded {len(data)} records")

    def _execute_merge_query(self, temp_table_ref: str):
        """
        執行 MERGE 查詢，實現完全冪等性的資料插入/更新

        🎛️ Feature Toggle: 支援啟用/停用去重複邏輯
        - 當 enable_deduplication=True：使用完整的 9 欄位複合主鍵去重
        - 當 enable_deduplication=False：直接插入所有資料，依賴 BigQuery 的自然去重

        🔍 問題背景：
        經過 BigQuery 分析發現，原始 tagtoo_event 表存在以下情況：
        1. 同一秒內相同用戶的相同事件可重複多達 80 次
        2. 但這些「重複」記錄在 link、referrer 等欄位上仍有差異
        3. 這代表它們可能是真實的不同用戶行為，不應隨意合併

        🎯 設計策略：
        1. **保持資料完整性**：不刻意合併可能有業務價值的不同記錄
        2. **防止同步重複**：確保我們的同步過程不會創造額外的重複
        3. **完整主鍵比對**：使用足夠的欄位組合來識別真正重複的記錄

        🔑 主鍵組合邏輯（當啟用去重時）：
        - permanent + ec_id + event_time + event：基本事件識別
        - partner_source + link：確保只比較相同來源和頁面的事件
        - value + currency + order_id：包含交易相關資訊避免覆蓋

        ⚡ 去重策略：
        1. 先對臨時表內部去重（處理批次內的重複）
        2. 再與目標表 MERGE（處理歷史重複）
        3. 使用 ROW_NUMBER() 保留時間最早的記錄
        """

        # 🚀 效能優化：移除複雜的 SQL 去重邏輯
        # 現在所有去重邏輯都在 Python 端處理，這裡只保留最高效的簡化版 MERGE
        logger.debug("使用簡化邏輯進行 MERGE 操作")
        merge_query = f"""
        MERGE `{self.target_table}` T
        USING (
          SELECT *
          FROM `{temp_table_ref}`
        ) S
        ON
          T.permanent = S.permanent
          AND T.ec_id = S.ec_id
          AND T.event_time = S.event_time
          AND T.event = S.event
          AND IFNULL(T.link, '') = IFNULL(S.link, '')
          AND T.value IS NOT DISTINCT FROM S.value
          AND T.currency IS NOT DISTINCT FROM S.currency
          AND T.order_id IS NOT DISTINCT FROM S.order_id
          AND T.partner_source = S.partner_source
        WHEN NOT MATCHED THEN
          INSERT (
            permanent, ec_id, partner_source, event_time, create_time,
            link, event, value, currency, order_id, items, user,
            partner_id, page, location, raw_json
          )
          VALUES (
            S.permanent, S.ec_id, S.partner_source, S.event_time, S.create_time,
            S.link, S.event, S.value, S.currency, S.order_id, S.items, S.user,
            S.partner_id, S.page, S.location, S.raw_json
          )
        """

        # 💰 成本估算
        job_config = bigquery.QueryJobConfig()  # MERGE 不需要參數
        cost_estimate = self.estimate_query_cost(merge_query, job_config)

        # 🚀 執行 MERGE 查詢
        execution_start = datetime.utcnow()
        job = self.bigquery_client.query(merge_query)
        job.result()  # 等待查詢完成
        execution_time = (datetime.utcnow() - execution_start).total_seconds()

        # 💰 追踪成本
        table_ref_parts = temp_table_ref.split('.')
        segment_info = table_ref_parts[-1] if len(table_ref_parts) > 0 else "unknown"
        self._track_bigquery_operation(
            "merge_operation",
            cost_estimate,
            execution_time,
            segment_info
        )

        # 📊 記錄執行結果
        affected_rows = job.num_dml_affected_rows or 0
        logger.info(f"MERGE operation completed: {affected_rows} rows inserted (0 updated), Cost: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, Time: {execution_time:.2f}s")

        return affected_rows

    def _process_batch(self, events: List[bigquery.Row], batch_id: str, dry_run: bool = False) -> Dict[str, Any]:
        """Transforms and inserts a single batch of events."""
        transformed_data = []
        error_count = 0
        for event in events:
            try:
                transformed_data.append(self.transform_event_data(event))
            except Exception:
                error_count += 1
                logger.error(f"Failed to transform event in batch {batch_id}", exc_info=True)

        # 如果是 dry_run 模式，只轉換資料不寫入
        if dry_run:
            logger.info(f"[Dry-run] Batch {batch_id}: 轉換了 {len(transformed_data)} 筆事件，錯誤 {error_count} 筆")
            return {"synced_count": len(transformed_data), "error_count": error_count}

        # 🎛️ Feature Toggle: 根據環境變數決定是否啟用優化版 MERGE 的記憶體去重
        use_optimized_merge = os.environ.get("USE_OPTIMIZED_MERGE", "false").lower() in ("true", "1", "yes")

        if use_optimized_merge:
            logger.info(f"Batch {batch_id}: 啟用優化版 MERGE，執行記憶體去重...")
            unique_keys = set()
            deduplicated_data = []
            for item in transformed_data:
                # link 欄位：只將 None 轉為 ''，其餘保留原值（與 transform_event_data 一致）
                link_val = item.get("link")
                link_key = '' if link_val is None else link_val

                # value 欄位：將 None 轉為 'NULL'，其餘轉為字串（與 BigQuery MERGE 行為一致）
                value_val = item.get("value")
                if value_val is None:
                    value_key = 'NULL'
                else:
                    value_key = str(value_val)

                key = (
                    item.get("permanent"),
                    item.get("ec_id"),
                    item.get("event_time"),
                    item.get("event"),
                    link_key,
                    value_key,
                    item.get("currency"),
                    item.get("order_id"),
                    item.get("partner_source")
                )
                if key not in unique_keys:
                    unique_keys.add(key)
                    deduplicated_data.append(item)

            logger.info(f"Batch {batch_id}: 記憶體去重完成，從 {len(transformed_data)} 筆資料中篩選出 {len(deduplicated_data)} 筆唯一資料。")
            data_to_insert = deduplicated_data
        else:
            data_to_insert = transformed_data

        if data_to_insert:
            try:
                # 無論是否去重，都使用簡化版的 MERGE 邏輯
                self._insert_to_bigquery(data_to_insert)
                logger.info(f"Batch {batch_id}: Successfully synced {len(data_to_insert)} events.")
            except GoogleCloudError:
                error_count += len(data_to_insert)

        return {"synced_count": len(data_to_insert), "error_count": error_count}

    def sync_time_segment(self, start_time: datetime, end_time: datetime, dry_run: bool = False) -> Dict[str, Any]:
        """Syncs all data within a single time segment using streaming processing."""
        mode_desc = "dry-run" if dry_run else "sync"
        logger.info(f"Worker: Starting {mode_desc} for segment: {start_time} -> {end_time}")

        # 📊 添加進度監控：定期報告處理狀態
        last_progress_report = datetime.utcnow()
        progress_interval = 300  # 每5分鐘報告一次進度

        # 使用迭代器進行串流處理
        events_iterator = self.get_events_to_sync(start_time, end_time)

        total_synced = 0
        total_errors = 0
        batch_count = 0
        current_batch = []

        try:
            for event in events_iterator:
                current_batch.append(event)

                # 🕒 進度監控：定期報告處理狀態
                current_time = datetime.utcnow()
                if (current_time - last_progress_report).total_seconds() > progress_interval:
                    logger.info(f"Progress: Processed {total_synced} events in {batch_count} batches. Current batch size: {len(current_batch)}")
                    last_progress_report = current_time

                # 當批次達到指定大小時進行處理
                if len(current_batch) >= BATCH_SIZE:
                    batch_count += 1
                    batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{batch_count}"
                    mode_desc = "[Dry-run]" if dry_run else ""
                    logger.info(f"{mode_desc} Processing batch {batch_id} with {len(current_batch)} events")

                    result = self._process_batch(current_batch, batch_id, dry_run=dry_run)
                    total_synced += result["synced_count"]
                    total_errors += result["error_count"]

                    # 清空批次，釋放記憶體
                    current_batch = []

            # 處理最後一個不滿的批次
            if current_batch:
                batch_count += 1
                batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{batch_count}"
                mode_desc = "[Dry-run]" if dry_run else ""
                logger.info(f"{mode_desc} Processing final batch {batch_id} with {len(current_batch)} events")

                result = self._process_batch(current_batch, batch_id, dry_run=dry_run)
                total_synced += result["synced_count"]
                total_errors += result["error_count"]

        except Exception as e:
            logger.error(f"Error during streaming processing: {e}", exc_info=True)
            status = "error"
            total_events = total_synced + total_errors
        else:
            total_events = total_synced + total_errors
            status = "success" if total_errors == 0 else "partial_success"

        if total_events == 0:
            logger.info(f"Segment {start_time} -> {end_time}: No new events to sync.")
            return {"status": "success", "total_events": 0, "synced_events": 0, "error_events": 0}

        logger.info(f"Segment {start_time} -> {end_time} sync complete. Status: {status}, Synced: {total_synced}, Errors: {total_errors}, Batches: {batch_count}")
        return {"status": status, "total_events": total_events, "synced_events": total_synced, "error_events": total_errors, "batches_processed": batch_count}

    def estimate_query_cost(self, query: str, job_config: bigquery.QueryJobConfig) -> Dict[str, Any]:
        """Estimates the cost of a BigQuery query using dry run."""
        try:
            # Create a new dry run job config
            dry_run_config = bigquery.QueryJobConfig()

            # Copy relevant parameters from original config
            if job_config.query_parameters:
                dry_run_config.query_parameters = job_config.query_parameters
            if job_config.use_legacy_sql is not None:
                dry_run_config.use_legacy_sql = job_config.use_legacy_sql

            # Set dry run specific options
            dry_run_config.dry_run = True
            dry_run_config.use_query_cache = False

            # Execute dry run
            job = self.bigquery_client.query(query, job_config=dry_run_config)

            # Calculate cost (approximately $5 per TB in most regions)
            bytes_processed = job.total_bytes_processed or 0
            tb_processed = bytes_processed / (1024**4) if bytes_processed else 0
            estimated_cost_usd = tb_processed * 5.0  # $5 per TB

            return {
                "bytes_processed": bytes_processed,
                "tb_processed": round(tb_processed, 6),
                "estimated_cost_usd": round(estimated_cost_usd, 4),
                "query_slots": job.slot_millis // 1000 if job.slot_millis else 0
            }
        except Exception as e:
            logger.warning(f"Failed to estimate query cost: {e}")
            return {
                "bytes_processed": 0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "query_slots": 0,
                "error": str(e)
            }

    def get_source_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the source table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        # 建構事件類型過濾條件（與 get_events_to_sync 保持一致）
        event_filter_condition = ""
        if EXCLUDED_EVENT_TYPES:
            excluded_list = [f"'{event_type}'" for event_type in EXCLUDED_EVENT_TYPES]
            excluded_str = ", ".join(excluded_list)
            event_filter_condition = f" AND event.name NOT IN ({excluded_str})"

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time{event_filter_condition}
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get source table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_target_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the target table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.target_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
            AND partner_source = 'legacy-tagtoo-event'
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get target table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_latest_data_timestamp(self) -> Optional[datetime]:
        """Retrieves the latest event_time from the target table."""
        query = f"""
            SELECT MAX(event_time) as latest_time
            FROM `{self.target_table}`
            WHERE partner_source = 'legacy-tagtoo-event'
        """
        try:
            result = list(self.bigquery_client.query(query).result())
            if result and result[0].latest_time:
                return result[0].latest_time
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
        except Exception as e:
            logger.error(f"Failed to get latest data timestamp: {e}", exc_info=True)
        return None

    def get_sync_status_for_date(self, target_date: datetime = None) -> Dict[str, Any]:
        """Retrieves comprehensive sync status for a specific date with BigQuery cost analysis."""
        if target_date is None:
            target_date = datetime.utcnow()

        # Get basic sync metadata
        last_sync_time = self.get_last_sync_time()

        # Get data counts for the target date (with cost estimation)
        source_result = self.get_source_table_count_for_date(target_date)
        target_result = self.get_target_table_count_for_date(target_date)

        # Extract counts from the new format
        source_count = source_result.get("count", 0)
        target_count = target_result.get("count", 0)

        # Get latest data timestamp
        latest_timestamp = self.get_latest_data_timestamp()

        # Calculate sync coverage
        sync_coverage = (target_count / source_count * 100) if source_count > 0 else 0

        # Calculate total cost for this status check
        total_cost = (source_result.get("query_stats", {}).get("estimated_cost_usd", 0) +
                     target_result.get("query_stats", {}).get("estimated_cost_usd", 0))
        total_tb_processed = (source_result.get("query_stats", {}).get("tb_processed", 0) +
                             target_result.get("query_stats", {}).get("tb_processed", 0))
        total_execution_time = (source_result.get("query_stats", {}).get("execution_time_seconds", 0) +
                               target_result.get("query_stats", {}).get("execution_time_seconds", 0))

        # Determine status
        status = "healthy"
        warnings = []
        cost_warnings = []

        # Data coverage warnings
        if source_count == 0:
            warnings.append("No source data found for the specified date")
        elif target_count == 0:
            status = "warning"
            warnings.append("No target data found for the specified date")
        elif sync_coverage < 95:
            status = "warning"
            warnings.append(f"Sync coverage is only {sync_coverage:.1f}%")

        # Time-based warnings
        if last_sync_time:
            hours_since_sync = (datetime.utcnow() - last_sync_time).total_seconds() / 3600
            if hours_since_sync > 6:
                status = "warning"
                warnings.append(f"Last sync was {hours_since_sync:.1f} hours ago")
        else:
            status = "warning"
            warnings.append("No sync history found")

        # Cost-based warnings
        if total_cost > 0.5:  # > $0.50
            cost_warnings.append(f"High query cost: ${total_cost:.4f} USD")
            status = "warning"
        elif total_cost > 0.1:  # > $0.10
            cost_warnings.append(f"Moderate query cost: ${total_cost:.4f} USD")

        if total_execution_time > 30:  # > 30 seconds
            cost_warnings.append(f"Slow queries detected: {total_execution_time:.2f}s total execution time")

        # Optimization suggestions
        optimization_suggestions = []
        if total_tb_processed > 0.1:  # > 100 GB
            optimization_suggestions.append("Consider adding date partitioning filters to reduce data scan")
        if total_execution_time > 10:
            optimization_suggestions.append("Consider adding indexes or optimizing WHERE clauses")

        return {
            "date": target_date.strftime("%Y-%m-%d"),
            "status": status,
            "last_sync_time": last_sync_time.isoformat() if last_sync_time else None,
            "source_events_count": source_count,
            "target_events_count": target_count,
            "sync_coverage_percent": round(sync_coverage, 2),
            "latest_data_timestamp": latest_timestamp.isoformat() if latest_timestamp else None,
            "warnings": warnings + cost_warnings,
            "table_info": {
                "source_table": self.source_table,
                "target_table": self.target_table
            },
            "filtering_config": {
                "excluded_event_types": EXCLUDED_EVENT_TYPES,
                "filtering_enabled": len(EXCLUDED_EVENT_TYPES) > 0,
                "description": f"已過濾 {len(EXCLUDED_EVENT_TYPES)} 種事件類型" if EXCLUDED_EVENT_TYPES else "未啟用事件過濾"
            },
            "bigquery_analysis": {
                "total_cost_usd": round(total_cost, 4),
                "total_tb_processed": round(total_tb_processed, 6),
                "total_execution_time_seconds": round(total_execution_time, 2),
                "source_query_stats": source_result.get("query_stats", {}),
                "target_query_stats": target_result.get("query_stats", {}),
                "optimization_suggestions": optimization_suggestions
            },
            "streaming_buffer_info": self.get_streaming_buffer_status()
        }

    def normalize_to_hour_boundary(self, dt: datetime) -> datetime:
        """標準化時間到整點邊界，確保冪等性"""
        return dt.replace(minute=0, second=0, microsecond=0)

    def generate_time_segments(self, start_date: datetime, end_date: datetime) -> List[tuple[datetime, datetime]]:
        """Generates time segments for the given date range (以分鐘為單位)."""
        # 標準化時間到整點邊界，確保冪等性
        start_date = self.normalize_to_hour_boundary(start_date)
        end_date = self.normalize_to_hour_boundary(end_date)

        segments = []
        current_time = start_date
        while current_time < end_date:
            segment_end = current_time + timedelta(minutes=MINUTES_PER_SEGMENT)
            if segment_end > end_date:
                segment_end = end_date
            segments.append((current_time, segment_end))
            current_time = segment_end
        return segments

    def coordinate_sync(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None, worker_url: Optional[str] = None) -> Dict[str, Any]:
        """Coordinator logic: generates segments and creates Cloud Tasks."""
        logger.info("Coordinator: Starting sync coordination.")

        # Set worker URL if provided or build it dynamically
        if worker_url:
            self.worker_url = worker_url
        elif not self.worker_url:
            # Try to build it from Flask request context (preferred method)
            try:
                from flask import request
                # 修正：確保生產環境總是使用 HTTPS
                base_url = request.url_root.rstrip('/')
                if ENVIRONMENT == 'prod' and base_url.startswith('http://'):
                    # Cloud Run 內部可能使用 HTTP，但外部訪問需要 HTTPS
                    base_url = base_url.replace('http://', 'https://')
                self.worker_url = f"{base_url}{WORKER_URL_PATH}"
                logger.info(f"Built worker URL from request context: {self.worker_url}")
            except RuntimeError:
                # 不在 request context - 提供測試環境的 fallback
                # 這種情況通常發生在測試環境或直接調用時
                if ENVIRONMENT in ['dev', 'test', 'ci']:
                    # 測試環境使用 localhost fallback
                    self.worker_url = f"http://localhost:8080{WORKER_URL_PATH}"
                    logger.info(f"Using test fallback worker URL: {self.worker_url}")
                else:
                    # 生產環境建立基於環境變數的 URL
                    self.worker_url = f"https://legacy-event-sync-{ENVIRONMENT}-{GCP_LOCATION}.a.run.app{WORKER_URL_PATH}"
                    logger.info(f"Built production worker URL: {self.worker_url}")

        if not end_date:
            # 為了處理 BigQuery streaming buffer 延遲，使用可配置的時間偏移
            # 基於真實監控，Streaming Buffer 延遲通常 10-15 分鐘，預設 1 小時緩衝
            streaming_buffer_offset_hours = int(os.environ.get("STREAMING_BUFFER_OFFSET_HOURS", 1))
            # 標準化到整點邊界確保冪等性
            end_date = self.normalize_to_hour_boundary(datetime.utcnow() - timedelta(hours=streaming_buffer_offset_hours))
            logger.info(f"Using default end_date with {streaming_buffer_offset_hours}-hour buffer: {end_date}")
        else:
            # 標準化用戶提供的 end_date
            end_date = self.normalize_to_hour_boundary(end_date)
        if not start_date:
            # 🎯 重疊同步機制：確保不遺漏 Streaming Buffer 延遲的資料
            # 使用 scheduler 間隔加上額外的重疊緩衝（預設 15 分鐘）
            overlap_buffer_minutes = int(os.environ.get("SYNC_OVERLAP_MINUTES", 15))
            sync_window_minutes = SCHEDULER_INTERVAL_MINUTES + overlap_buffer_minutes
            default_start = end_date - timedelta(minutes=sync_window_minutes)

            logger.info(f"使用重疊同步機制：窗口大小 {sync_window_minutes} 分鐘（{SCHEDULER_INTERVAL_MINUTES} + {overlap_buffer_minutes} 重疊）")

            # 📋 記錄 last_sync_time 僅用於監控，不影響處理邏輯
            last_sync_time = self.get_last_sync_time()
            if last_sync_time:
                logger.info(f"監控信息：上次成功同步時間 {last_sync_time}, 當前處理時段 {default_start} -> {end_date}")

                # ⚠️ 檢測是否有時間空隙，用於監控告警
                time_gap_minutes = (default_start - last_sync_time).total_seconds() / 60
                if time_gap_minutes > MINUTES_PER_SEGMENT + 1:  # 允許1分鐘容差
                    logger.warning(f"檢測到時間空隙：上次同步 {last_sync_time} 到當前處理 {default_start}，間隔 {time_gap_minutes:.1f} 分鐘")
                    logger.warning("建議檢查是否需要手動補齊遺漏的時段")
            else:
                logger.info(f"首次運行或無同步記錄，處理固定時段：{default_start} -> {end_date}")
            # 標準化到整點邊界確保冪等性
            start_date = self.normalize_to_hour_boundary(default_start)
        else:
            # 標準化用戶提供的 start_date
            start_date = self.normalize_to_hour_boundary(start_date)

        if start_date >= end_date:
            logger.info("No new time range to sync. Last sync was at %s.", start_date)
            return {"status": "no_new_data", "message": "No new time range to sync."}

        segments = self.generate_time_segments(start_date, end_date)
        if not segments:
            logger.info("No segments generated for the time range.")
            return {"status": "no_segments", "message": "No segments to process."}

        logger.info(f"Generated {len(segments)} segments to process from {start_date} to {end_date}.")

        # 加入安全檢查：避免創建過多任務
        max_segments_per_request = 50  # 限制單次最多50個segments
        if len(segments) > max_segments_per_request:
            logger.error(f"Too many segments ({len(segments)}). Maximum allowed: {max_segments_per_request}")
            return {
                "status": "error",
                "message": f"Time range too large. Generated {len(segments)} segments, but maximum allowed is {max_segments_per_request}. Please use smaller time ranges."
            }

        parent = self.tasks_client.queue_path(PROJECT_ID, GCP_LOCATION, self.task_queue_name)
        tasks_created = 0
        for start, end in segments:
            payload = {"start_time": start.isoformat(), "end_time": end.isoformat()}

            # 設定任務超時時間為 30 分鐘 (1800秒)，Cloud Tasks 上限
            from google.protobuf import duration_pb2
            timeout_duration = duration_pb2.Duration()
            timeout_duration.seconds = 1800  # 30 分鐘 (Cloud Tasks 上限)

            task = {
                "http_request": {
                    "http_method": tasks_v2.HttpMethod.POST,
                    "url": self.worker_url,
                    "headers": {"Content-type": "application/json"},
                    "body": json.dumps(payload).encode(),
                    # 新增 OIDC 認證設定，讓 Cloud Tasks 能夠呼叫受保護的 Cloud Run 服務
                    "oidc_token": {
                        "service_account_email": os.environ.get("GOOGLE_SERVICE_ACCOUNT_EMAIL", f"integrated-event-{ENVIRONMENT}@{PROJECT_ID}.iam.gserviceaccount.com"),
                        "audience": self.worker_url,
                    }
                },
                # 設定任務執行的超時時間，解決 600s 預設限制
                "dispatch_deadline": timeout_duration
            }
            try:
                logger.info(f"Creating task for segment {start} -> {end} with URL: {self.worker_url} (timeout: 1800s)")
                self.tasks_client.create_task(request={"parent": parent, "task": task})
                tasks_created += 1
            except GoogleCloudError as e:
                logger.error(f"Failed to create task for segment {start} -> {end}: {e}", exc_info=True)

        logger.info(f"Coordinator: Created {tasks_created} of {len(segments)} tasks.")

        if tasks_created == len(segments):
            # 🎯 更新 last_sync_time 僅用於監控，記錄最後成功處理的時段結束時間
            # 注意：這不影響下次處理的 start_date，只用於監控和告警
            self.set_last_sync_time(end_date)
            logger.info(f"Successfully updated monitoring timestamp to {end_date.isoformat()} (固定時段處理模式)")
        else:
            logger.warning(f"部分任務建立失敗 ({tasks_created}/{len(segments)})，不更新監控時間戳記")

        return {"status": "success", "total_segments": len(segments), "tasks_created": tasks_created}

# --- Flask App Routes ---
processor = LegacyEventSyncProcessor(
    source_table=SOURCE_TABLE,
    target_table=TARGET_TABLE,
    task_queue_name=TASK_QUEUE_NAME,
    worker_url=WORKER_URL,
)

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify(
        {
            "status": "healthy",
            "service": "legacy-event-sync",
            "version": SERVICE_VERSION,
            "commit": SERVICE_COMMIT,
            "timestamp": datetime.utcnow().isoformat(),
        }
    )

@app.route("/start-sync", methods=["POST"])
def start_sync_endpoint():
    """Coordinator endpoint, triggered by Cloud Scheduler."""
    try:
        data = request.get_json() or {}
        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        start_date = datetime.fromisoformat(start_date_str) if start_date_str else None
        end_date = datetime.fromisoformat(end_date_str) if end_date_str else None

        # Build worker URL from current request
        # 確保生產環境總是使用 HTTPS
        base_url = request.url_root.rstrip('/')
        if ENVIRONMENT == 'prod' or base_url.startswith('https://'):
            # 生產環境或已經是 HTTPS，確保使用 HTTPS
            if base_url.startswith('http://'):
                base_url = base_url.replace('http://', 'https://')
            worker_url = f"{base_url}{WORKER_URL_PATH}"
        else:
            # 開發/測試環境，保持原來的協議
            worker_url = f"{base_url}{WORKER_URL_PATH}"

        result = processor.coordinate_sync(start_date, end_date, worker_url)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Coordinator error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/process-segment", methods=["POST"])
def process_segment_endpoint():
    """Worker endpoint, triggered by Cloud Tasks."""
    try:
        # 💰 初始化成本追踪器（為這個 segment 建立新的追踪會話）
        processor._ensure_cost_tracker_initialized()
        if processor.cost_tracker:
            processor.cost_tracker.reset()  # 重置以追踪這個 segment 的成本

        data = request.get_json(silent=True)
        if not data or "start_time" not in data or "end_time" not in data:
            logger.error("Invalid task payload received. Payload: %s", data)
            return "Invalid payload", 400

        start_time = datetime.fromisoformat(data["start_time"])
        end_time = datetime.fromisoformat(data["end_time"])

        result = processor.sync_time_segment(start_time, end_time)

        # 💰 加入成本資訊到回應中
        if processor.cost_tracker and processor._enable_cost_tracking:
            cost_summary = processor.cost_tracker.get_summary()
            result["bigquery_costs"] = cost_summary

            # 記錄成本資訊到日誌
            logger.info(f"Segment {start_time}-{end_time} BigQuery costs: "
                       f"${cost_summary['cost_summary']['total_cost_usd']:.4f} USD, "
                       f"{cost_summary['session_info']['total_operations']} operations, "
                       f"Efficiency score: {cost_summary['cost_summary']['cost_efficiency_score']}")

        # Cloud Tasks expects a 2xx response to consider the task successful
        if result.get("status") in ["success", "partial_success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500
    except (ValueError, TypeError) as e:
        logger.error(f"Worker error processing segment due to value/type error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": f"Invalid date format or payload structure: {e}"}), 400
    except Exception as e:
        logger.error(f"Worker error processing segment: {e}", exc_info=True)
        # Return a non-2xx status to signal task failure for retry
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/sync-status", methods=["GET"])
def sync_status_endpoint():
    """Sync status endpoint, provides comprehensive sync status information."""
    try:
        # Get optional date parameter
        date_param = request.args.get("date")
        target_date = None

        if date_param:
            try:
                target_date = datetime.strptime(date_param, "%Y-%m-%d")
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "Invalid date format. Use YYYY-MM-DD format."
                }), 400

        result = processor.get_sync_status_for_date(target_date)
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Sync status endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route("/backfill-sync", methods=["POST"])
def backfill_sync_endpoint():
    """回補遺漏時段的同步任務，支援指定時間範圍"""
    try:
        data = request.get_json() or {}

        # 必須指定開始和結束時間
        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        if not start_date_str or not end_date_str:
            return jsonify({
                "status": "error",
                "message": "請指定 start_date 和 end_date (ISO 格式)"
            }), 400

        try:
            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)
        except ValueError:
            return jsonify({
                "status": "error",
                "message": "日期格式錯誤，請使用 ISO 格式 (例如: 2025-07-17T13:00:00)"
            }), 400

        # 安全檢查：限制回補範圍，避免建立過多任務
        time_diff_hours = (end_date - start_date).total_seconds() / 3600
        max_backfill_hours = 72  # 最多回補3天

        if time_diff_hours > max_backfill_hours:
            return jsonify({
                "status": "error",
                "message": f"回補範圍過大 ({time_diff_hours:.1f} 小時)，最多允許 {max_backfill_hours} 小時"
            }), 400

        if start_date >= end_date:
            return jsonify({
                "status": "error",
                "message": "開始時間必須早於結束時間"
            }), 400

        # Build worker URL from current request
        base_url = request.url_root.rstrip('/')
        if ENVIRONMENT == 'prod' or base_url.startswith('https://'):
            if base_url.startswith('http://'):
                base_url = base_url.replace('http://', 'https://')
            worker_url = f"{base_url}{WORKER_URL_PATH}"
        else:
            worker_url = f"{base_url}{WORKER_URL_PATH}"

        # 執行回補同步（強制使用指定的時間範圍）
        logger.info(f"開始回補同步：{start_date} -> {end_date}")
        result = processor.coordinate_sync(start_date, end_date, worker_url)

        # 為回補任務添加特殊標記
        result["operation_type"] = "backfill"
        result["backfill_range"] = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "hours_covered": round(time_diff_hours, 1)
        }

        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Backfill sync endpoint error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/bigquery-costs", methods=["GET"])
def bigquery_costs_endpoint():
    """
    BigQuery 成本分析端點

    提供詳細的 BigQuery 使用成本分析，包括即時成本估算、
    歷史成本趨勢和最佳化建議。

    Query Parameters:
    - date: 指定日期 (YYYY-MM-DD 格式，可選)
    - analysis_type: 分析類型 ('quick' 或 'detailed'，預設 'quick')
    """
    try:
        # 📋 解析查詢參數
        date_param = request.args.get("date")
        analysis_type = request.args.get("analysis_type", "quick")

        target_date = datetime.utcnow() if not date_param else datetime.strptime(date_param, "%Y-%m-%d")

        # 💰 初始化成本追踪器進行分析
        processor._ensure_cost_tracker_initialized()
        if processor.cost_tracker:
            processor.cost_tracker.reset()  # 重置以進行新的成本分析

        response_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "analysis_date": target_date.strftime("%Y-%m-%d"),
            "analysis_type": analysis_type,
            "cost_tracking_enabled": processor._enable_cost_tracking
        }

        if not processor._enable_cost_tracking:
            response_data["message"] = "成本追踪已停用，請設定環境變數 ENABLE_COST_TRACKING=true 啟用"
            return jsonify(response_data), 200

        if analysis_type == "detailed":
            # 🔍 詳細分析：執行實際的成本分析查詢
            logger.info(f"執行詳細成本分析：{target_date.strftime('%Y-%m-%d')}")

            # 執行同步狀態檢查（會觸發 BigQuery 查詢並追踪成本）
            sync_status = processor.get_sync_status_for_date(target_date)

            # 取得成本追踪摘要
            if processor.cost_tracker:
                cost_summary = processor.cost_tracker.get_summary()
                response_data["detailed_analysis"] = {
                    "sync_status": sync_status,
                    "cost_breakdown": cost_summary,
                    "real_time_costs": cost_summary["cost_summary"]
                }

        else:
            # ⚡ 快速分析：僅提供基本的成本評估
            logger.info(f"執行快速成本分析：{target_date.strftime('%Y-%m-%d')}")

            # 執行同步狀態檢查（已包含成本分析）
            sync_status = processor.get_sync_status_for_date(target_date)

            response_data["quick_analysis"] = {
                "bigquery_summary": sync_status.get("bigquery_analysis", {}),
                "sync_overview": {
                    "source_events": sync_status.get("source_events_count", 0),
                    "target_events": sync_status.get("target_events_count", 0),
                    "sync_coverage": sync_status.get("sync_coverage_percent", 0),
                    "status": sync_status.get("status", "unknown")
                }
            }

        # 📊 加入通用的最佳化建議
        response_data["optimization_recommendations"] = [
            "🎯 使用分區表 (PARTITION BY DATE) 減少資料掃描範圍",
            "🔍 避免 SELECT * 查詢，只選取必要欄位",
            "⏰ 在測試環境使用 LIMIT 限制查詢範圍",
            "📈 定期監控查詢成本，設定預算警告",
            "🏗️ 考慮使用 CLUSTERED 表加速常用查詢",
            "💾 利用 BigQuery 快取機制，避免重複查詢"
        ]

        # 🎛️ 加入成本控制設定資訊
        response_data["cost_control_settings"] = {
            "cost_thresholds": {
                "warning": "$0.10 USD",
                "high": "$0.50 USD",
                "critical": "$2.00 USD"
            },
            "current_pricing": "$5.00 per TB (on-demand pricing)",
            "suggestions": [
                "考慮使用 BigQuery 預留容量降低成本",
                "設定每日查詢預算避免意外高額費用",
                "使用 Cloud Monitoring 建立成本警報"
            ]
        }

        return jsonify(response_data), 200

    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": f"無效的日期格式，請使用 YYYY-MM-DD: {e}"
        }), 400
    except Exception as e:
        logger.error(f"BigQuery costs endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


@app.route("/streaming-buffer-status", methods=["GET"])
def streaming_buffer_status_endpoint():
    """
    BigQuery Streaming Buffer 狀況檢查端點

    提供即時的 Streaming Buffer 狀況分析，協助優化同步時機。

    Query Parameters:
    - include_recommendations: 是否包含同步偏移建議 ('true' 或 'false'，預設 'true')

    Response:
    - streaming_buffer: 當前 Streaming Buffer 狀況
    - current_settings: 目前同步配置
    - recommendations: 優化建議 (如果啟用)
    """
    logger.info("Streaming Buffer 狀況檢查端點被調用")

    try:
        include_recommendations = request.args.get('include_recommendations', 'true').lower() == 'true'

        # 獲取 Streaming Buffer 狀況
        buffer_status = processor.get_streaming_buffer_status()

        response_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "streaming_buffer": buffer_status,
            "current_settings": {
                "sync_offset_minutes": 120,  # 目前設定的 2 小時
                "sync_schedule": "每 60 分鐘觸發一次",
                "batch_size": BATCH_SIZE,
                "minutes_per_segment": MINUTES_PER_SEGMENT
            }
        }

        if include_recommendations and buffer_status.get("has_streaming_buffer"):
            delay_minutes = buffer_status.get("delay_minutes", 0)
            safe_offset = buffer_status.get("safe_offset_minutes", 120)

            # 智能建議
            recommendations = []

            if delay_minutes <= 30:
                recommendations.append({
                    "type": "optimization",
                    "priority": "medium",
                    "message": f"當前延遲只有 {delay_minutes:.1f} 分鐘，可考慮縮短同步偏移時間",
                    "suggested_action": f"可嘗試將偏移時間從 120 分鐘縮短至 {max(60, safe_offset)} 分鐘"
                })
            elif delay_minutes > 120:
                recommendations.append({
                    "type": "warning",
                    "priority": "high",
                    "message": f"當前延遲達 {delay_minutes:.1f} 分鐘，建議增加同步偏移時間",
                    "suggested_action": f"建議將偏移時間增加至 {safe_offset} 分鐘"
                })
            else:
                recommendations.append({
                    "type": "status",
                    "priority": "low",
                    "message": "當前偏移時間配置合理",
                    "suggested_action": "建議定期監控以確保配置最佳化"
                })

            # 歷史趨勢建議
            if delay_minutes < 60:
                recommendations.append({
                    "type": "trend_analysis",
                    "priority": "low",
                    "message": "建議分析歷史趨勢以確認是否可長期縮短偏移時間",
                    "suggested_action": "執行: python scripts/check_streaming_buffer.py --analyze-trends --recommend-offset"
                })

            response_data["recommendations"] = recommendations

        # 加入快速診斷
        if buffer_status.get("has_streaming_buffer"):
            delay_minutes = buffer_status.get("delay_minutes", 0)
            if delay_minutes <= 15:
                status_level = "excellent"
                status_message = "延遲極低，適合即時處理"
            elif delay_minutes <= 60:
                status_level = "good"
                status_message = "延遲在合理範圍內"
            elif delay_minutes <= 120:
                status_level = "acceptable"
                status_message = "延遲稍高，建議監控"
            else:
                status_level = "concerning"
                status_message = "延遲較高，建議增加偏移時間"

            response_data["quick_diagnosis"] = {
                "status_level": status_level,
                "status_message": status_message,
                "delay_assessment": f"當前延遲 {delay_minutes:.1f} 分鐘",
                "sync_safety": buffer_status.get("is_safe", True)
            }

        return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"Streaming Buffer status endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
