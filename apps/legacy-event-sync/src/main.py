"""
Legacy Event Sync Service - Cloud Run Worker/Coordinator

This service, running on Cloud Run, synchronizes data from the legacy
`tagtoo_event` table to the unified `integrated_event` table.

It operates in two modes:
- Coordinator (/start-sync): Triggered by Cloud Scheduler, it determines the
  time range to sync and creates tasks for each segment.
- Worker (/process-segment): Triggered by Cloud Tasks, it processes a single
  time segment of data.
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from flask import Flask, jsonify, request
from google.cloud import bigquery, firestore, tasks_v2
from google.cloud.exceptions import GoogleCloudError, NotFound

# --- Logging Configuration ---
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# --- Environment Configuration ---
PROJECT_ID = os.environ.get("PROJECT_ID", "tagtoo-tracking")
ENVIRONMENT = os.environ.get("ENVIRONMENT", "dev")
GCP_LOCATION = os.environ.get("GCP_LOCATION", "asia-east1")

SOURCE_TABLE = os.environ.get("SOURCE_TABLE", "tagtoo-tracking.event_prod.tagtoo_event")
TARGET_TABLE_DATASET = os.environ.get("BIGQUERY_DATASET", "event_test")
TARGET_TABLE = os.environ.get(
    "TARGET_TABLE", f"{PROJECT_ID}.{TARGET_TABLE_DATASET}.integrated_event"
)

TASK_QUEUE_NAME = os.environ.get("TASK_QUEUE_NAME")
WORKER_URL = os.environ.get("WORKER_URL")
WORKER_URL_PATH = os.environ.get("WORKER_URL_PATH", "/process-segment")

HOURS_PER_SEGMENT = int(os.environ.get("HOURS_PER_SEGMENT", "4"))
BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "200000"))


def create_bigquery_client():
    """Creates and validates a BigQuery client."""
    try:
        logger.info(f"Initializing BigQuery client for project: {PROJECT_ID}")
        client = bigquery.Client(project=PROJECT_ID)
        list(client.list_datasets(max_results=1))  # Test connection
        logger.info("✅ BigQuery client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize BigQuery client: {e}", exc_info=True)
        raise

def create_firestore_client():
    """Creates a Firestore client."""
    try:
        logger.info(f"Initializing Firestore client for project: {PROJECT_ID}")
        client = firestore.Client(project=PROJECT_ID)
        logger.info("✅ Firestore client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Firestore client: {e}", exc_info=True)
        raise

def create_tasks_client():
    """Creates a Cloud Tasks client."""
    try:
        logger.info("Initializing Cloud Tasks client.")
        client = tasks_v2.CloudTasksClient()
        logger.info("✅ Cloud Tasks client initialized successfully.")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Cloud Tasks client: {e}", exc_info=True)
        raise


class LegacyEventSyncProcessor:
    """Handles the logic for the event synchronization."""

    def __init__(self, source_table: str, target_table: str, task_queue_name: Optional[str] = None, worker_url: Optional[str] = None):
        """Initializes the processor with configuration and lazy-loaded clients."""
        self.source_table = source_table
        self.target_table = target_table
        self.task_queue_name = task_queue_name or TASK_QUEUE_NAME
        self.worker_url = worker_url or WORKER_URL or self._build_worker_url()

        self._bigquery_client = None
        self._tasks_client = None
        self._firestore_client = None
        self.last_sync_doc_ref = (
            f"legacy_sync_metadata/{self.source_table}-{self.target_table}"
        )

    def _build_worker_url(self) -> str:
        """Build worker URL dynamically when running in Cloud Run."""
        # This will be set dynamically in the coordinate_sync method
        # when we have access to the Flask request context
        return None

    @property
    def bigquery_client(self):
        if self._bigquery_client is None:
            self._bigquery_client = create_bigquery_client()
        return self._bigquery_client

    @property
    def tasks_client(self):
        if self._tasks_client is None:
            self._tasks_client = create_tasks_client()
        return self._tasks_client

    @property
    def firestore_client(self):
        if self._firestore_client is None:
            self._firestore_client = create_firestore_client()
        return self._firestore_client

    def get_last_sync_time(self) -> Optional[datetime]:
        """Retrieves the last successful sync time from Firestore."""
        try:
            doc = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}").get()
            if doc.exists:
                return datetime.fromisoformat(doc.to_dict()["last_sync_utc"])
        except Exception as e:
            logger.error(f"Failed to get last sync time: {e}")
        return None

    def set_last_sync_time(self, sync_time: datetime):
        """Stores the last successful sync time in Firestore."""
        try:
            doc_ref = self.firestore_client.collection("legacy_sync_metadata").document(f"{self.source_table}-{self.target_table}")
            doc_ref.set({"last_sync_utc": sync_time.isoformat()})
        except Exception as e:
            logger.error(f"Failed to set last sync time: {e}")

    def get_events_to_sync(self, start_time: datetime, end_time: datetime) -> List[bigquery.Row]:
        """Fetches events from the source BigQuery table within a time range."""
        query = f"""
            SELECT *
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
            ORDER BY event_time
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
            ]
        )
        try:
            logger.info(f"Querying events from {start_time} to {end_time}")
            return list(self.bigquery_client.query(query, job_config=job_config).result())
        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            return []
        except Exception as e:
            logger.error(f"Failed to get events from BigQuery: {e}", exc_info=True)
            raise

    def transform_event_data(self, event: bigquery.Row) -> Dict[str, Any]:
        """Transforms a single event from the source format to the target format."""

        event_details = event.get("event")
        if isinstance(event_details, str):
            try:
                event_details = json.loads(event_details)
            except json.JSONDecodeError:
                logger.error(f"Failed to decode event JSON: {event_details}")
                event_details = {} # or handle error appropriately

        user_details = event.get("user")
        if isinstance(user_details, str):
            try:
                user_details = json.loads(user_details)
            except json.JSONDecodeError:
                logger.error(f"Failed to decode user JSON: {user_details}")
                user_details = {}

        return {
            "permanent": event.get("permanent"),
            "ec_id": event.get("ec_id"),
            "partner_source": "legacy-tagtoo-event",
            "event_time": event.get("event_time").isoformat() if event.get("event_time") else None,
            "create_time": datetime.utcnow().isoformat(),
            "link": event.get("link"),
            "event": event_details.get("name"),
            "value": event_details.get("value"),
            "currency": event_details.get("currency"),
            "order_id": (
                (event_details.get("custom_data") or {}).get("order_id")
                if isinstance(event_details.get("custom_data"), dict) or event_details.get("custom_data") is None
                else None
            ),
            "items": [
                {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "price": item.get("price"),
                    "quantity": item.get("quantity"),
                }
                for item in (event_details.get("items") or [])
                if isinstance(item, dict)
            ],
            "user": {
                "em": user_details.get("em"),
                "ph": user_details.get("ph"),
            },
            "partner_id": None,
            "page": None,
            "location": (
                {
                    "country_code": event.get("location", {}).get("country_code"),
                    "region_name": event.get("location", {}).get("region_name"),
                    "city_name": event.get("location", {}).get("city_name"),
                }
                if isinstance(event.get("location"), dict)
                else None
            ),
            "raw_json": None,
        }

    def _insert_to_bigquery(self, data: List[Dict[str, Any]]):
        """Inserts a batch of data into the target BigQuery table."""
        if not data:
            return

        # 修正：使用 Table 物件而不是字符串
        table = self.bigquery_client.get_table(self.target_table)
        errors = self.bigquery_client.insert_rows_json(table, data)

        if errors:
            logger.error(f"BigQuery insert errors: {errors}")
            raise GoogleCloudError(f"BigQuery insert failed: {errors}")

    def _process_batch(self, events: List[bigquery.Row], batch_id: str) -> Dict[str, Any]:
        """Transforms and inserts a single batch of events."""
        transformed_data = []
        error_count = 0
        for event in events:
            try:
                transformed_data.append(self.transform_event_data(event))
            except Exception:
                error_count += 1
                logger.error(f"Failed to transform event in batch {batch_id}", exc_info=True)

        if transformed_data:
            try:
                self._insert_to_bigquery(transformed_data)
                logger.info(f"Batch {batch_id}: Successfully synced {len(transformed_data)} events.")
            except GoogleCloudError:
                error_count += len(transformed_data)

        return {"synced_count": len(transformed_data), "error_count": error_count}

    def sync_time_segment(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Syncs all data within a single time segment, handling batching."""
        logger.info(f"Worker: Starting sync for segment: {start_time} -> {end_time}")
        events_to_sync = self.get_events_to_sync(start_time, end_time)
        total_events = len(events_to_sync)
        if total_events == 0:
            logger.info(f"Segment {start_time} -> {end_time}: No new events to sync.")
            return {"status": "success", "total_events": 0, "synced_events": 0, "error_events": 0}

        total_synced = 0
        total_errors = 0
        for i in range(0, total_events, BATCH_SIZE):
            batch = events_to_sync[i:i + BATCH_SIZE]
            batch_id = f"{start_time.strftime('%Y%m%d%H%M%S')}-{i // BATCH_SIZE + 1}"
            result = self._process_batch(batch, batch_id)
            total_synced += result["synced_count"]
            total_errors += result["error_count"]

        status = "success" if total_errors == 0 else "partial_success"
        logger.info(f"Segment {start_time} -> {end_time} sync complete. Status: {status}, Synced: {total_synced}, Errors: {total_errors}")
        return {"status": status, "total_events": total_events, "synced_events": total_synced, "error_events": total_errors}

    def estimate_query_cost(self, query: str, job_config: bigquery.QueryJobConfig) -> Dict[str, Any]:
        """Estimates the cost of a BigQuery query using dry run."""
        try:
            # Create a new dry run job config
            dry_run_config = bigquery.QueryJobConfig()

            # Copy relevant parameters from original config
            if job_config.query_parameters:
                dry_run_config.query_parameters = job_config.query_parameters
            if job_config.use_legacy_sql is not None:
                dry_run_config.use_legacy_sql = job_config.use_legacy_sql

            # Set dry run specific options
            dry_run_config.dry_run = True
            dry_run_config.use_query_cache = False

            # Execute dry run
            job = self.bigquery_client.query(query, job_config=dry_run_config)

            # Calculate cost (approximately $5 per TB in most regions)
            bytes_processed = job.total_bytes_processed or 0
            tb_processed = bytes_processed / (1024**4) if bytes_processed else 0
            estimated_cost_usd = tb_processed * 5.0  # $5 per TB

            return {
                "bytes_processed": bytes_processed,
                "tb_processed": round(tb_processed, 6),
                "estimated_cost_usd": round(estimated_cost_usd, 4),
                "query_slots": job.slot_millis // 1000 if job.slot_millis else 0
            }
        except Exception as e:
            logger.warning(f"Failed to estimate query cost: {e}")
            return {
                "bytes_processed": 0,
                "tb_processed": 0.0,
                "estimated_cost_usd": 0.0,
                "query_slots": 0,
                "error": str(e)
            }

    def get_source_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the source table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Source table `{self.source_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get source table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_target_table_count_for_date(self, target_date: datetime) -> Dict[str, Any]:
        """Retrieves the count of events in the target table for a specific date with cost estimation."""
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        query = f"""
            SELECT COUNT(*) as count
            FROM `{self.target_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
            AND partner_source = 'legacy-tagtoo-event'
        """
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_of_day),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_of_day),
            ]
        )

        # Estimate cost first
        cost_estimate = self.estimate_query_cost(query, job_config)

        # Log cost warning if high
        if cost_estimate.get("estimated_cost_usd", 0) > 0.1:  # > $0.10
            logger.warning(f"High cost query detected: ${cost_estimate.get('estimated_cost_usd', 0):.4f} USD, {cost_estimate.get('tb_processed', 0):.6f} TB")

        try:
            start_time = datetime.utcnow()
            result = list(self.bigquery_client.query(query, job_config=job_config).result())
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            return {
                "count": result[0].count if result else 0,
                "query_stats": {
                    **cost_estimate,
                    "execution_time_seconds": round(execution_time, 2)
                }
            }
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
            return {"count": 0, "query_stats": cost_estimate}
        except Exception as e:
            logger.error(f"Failed to get target table count: {e}", exc_info=True)
            return {"count": 0, "query_stats": cost_estimate}

    def get_latest_data_timestamp(self) -> Optional[datetime]:
        """Retrieves the latest event_time from the target table."""
        query = f"""
            SELECT MAX(event_time) as latest_time
            FROM `{self.target_table}`
            WHERE partner_source = 'legacy-tagtoo-event'
        """
        try:
            result = list(self.bigquery_client.query(query).result())
            if result and result[0].latest_time:
                return result[0].latest_time
        except NotFound:
            logger.warning(f"Target table `{self.target_table}` not found.")
        except Exception as e:
            logger.error(f"Failed to get latest data timestamp: {e}", exc_info=True)
        return None

    def get_sync_status_for_date(self, target_date: datetime = None) -> Dict[str, Any]:
        """Retrieves comprehensive sync status for a specific date with BigQuery cost analysis."""
        if target_date is None:
            target_date = datetime.utcnow()

        # Get basic sync metadata
        last_sync_time = self.get_last_sync_time()

        # Get data counts for the target date (with cost estimation)
        source_result = self.get_source_table_count_for_date(target_date)
        target_result = self.get_target_table_count_for_date(target_date)

        # Extract counts from the new format
        source_count = source_result.get("count", 0)
        target_count = target_result.get("count", 0)

        # Get latest data timestamp
        latest_timestamp = self.get_latest_data_timestamp()

        # Calculate sync coverage
        sync_coverage = (target_count / source_count * 100) if source_count > 0 else 0

        # Calculate total cost for this status check
        total_cost = (source_result.get("query_stats", {}).get("estimated_cost_usd", 0) +
                     target_result.get("query_stats", {}).get("estimated_cost_usd", 0))
        total_tb_processed = (source_result.get("query_stats", {}).get("tb_processed", 0) +
                             target_result.get("query_stats", {}).get("tb_processed", 0))
        total_execution_time = (source_result.get("query_stats", {}).get("execution_time_seconds", 0) +
                               target_result.get("query_stats", {}).get("execution_time_seconds", 0))

        # Determine status
        status = "healthy"
        warnings = []
        cost_warnings = []

        # Data coverage warnings
        if source_count == 0:
            warnings.append("No source data found for the specified date")
        elif target_count == 0:
            status = "warning"
            warnings.append("No target data found for the specified date")
        elif sync_coverage < 95:
            status = "warning"
            warnings.append(f"Sync coverage is only {sync_coverage:.1f}%")

        # Time-based warnings
        if last_sync_time:
            hours_since_sync = (datetime.utcnow() - last_sync_time).total_seconds() / 3600
            if hours_since_sync > 6:
                status = "warning"
                warnings.append(f"Last sync was {hours_since_sync:.1f} hours ago")
        else:
            status = "warning"
            warnings.append("No sync history found")

        # Cost-based warnings
        if total_cost > 0.5:  # > $0.50
            cost_warnings.append(f"High query cost: ${total_cost:.4f} USD")
            status = "warning"
        elif total_cost > 0.1:  # > $0.10
            cost_warnings.append(f"Moderate query cost: ${total_cost:.4f} USD")

        if total_execution_time > 30:  # > 30 seconds
            cost_warnings.append(f"Slow queries detected: {total_execution_time:.2f}s total execution time")

        # Optimization suggestions
        optimization_suggestions = []
        if total_tb_processed > 0.1:  # > 100 GB
            optimization_suggestions.append("Consider adding date partitioning filters to reduce data scan")
        if total_execution_time > 10:
            optimization_suggestions.append("Consider adding indexes or optimizing WHERE clauses")

        return {
            "date": target_date.strftime("%Y-%m-%d"),
            "status": status,
            "last_sync_time": last_sync_time.isoformat() if last_sync_time else None,
            "source_events_count": source_count,
            "target_events_count": target_count,
            "sync_coverage_percent": round(sync_coverage, 2),
            "latest_data_timestamp": latest_timestamp.isoformat() if latest_timestamp else None,
            "warnings": warnings + cost_warnings,
            "table_info": {
                "source_table": self.source_table,
                "target_table": self.target_table
            },
            "bigquery_analysis": {
                "total_cost_usd": round(total_cost, 4),
                "total_tb_processed": round(total_tb_processed, 6),
                "total_execution_time_seconds": round(total_execution_time, 2),
                "source_query_stats": source_result.get("query_stats", {}),
                "target_query_stats": target_result.get("query_stats", {}),
                "optimization_suggestions": optimization_suggestions
            }
        }

    def generate_time_segments(self, start_date: datetime, end_date: datetime) -> List[tuple[datetime, datetime]]:
        """Generates time segments for the given date range."""
        segments = []
        current_time = start_date
        while current_time < end_date:
            segment_end = current_time + timedelta(hours=HOURS_PER_SEGMENT)
            if segment_end > end_date:
                segment_end = end_date
            segments.append((current_time, segment_end))
            current_time = segment_end
        return segments

    def coordinate_sync(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None, worker_url: Optional[str] = None) -> Dict[str, Any]:
        """Coordinator logic: generates segments and creates Cloud Tasks."""
        logger.info("Coordinator: Starting sync coordination.")

        # Set worker URL if provided or build it dynamically
        if worker_url:
            self.worker_url = worker_url
        elif not self.worker_url:
            # Try to build it from Flask request context (preferred method)
            try:
                from flask import request
                # 修正：確保生產環境總是使用 HTTPS
                base_url = request.url_root.rstrip('/')
                if ENVIRONMENT == 'prod' and base_url.startswith('http://'):
                    # Cloud Run 內部可能使用 HTTP，但外部訪問需要 HTTPS
                    base_url = base_url.replace('http://', 'https://')
                self.worker_url = f"{base_url}{WORKER_URL_PATH}"
                logger.info(f"Built worker URL from request context: {self.worker_url}")
            except RuntimeError:
                # 不在 request context - 提供測試環境的 fallback
                # 這種情況通常發生在測試環境或直接調用時
                if ENVIRONMENT in ['dev', 'test', 'ci']:
                    # 測試環境使用 localhost fallback
                    self.worker_url = f"http://localhost:8080{WORKER_URL_PATH}"
                    logger.info(f"Using test fallback worker URL: {self.worker_url}")
                else:
                    # 生產環境建立基於環境變數的 URL
                    self.worker_url = f"https://legacy-event-sync-{ENVIRONMENT}-{GCP_LOCATION}.a.run.app{WORKER_URL_PATH}"
                    logger.info(f"Built production worker URL: {self.worker_url}")

        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            # 修復：將初始同步範圍從30天改為1天，避免創建過多任務
            # 這可以防止記憶體不足問題，後續可以通過手動觸發增量同步
            default_start = self.get_last_sync_time()
            if default_start is None:
                # 第一次運行：只同步最近1天的資料
                default_start = end_date - timedelta(days=1)
                logger.warning(f"First time sync detected. Limiting to 1 day range: {default_start} to {end_date}")
                logger.warning("For historical data, manually trigger sync with specific date ranges")
            start_date = default_start

        if start_date >= end_date:
            logger.info("No new time range to sync. Last sync was at %s.", start_date)
            return {"status": "no_new_data", "message": "No new time range to sync."}

        segments = self.generate_time_segments(start_date, end_date)
        if not segments:
            logger.info("No segments generated for the time range.")
            return {"status": "no_segments", "message": "No segments to process."}

        logger.info(f"Generated {len(segments)} segments to process from {start_date} to {end_date}.")

        # 加入安全檢查：避免創建過多任務
        max_segments_per_request = 50  # 限制單次最多50個segments
        if len(segments) > max_segments_per_request:
            logger.error(f"Too many segments ({len(segments)}). Maximum allowed: {max_segments_per_request}")
            return {
                "status": "error",
                "message": f"Time range too large. Generated {len(segments)} segments, but maximum allowed is {max_segments_per_request}. Please use smaller time ranges."
            }

        parent = self.tasks_client.queue_path(PROJECT_ID, GCP_LOCATION, self.task_queue_name)
        tasks_created = 0
        for start, end in segments:
            payload = {"start_time": start.isoformat(), "end_time": end.isoformat()}
            task = {
                "http_request": {
                    "http_method": tasks_v2.HttpMethod.POST,
                    "url": self.worker_url,
                    "headers": {"Content-type": "application/json"},
                    "body": json.dumps(payload).encode(),
                    # 新增 OIDC 認證設定，讓 Cloud Tasks 能夠呼叫受保護的 Cloud Run 服務
                    "oidc_token": {
                        "service_account_email": os.environ.get("GOOGLE_SERVICE_ACCOUNT_EMAIL", f"integrated-event-{ENVIRONMENT}@{PROJECT_ID}.iam.gserviceaccount.com"),
                        "audience": self.worker_url,
                    }
                }
            }
            try:
                logger.info(f"Creating task for segment {start} -> {end} with URL: {self.worker_url}")
                self.tasks_client.create_task(request={"parent": parent, "task": task})
                tasks_created += 1
            except GoogleCloudError as e:
                logger.error(f"Failed to create task for segment {start} -> {end}: {e}", exc_info=True)

        logger.info(f"Coordinator: Created {tasks_created} of {len(segments)} tasks.")

        if tasks_created == len(segments):
            self.set_last_sync_time(end_date)
            logger.info(f"Successfully updated last sync time to {end_date.isoformat()}")

        return {"status": "success", "total_segments": len(segments), "tasks_created": tasks_created}

# --- Flask App Routes ---
processor = LegacyEventSyncProcessor(
    source_table=SOURCE_TABLE,
    target_table=TARGET_TABLE,
    task_queue_name=TASK_QUEUE_NAME,
    worker_url=WORKER_URL,
)

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "legacy-event-sync",
        "timestamp": datetime.utcnow().isoformat(),
    })

@app.route("/start-sync", methods=["POST"])
def start_sync_endpoint():
    """Coordinator endpoint, triggered by Cloud Scheduler."""
    try:
        data = request.get_json() or {}
        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        start_date = datetime.fromisoformat(start_date_str) if start_date_str else None
        end_date = datetime.fromisoformat(end_date_str) if end_date_str else None

        # Build worker URL from current request
        # 確保生產環境總是使用 HTTPS
        base_url = request.url_root.rstrip('/')
        if ENVIRONMENT == 'prod' or base_url.startswith('https://'):
            # 生產環境或已經是 HTTPS，確保使用 HTTPS
            if base_url.startswith('http://'):
                base_url = base_url.replace('http://', 'https://')
            worker_url = f"{base_url}{WORKER_URL_PATH}"
        else:
            # 開發/測試環境，保持原來的協議
            worker_url = f"{base_url}{WORKER_URL_PATH}"

        result = processor.coordinate_sync(start_date, end_date, worker_url)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Coordinator error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/process-segment", methods=["POST"])
def process_segment_endpoint():
    """Worker endpoint, triggered by Cloud Tasks."""
    try:
        data = request.get_json(silent=True)
        if not data or "start_time" not in data or "end_time" not in data:
            logger.error("Invalid task payload received. Payload: %s", data)
            return "Invalid payload", 400

        start_time = datetime.fromisoformat(data["start_time"])
        end_time = datetime.fromisoformat(data["end_time"])

        result = processor.sync_time_segment(start_time, end_time)

        # Cloud Tasks expects a 2xx response to consider the task successful
        if result.get("status") in ["success", "partial_success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 500
    except (ValueError, TypeError) as e:
        logger.error(f"Worker error processing segment due to value/type error: {e}", exc_info=True)
        return jsonify({"status": "error", "message": f"Invalid date format or payload structure: {e}"}), 400
    except Exception as e:
        logger.error(f"Worker error processing segment: {e}", exc_info=True)
        # Return a non-2xx status to signal task failure for retry
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/sync-status", methods=["GET"])
def sync_status_endpoint():
    """Sync status endpoint, provides comprehensive sync status information."""
    try:
        # Get optional date parameter
        date_param = request.args.get("date")
        target_date = None

        if date_param:
            try:
                target_date = datetime.strptime(date_param, "%Y-%m-%d")
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "Invalid date format. Use YYYY-MM-DD format."
                }), 400

        result = processor.get_sync_status_for_date(target_date)
        return jsonify(result), 200

    except Exception as e:
        logger.error(f"Sync status endpoint error: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
