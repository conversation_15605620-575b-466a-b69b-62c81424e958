"""
測試環境檢查
驗證基本的 BigQuery 連線和認證是否正常
"""

import os
import sys
import pytest
from datetime import datetime

# 設定測試環境變數，表格名稱加上時間戳避免並行測試衝突
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精確到毫秒
os.environ["PROJECT_ID"] = "tagtoo-tracking"
os.environ["ENVIRONMENT"] = "test"
os.environ["BIGQUERY_DATASET"] = "temp"
os.environ["TARGET_TABLE"] = f"tagtoo-tracking.temp.integrated_event_test_{TIMESTAMP}"
os.environ["SOURCE_TABLE"] = f"tagtoo-tracking.temp.tagtoo_event_test_{TIMESTAMP}"

# 讓其他 imports 讀取到正確的環境變數
from src.main import LegacyEventSyncProcessor

from google.cloud import bigquery


def check_bigquery_connection():
    """檢查 BigQuery 連線"""
    print("🔍 檢查 BigQuery 連線...")
    try:
        client = bigquery.Client(project="tagtoo-tracking")

        # 測試基本查詢權限
        query = "SELECT 1 as test"
        result = client.query(query).result()
        rows = list(result)

        if len(rows) == 1 and rows[0].test == 1:
            print("✅ BigQuery 連線正常")
            return True
        else:
            print("❌ BigQuery 查詢結果異常")
            return False

    except Exception as e:
        print(f"❌ BigQuery 連線失敗: {e}")
        return False


def check_dataset_access():
    """檢查 temp dataset 存取權限"""
    print("🔍 檢查 temp dataset 權限...")
    try:
        client = bigquery.Client(project="tagtoo-tracking")

        # 檢查 dataset 是否存在
        dataset_id = "tagtoo-tracking.temp"
        dataset = client.get_dataset(dataset_id)
        print(f"✅ Dataset {dataset_id} 存在")

        # 測試在 temp dataset 中建立暫存表的權限
        test_table_id = f"{dataset_id}.test_permissions_{int(datetime.utcnow().timestamp())}"

        schema = [
            bigquery.SchemaField("test_field", "STRING", mode="NULLABLE"),
        ]

        table = bigquery.Table(test_table_id, schema=schema)
        table = client.create_table(table)
        print(f"✅ 可以在 temp dataset 中建立表格")

        # 立即刪除測試表格
        client.delete_table(test_table_id)
        print("✅ 可以刪除表格")

        return True

    except Exception as e:
        print(f"❌ Dataset 權限檢查失敗: {e}")
        return False


def check_environment_variables():
    """檢查環境變數"""
    print("🔍 檢查環境變數...")

    required_vars = [
        "PROJECT_ID",
        "BIGQUERY_DATASET",
        "TARGET_TABLE",
        "SOURCE_TABLE"
    ]

    all_good = True
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}={value}")
        else:
            print(f"❌ {var} 未設定")
            all_good = False

    return all_good


def main():
    """主要檢查函數"""
    print("🚀 Legacy Event Sync 測試環境檢查")
    print("=" * 50)

    checks = [
        ("環境變數", check_environment_variables),
        ("BigQuery 連線", check_bigquery_connection),
        ("Dataset 權限", check_dataset_access),
    ]

    results = []

    for name, check_func in checks:
        print(f"\n📋 {name}")
        success = check_func()
        results.append((name, success))

    print("\n" + "=" * 50)
    print("📊 檢查結果總結:")

    all_passed = True
    for name, success in results:
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"  {name}: {status}")
        if not success:
            all_passed = False

    if all_passed:
        print("\n🎉 所有檢查都通過！可以執行真實 BigQuery 測試")
        return 0
    else:
        print("\n⚠️ 部分檢查失敗，請修正後再執行測試")
        return 1


if __name__ == "__main__":
    sys.exit(main())
