#!/usr/bin/env python3
"""
Optimized MERGE and Deduplication Logic Test

🎯 目的：
確保優化後的 MERGE 邏輯和記憶體去重功能正常運作。

🧪 測試重點：
1. 驗證 `USE_OPTIMIZED_MERGE` 開關能正確啟用/停用記憶體去重。
2. 驗證記憶體去重邏輯能正確過濾重複資料。
3. 確保簡化後的 MERGE 查詢語法正確，特別是 ON 條件中的 NULL 處理。
4. 確保在任何情況下都不會產生包含 `PARTITION BY` 的複雜 MERGE 查詢。
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os
from datetime import datetime

# 添加 src 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import LegacyEventSyncProcessor
import logging

logger = logging.getLogger(__name__)

def create_full_mock_row(permanent, ec_id, event_time, link, order_id, event_name="focus", value=None, currency=None):
    """輔助函數，創建一個結構完整的模擬 BigQuery Row 物件"""
    row = Mock()
    row.permanent = permanent
    row.ec_id = ec_id
    row.event_time = datetime.fromisoformat(event_time) if isinstance(event_time, str) else event_time
    row.link = link

    # 模擬 get 方法的行為
    _data = {
        "permanent": permanent,
        "ec_id": ec_id,
        "event_time": row.event_time,
        "link": link,
        "event": {
            "name": event_name,
            "value": value,
            "currency": currency,
            "custom_data": {"order_id": order_id},
            "items": [] # 確保 items 是可迭代的
        },
        "user": {},
        "location": {}
    }

    def get_side_effect(key, default=None):
        # 處理巢狀 get
        if '.' in key:
            keys = key.split('.')
            val = _data
            for k in keys:
                if val is None: return default
                val = val.get(k, default)
            return val
        return _data.get(key, default)

    row.get.side_effect = get_side_effect
    return row


class TestOptimizedMergeAndDeduplication(unittest.TestCase):
    """測試優化後的 MERGE 和記憶體去重邏輯"""

    def setUp(self):
        """設定測試環境"""
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]

        with patch('main.bigquery.Client'), \
             patch('main.bigquery.Table'), \
             patch('main.tasks_v2.CloudTasksClient'):

            self.processor = LegacyEventSyncProcessor(
                source_table="test-project.test_dataset.source_table",
                target_table="test-project.test_dataset.target_table"
            )

        mock_client = Mock()
        mock_table = Mock()
        mock_table.schema = []
        mock_client.get_table.return_value = mock_table
        mock_client.create_table.return_value = mock_table
        mock_client.insert_rows_json.return_value = []
        mock_client.delete_table.return_value = None

        mock_job = Mock()
        mock_job.result.return_value = None
        mock_job.num_dml_affected_rows = 1
        mock_client.query.return_value = mock_job

        self.processor._bigquery_client = mock_client
        self.mock_bigquery_client = mock_client

    def tearDown(self):
        if "USE_OPTIMIZED_MERGE" in os.environ:
            del os.environ["USE_OPTIMIZED_MERGE"]

    def test_simplified_merge_query_is_always_used(self):
        """測試確保始終使用簡化版 MERGE 查詢，無論是否啟用去重"""
        # Mock BigQuery client
        mock_client = Mock()
        mock_job = Mock()
        mock_job.num_dml_affected_rows = 1
        mock_client.query.return_value = mock_job

        # 設定 mock client
        self.processor._bigquery_client = mock_client

        # Mock 成本估算以避免數學運算錯誤
        with patch.object(self.processor, 'estimate_query_cost') as mock_estimate:
            mock_estimate.return_value = {
                "estimated_cost_usd": 0.0001,
                "bytes_processed": 1024,
                "tb_processed": 0.000001,
                "query_slots": 10
            }

            # Mock 成本追踪和臨時表操作
            with patch.object(self.processor, '_track_bigquery_operation'):
                with patch.object(self.processor, '_create_temp_table_and_load_data'):
                    test_data = [{"permanent": "test", "ec_id": 123}]

                    # 執行 MERGE 操作
                    self.processor._merge_to_bigquery(test_data)

                    # 獲取 BigQuery 查詢執行（應該只有 MERGE 查詢）
                    executed_queries = mock_client.query.call_args_list

                    # 至少應該有一個查詢（MERGE 查詢）
                    self.assertGreater(len(executed_queries), 0, "Should have at least one query execution")

                    # 檢查查詢是否為 MERGE 查詢
                    merge_query = executed_queries[0][0][0]  # 第一個查詢的 SQL
                    self.assertIn("MERGE", merge_query)
                    self.assertIn("WHEN NOT MATCHED THEN", merge_query)
                    self.assertNotIn("WHEN MATCHED THEN", merge_query)  # 確認沒有 UPDATE 語句

                    # 確保沒有複雜的去重邏輯（如 ROW_NUMBER, PARTITION BY）
                    self.assertNotIn("ROW_NUMBER()", merge_query)
                    self.assertNotIn("PARTITION BY", merge_query)

    def test_memory_deduplication_with_toggle(self):
        """測試 USE_OPTIMIZED_MERGE 開關能正確控制記憶體去重"""
        base_time = "2023-01-01T00:00:00"
        test_data = [
            create_full_mock_row("user1", 1, base_time, "link1", "order1"),
            create_full_mock_row("user1", 1, base_time, "link1", "order1"), # 重複
            create_full_mock_row("user2", 2, "2023-01-01T01:00:00", "link2", "order2"),
        ]

        with patch.object(self.processor, '_insert_to_bigquery') as mock_insert:
            os.environ["USE_OPTIMIZED_MERGE"] = "false"
            self.processor._process_batch(test_data, "batch-no-dedup")
            self.assertEqual(mock_insert.call_count, 1)
            self.assertEqual(len(mock_insert.call_args[0][0]), 3)
            mock_insert.reset_mock()

            os.environ["USE_OPTIMIZED_MERGE"] = "true"
            self.processor._process_batch(test_data, "batch-with-dedup")
            self.assertEqual(mock_insert.call_count, 1)
            self.assertEqual(len(mock_insert.call_args[0][0]), 2)

    def test_simplified_merge_on_condition_safety(self):
        """測試簡化版 MERGE 查詢的 ON 條件能安全處理 NULL"""
        test_data = [{"permanent": "user1"}]

        executed_queries = []
        def capture_query(query, *args, **kwargs):
            executed_queries.append(query)
            mock_job = Mock()
            mock_job.result.return_value = None
            return mock_job
        self.mock_bigquery_client.query.side_effect = capture_query

        with patch.object(self.processor, '_create_temp_table_and_load_data'):
            self.processor._insert_to_bigquery(test_data)

        self.assertTrue(len(executed_queries) > 0)
        merge_query = executed_queries[0]

        self.assertIn("IFNULL(T.link, '') = IFNULL(S.link, '')", merge_query)
        self.assertIn("T.permanent = S.permanent", merge_query)

    def test_no_float64_error_in_simplified_merge(self):
        """測試簡化版 MERGE 不會因為 value 欄位導致 FLOAT64 錯誤"""
        test_data = [create_full_mock_row("user1", 1, "2023-01-01T00:00:00", "l1", "o1", value=99.99)]

        executed_queries = []
        def capture_query(query, *args, **kwargs):
            executed_queries.append(query)
            mock_job = Mock()
            mock_job.result.return_value = None
            return mock_job
        self.mock_bigquery_client.query.side_effect = capture_query

        with patch.object(self.processor, '_create_temp_table_and_load_data'):
            self.processor._insert_to_bigquery(test_data)

        self.assertTrue(len(executed_queries) > 0)
        merge_query = executed_queries[0]
        self.assertNotIn("PARTITION BY", merge_query)


if __name__ == "__main__":
    unittest.main(verbosity=2)
