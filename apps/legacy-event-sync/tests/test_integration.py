"""
BigQuery 整合測試
使用真實的 BigQuery 環境進行完整的整合測試
成本控制：使用時間戳命名、TTL 自動清理、最小資料集
"""
import os
import pytest
import time
from datetime import datetime, timedelta
from google.cloud import bigquery
from unittest.mock import Mock, patch
import sys
from google.cloud.exceptions import NotFound
from google.api_core.exceptions import NotFound
import json

# 設定測試環境變數
os.environ["PROJECT_ID"] = "tagtoo-tracking"

# 使用時間戳避免表格名稱衝突，與其他測試檔案格式一致
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精確到毫秒
SOURCE_TABLE_NAME = f"tagtoo_event_test_{TIMESTAMP}"
TARGET_TABLE_NAME = f"integrated_event_test_{TIMESTAMP}"

os.environ["SOURCE_TABLE"] = f"tagtoo-tracking.temp.{SOURCE_TABLE_NAME}"
os.environ["TARGET_TABLE"] = f"tagtoo-tracking.temp.{TARGET_TABLE_NAME}"
os.environ["BIGQUERY_DATASET"] = "temp"

# 加入 src 路徑
sys.path.insert(0, "/app/src")

from main import LegacyEventSyncProcessor


@pytest.fixture(scope="function")
def processor(request):
    """
    Creates a single processor instance for the integration test class.
    It handles setup and teardown of the BigQuery test table.
    """
    # Use a unique suffix for tables to avoid collisions in parallel tests
    timestamp_suffix = datetime.utcnow().strftime("%Y%m%d_%H%M%S_%f")
    source_table_id = f"tagtoo-tracking.temp.tagtoo_event_test_{timestamp_suffix}"
    target_table_id = f"tagtoo-tracking.temp.integrated_event_test_{timestamp_suffix}"

    # Instantiate processor with dynamic table names
    proc = LegacyEventSyncProcessor(
        source_table=source_table_id,
        target_table=target_table_id
    )

    # We must re-import or reload the module for it to pick up the new env vars
    # For simplicity in testing, we'll just set them here for any code that might still use them
    os.environ["SOURCE_TABLE"] = source_table_id
    os.environ["TARGET_TABLE"] = target_table_id

    # Setup: Create source and target tables
    try:
        # 修正 Schema 檔案路徑
        with open("/app/terraform/schema/tagtoo_event.json", "r") as f:
            source_schema = [bigquery.SchemaField.from_api_repr(field) for field in json.load(f)]
        source_table = bigquery.Table(source_table_id, schema=source_schema)
        # 設定表格 TTL 為 1 小時
        source_table.expires = datetime.utcnow() + timedelta(hours=1)
        proc.bigquery_client.create_table(source_table, exists_ok=True)
        print(f"✅ Created source table: {source_table_id}")

        with open("/app/terraform/schema/integrated_event.json", "r") as f:
            target_schema = [bigquery.SchemaField.from_api_repr(field) for field in json.load(f)]
        target_table = bigquery.Table(target_table_id, schema=target_schema)
        # 設定表格 TTL 為 1 小時
        target_table.expires = datetime.utcnow() + timedelta(hours=1)
        proc.bigquery_client.create_table(target_table, exists_ok=True)
        print(f"✅ Created target table: {target_table_id}")

    except Exception as e:
        pytest.fail(f"Failed to create test tables: {e}")

    yield proc

    # Teardown: Clean up tables
    print(f"🧹 Cleaning up test environment (timestamp: {timestamp_suffix})")
    try:
        proc.bigquery_client.delete_table(source_table_id, not_found_ok=True)
        print(f"✅ Deleted source table: {source_table_id}")
        proc.bigquery_client.delete_table(target_table_id, not_found_ok=True)
        print(f"✅ Deleted target table: {target_table_id}")
    except Exception as e:
        print(f"Error during teardown: {e}")


@pytest.fixture(scope="function")
def test_data(processor):
    """Inserts test data into the source table."""
    # 修正：使用 create_test_event 輔助函數來建立符合 source table schema 的資料
    test_event_data = create_test_event(
        permanent="perm-1",
        ec_id=123,
        event_name="view",
    )
    # 固定 event_time 以便測試
    test_event_data["event_time"] = datetime(2025, 1, 1, 0, 30, 0).isoformat()
    # 簡化部分欄位以匹配舊測試的意圖，但保留 schema 完整性
    test_event_data["event"]["value"] = None
    test_event_data["event"]["currency"] = None
    test_event_data["event"]["items"] = []
    test_event_data["event"]["custom_data"] = None
    test_event_data["user"]["em"] = "<EMAIL>"
    test_event_data["user"]["ph"] = None

    rows_to_insert = [test_event_data]

    errors = processor.bigquery_client.insert_rows_json(processor.source_table, rows_to_insert)
    if errors:
        pytest.fail(f"Failed to insert test data: {errors}")
    print("✅ Inserted test data.")


@pytest.mark.usefixtures("processor", "test_data")
class TestLegacyEventSyncIntegration:
    """Legacy Event Sync 完整整合測試 - 使用真實 BigQuery（成本優化版）"""

    @pytest.fixture(autouse=True)
    def _processor_instance(self, processor):
        """Makes the processor instance available to all tests in the class."""
        self.processor = processor

    @pytest.mark.integration
    def test_bigquery_connection(self):
        """Test that the BigQuery client can connect and query."""
        query = f"SELECT 1"
        result = list(self.processor.bigquery_client.query(query).result())
        assert len(result) == 1

    @pytest.mark.integration
    def test_get_last_sync_time(self):
        """Test getting and setting the last sync time in Firestore."""
        doc_ref_path = self.processor.last_sync_doc_ref

        # Ensure clean state
        try:
            self.processor.firestore_client.document(doc_ref_path).delete()
        except NotFound:
            pass # It's already gone, which is fine

        # Should be None initially
        assert self.processor.get_last_sync_time() is None

        # Set and get a time
        now = datetime.utcnow().replace(microsecond=0)
        self.processor.set_last_sync_time(now)
        time.sleep(1) # Firestore is eventually consistent
        retrieved_time = self.processor.get_last_sync_time().replace(microsecond=0)
        assert retrieved_time == now

        # Cleanup
        self.processor.firestore_client.document(doc_ref_path).delete()

    @pytest.mark.integration
    @patch("google.cloud.tasks_v2.CloudTasksClient")
    def test_coordinator_workflow(self, mock_tasks_client):
        """Test the coordinator workflow, mocking task creation."""
        print("🔄 Testing Coordinator Workflow...")
        # Clean up any previous sync time to ensure a predictable start
        doc_ref_path = self.processor.last_sync_doc_ref
        try:
            self.processor.firestore_client.document(doc_ref_path).delete()
        except NotFound:
            pass

        result = self.processor.coordinate_sync(end_date=datetime(2025, 1, 1, 2, 0, 0))

        mock_tasks_instance = mock_tasks_client.return_value
        assert mock_tasks_instance.create_task.call_count > 0
        assert result["status"] == "success"
        assert result["tasks_created"] > 0
        print("✅ Coordinator test passed.")

    @pytest.mark.integration
    def test_worker_and_incremental_sync(self):
        """Test the worker logic and ensure it handles incremental syncs correctly."""
        print("🔄 Testing Worker and Incremental Sync...")
        target_table_id = self.processor.target_table

        # First sync for a specific time segment
        result1 = self.processor.sync_time_segment(
            datetime(2025, 1, 1, 0, 0, 0), datetime(2025, 1, 1, 1, 0, 0)
        )
        assert result1["synced_events"] == 1
        assert result1["status"] == "success"

        # Verify data is in the target table
        rows = list(self.processor.bigquery_client.query(f"SELECT COUNT(*) as c FROM `{target_table_id}`").result())
        assert rows[0].c == 1

        # Second sync for a new segment, should sync nothing as there's no new data
        result2 = self.processor.sync_time_segment(
            datetime(2025, 1, 1, 1, 0, 0), datetime(2025, 1, 1, 2, 0, 0)
        )
        assert result2["total_events"] == 0 # No events in this new time range

        # Verify data count is still 1
        rows_after_second_sync = list(self.processor.bigquery_client.query(f"SELECT COUNT(*) as c FROM `{target_table_id}`").result())
        assert rows_after_second_sync[0].c == 1
        print("✅ Worker and incremental sync test passed.")

    @pytest.mark.integration
    def test_sync_status_api_integration(self):
        """Test the sync status API with real BigQuery data."""
        print("🔄 Testing Sync Status API Integration...")

        # First, sync some data
        result = self.processor.sync_time_segment(
            datetime(2025, 1, 1, 0, 0, 0), datetime(2025, 1, 1, 1, 0, 0)
        )
        assert result["synced_events"] == 1

        # Now test the sync status for that date
        status = self.processor.get_sync_status_for_date(datetime(2025, 1, 1))

        # Verify the status contains expected information
        assert status["date"] == "2025-01-01"
        assert "status" in status
        assert status["source_events_count"] >= 0
        assert status["target_events_count"] >= 0
        assert "sync_coverage_percent" in status
        assert "table_info" in status
        assert status["table_info"]["source_table"] == self.processor.source_table
        assert status["table_info"]["target_table"] == self.processor.target_table

        # Check BigQuery analysis is present
        assert "bigquery_analysis" in status
        assert "total_cost_usd" in status["bigquery_analysis"]
        assert "total_tb_processed" in status["bigquery_analysis"]
        assert "total_execution_time_seconds" in status["bigquery_analysis"]
        assert "source_query_stats" in status["bigquery_analysis"]
        assert "target_query_stats" in status["bigquery_analysis"]
        assert "optimization_suggestions" in status["bigquery_analysis"]

        # Test individual count methods (new format)
        source_result = self.processor.get_source_table_count_for_date(datetime(2025, 1, 1))
        target_result = self.processor.get_target_table_count_for_date(datetime(2025, 1, 1))

        # Check that results are dictionaries with expected structure
        assert isinstance(source_result, dict)
        assert isinstance(target_result, dict)
        assert "count" in source_result
        assert "count" in target_result
        assert "query_stats" in source_result
        assert "query_stats" in target_result

        # Extract counts for comparison
        source_count = source_result["count"]
        target_count = target_result["count"]

        assert source_count >= 0
        assert target_count >= 0
        assert status["source_events_count"] == source_count
        assert status["target_events_count"] == target_count

        # Check query stats structure
        for stats in [source_result["query_stats"], target_result["query_stats"]]:
            assert "bytes_processed" in stats
            assert "tb_processed" in stats
            assert "estimated_cost_usd" in stats
            assert "execution_time_seconds" in stats

        print("✅ Sync status API integration test passed.")


# 添加清理工具函數
def cleanup_test_tables():
    """清理可能遺留的測試表格"""
    client = bigquery.Client(project="tagtoo-tracking")
    dataset = client.dataset("temp")

    tables = client.list_tables(dataset)
    cleaned = 0

    for table in tables:
        # 只支援新的命名格式
        if table.table_id.startswith(("tagtoo_event_test_", "integrated_event_test_")):
            try:
                client.delete_table(table)
                cleaned += 1
                print(f"🧹 清理遺留表格: {table.table_id}")
            except Exception as e:
                print(f"❌ 清理失敗: {table.table_id} - {e}")

    print(f"✅ 清理完成，共清理 {cleaned} 個表格")


def create_test_event(permanent="test-permanent", ec_id=123, event_name="test_event"):
    """建立測試事件資料 - 符合正確的 tagtoo_event schema"""
    return {
        "permanent": permanent,
        "ec_id": ec_id,
        "language": "zh-TW",
        "link": "https://example.com",
        "referrer": "https://google.com",
        "event_time": datetime.utcnow().isoformat(),
        "ip_address": "127.0.0.1",
        "location": {
            "country_code": "TW",
            "region_name": "Taipei",
            "city_name": "Taipei",
            "latitude": 25.0330,
            "longitude": 121.5654,
            "zip_code": "100"
        },
        "user_agent": {
            "browser": "Chrome",
            "browser_version": "91.0",
            "os": "Windows",
            "os_version": "10",
            "device": "Desktop",
            "is_mobile": False,
            "is_tablet": False,
            "is_pc": True,
            "is_touch_capable": False,
            "is_bot": False
        },
        "event": {
            "name": event_name,
            "value": 100.0,
            "currency": "TWD",
            "items": [
                {
                    "id": "product-123",
                    "name": "Test Product",
                    "price": 100.0,
                    "quantity": 1.0,
                    "availability": "in_stock"
                }
            ],
            "custom_data": {
                "order_id": "order-456",
                "payment_method": "credit_card"
            }
        },
        "user": {
            "em": "<EMAIL>",
            "ph": "+1234567890"
        },
        "session": {
            "id": "session-123",
            "source": "google",
            "medium": "cpc"
        }
    }


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--cleanup", action="store_true", help="清理遺留的測試表格")
    args = parser.parse_args()

    if args.cleanup:
        cleanup_test_tables()
    else:
        pytest.main([__file__, "-v", "-s"])
