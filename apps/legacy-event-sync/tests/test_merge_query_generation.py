#!/usr/bin/env python3
"""
Simplified MERGE Query Generation Test

🎯 目的：
專門測試簡化後的 MERGE 查詢生成邏輯，確保其 SQL 語法和結構的正確性。

🧪 測試重點：
1. 驗證 MERGE 查詢的基本 SQL 語法。
2. 確保 ON 條件的欄位完整且能安全處理 NULL。
3. 驗證 INSERT 子句包含所有必要的欄位。
4. 確保查詢中不包含 UPDATE 子句。
5. 驗證日誌記錄的正確性。
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import LegacyEventSyncProcessor


class TestSimplifiedMergeQueryGeneration(unittest.TestCase):
    """測試簡化版 MERGE 查詢的生成邏輯"""

    def setUp(self):
        """設定測試環境"""
        with patch('main.bigquery.Client'), \
             patch('main.bigquery.Table') as mock_table_class:

            self.processor = LegacyEventSyncProcessor(
                source_table="test-project.test_dataset.source_table",
                target_table="test-project.test_dataset.target_table"
            )

        mock_client = Mock()
        mock_table = Mock()
        mock_table.schema = []
        mock_client.get_table.return_value = mock_table
        mock_client.create_table.return_value = mock_table
        mock_client.insert_rows_json.return_value = []
        mock_client.delete_table.return_value = None

        mock_job = Mock()
        mock_job.result.return_value = None
        mock_job.num_dml_affected_rows = 1
        mock_client.query.return_value = mock_job

        mock_table_class.return_value = mock_table
        self.processor._bigquery_client = mock_client

        # 執行一次查詢以供所有測試使用
        self.temp_table_ref = "test_project.test_dataset.temp_table_123"

        # 攔截查詢
        executed_queries = []
        def capture_query(query, *args, **kwargs):
            executed_queries.append(query)
            return mock_job
        self.processor.bigquery_client.query.side_effect = capture_query

        self.processor._execute_merge_query(self.temp_table_ref)
        self.merge_query = executed_queries[0] if executed_queries else ""


    def test_simplified_merge_syntax_is_correct(self):
        """測試 MERGE 查詢的基本 SQL 語法正確性"""
        self.assertIn("MERGE", self.merge_query)
        self.assertIn("USING", self.merge_query)
        self.assertIn("ON", self.merge_query)
        self.assertIn("WHEN NOT MATCHED THEN", self.merge_query)
        self.assertIn("INSERT", self.merge_query)
        self.assertIn("VALUES", self.merge_query)

    def test_on_condition_is_complete_and_safe(self):
        """測試 ON 條件的完整性和 NULL 安全性"""
        expected_on_conditions = [
            "T.permanent = S.permanent",
            "T.ec_id = S.ec_id",
            "T.event_time = S.event_time",
            "T.event = S.event",
            "T.partner_source = S.partner_source",
            "IFNULL(T.link, '') = IFNULL(S.link, '')"
        ]
        for condition in expected_on_conditions:
            self.assertIn(condition, self.merge_query)

    def test_insert_clause_is_complete(self):
        """測試 INSERT 子句的欄位完整性"""
        required_insert_fields = [
            "permanent", "ec_id", "partner_source", "event_time", "create_time",
            "link", "event", "value", "currency", "order_id", "items", "user",
            "partner_id", "page", "location", "raw_json"
        ]
        # 簡單檢查 INSERT 和 VALUES 子句中是否包含所有欄位
        for field in required_insert_fields:
            self.assertIn(field, self.merge_query)

    def test_no_update_clause_exists(self):
        """測試查詢中不包含 UPDATE 子句"""
        self.assertNotIn("WHEN MATCHED THEN UPDATE", self.merge_query)

    def test_table_references_are_correct(self):
        """測試目標表和臨時表的引用是否正確"""
        self.assertIn(f"`{self.processor.target_table}` T", self.merge_query)
        self.assertIn(f"`{self.temp_table_ref}`", self.merge_query)

    def test_logging_message_is_correct(self):
        """測試 MERGE 操作完成後的日誌訊息格式是否正確（包含成本和時間資訊）"""
        # 直接測試日誌訊息的格式變更
        with patch('main.logger') as mock_logger:
            # Mock BigQuery 相關的操作
            mock_job = Mock()
            mock_job.result.return_value = None
            mock_job.num_dml_affected_rows = 1
            self.processor.bigquery_client.query.return_value = mock_job

            # Mock 成本估算以避免數學運算錯誤
            with patch.object(self.processor, 'estimate_query_cost') as mock_estimate:
                mock_estimate.return_value = {
                    "estimated_cost_usd": 0.0000,
                    "bytes_processed": 0,
                    "tb_processed": 0.0,
                    "query_slots": 0
                }

                # Mock 成本追踪方法
                with patch.object(self.processor, '_track_bigquery_operation'):
                    result = self.processor._execute_merge_query(self.temp_table_ref)

            self.assertEqual(result, 1)

            # 檢查日誌是否被呼叫
            self.assertTrue(mock_logger.info.called, "Logger.info should be called")

            # 取得最後一次日誌呼叫
            last_log_call = mock_logger.info.call_args_list[-1]
            last_log_message = last_log_call[0][0]

            # 驗證日誌包含新增的成本和時間資訊
            self.assertIn("MERGE operation completed:", last_log_message)
            self.assertIn("rows inserted", last_log_message)
            self.assertIn("Cost:", last_log_message)
            self.assertIn("USD", last_log_message)
            self.assertIn("Time:", last_log_message)


if __name__ == '__main__':
    unittest.main(verbosity=2)
