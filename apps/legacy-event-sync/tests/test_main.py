"""
Legacy Event Sync Service 單元測試
純單元測試，不建立真實 BigQuery 表格，使用 Mock 進行測試
"""

import json
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from flask import Flask
from google.cloud import bigquery

# 必須在 import app 之前設定環境變數
# 在單元測試中，這些值僅用於確保模組可以被載入，實際的外部互動會被 mock 掉
import os
os.environ["PROJECT_ID"] = "test-project"
os.environ["ENVIRONMENT"] = "test"
os.environ["GCP_LOCATION"] = "test-location"
os.environ["SOURCE_TABLE"] = "test-project.test_dataset.source"
os.environ["TARGET_TABLE"] = "test-project.test_dataset.target"
os.environ["TASK_QUEUE_NAME"] = "test-queue"
os.environ["WORKER_URL_PATH"] = "/process-segment"
# 移除假的憑證檔案路徑設定，避免影響整合測試

# Mock 掉所有會產生外部 API 呼叫的 client factory functions
# 移除 autouse=True，此 fixture 只在單元測試中被明確使用
@pytest.fixture()
def mock_gcp_clients():
    with patch("src.main.create_bigquery_client") as mock_bq, \
         patch("src.main.create_firestore_client") as mock_fs, \
         patch("src.main.create_tasks_client") as mock_tasks:
        yield {
            "bigquery": mock_bq,
            "firestore": mock_fs,
            "tasks": mock_tasks
        }

# 現在可以安全地 import app 和 processor 了
from src.main import LegacyEventSyncProcessor, app


@pytest.fixture
def client():
    """Flask test client fixture."""
    app.config["TESTING"] = True
    with app.test_client() as client:
        yield client


@pytest.fixture
def mock_processor(mock_gcp_clients): # 單元測試的 processor mock 都需要 mock_gcp_clients
    """創建一個 LegacyEventSyncProcessor 的 mock 實例"""
    # 我們 patch class 本身，這樣就不會執行真實的 __init__
    with patch("src.main.LegacyEventSyncProcessor", autospec=True) as MockProcessor:
        # 創建一個 mock instance
        processor_instance = MockProcessor.return_value
        # 將這個 mock instance patch 到 main.processor 上
        with patch("src.main.processor", processor_instance):
            yield processor_instance


class TestHealthCheck:
    """Tests for the /health endpoint."""

    @pytest.mark.unit
    def test_health_endpoint(self, client):
        """Ensure the health endpoint returns a healthy status."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.get_json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "timestamp" in data


class TestSyncStatusEndpoint:
    """Tests for the /sync-status endpoint."""

    @pytest.mark.unit
    def test_sync_status_endpoint_default_date(self, client, mock_processor):
        """Test the /sync-status endpoint with default date (today)."""
        mock_status = {
            "date": "2023-01-01",
            "status": "healthy",
            "last_sync_time": "2023-01-01T10:00:00",
            "source_events_count": 1000,
            "target_events_count": 1000,
            "sync_coverage_percent": 100.0,
            "latest_data_timestamp": "2023-01-01T09:55:00",
            "warnings": [],
            "table_info": {
                "source_table": "test-project.test_dataset.source",
                "target_table": "test-project.test_dataset.target"
            }
        }
        mock_processor.get_sync_status_for_date.return_value = mock_status

        response = client.get("/sync-status")
        assert response.status_code == 200
        assert response.get_json() == mock_status
        mock_processor.get_sync_status_for_date.assert_called_once_with(None)

    @pytest.mark.unit
    def test_sync_status_endpoint_specific_date(self, client, mock_processor):
        """Test the /sync-status endpoint with a specific date."""
        target_date = datetime(2023, 1, 15)
        mock_status = {
            "date": "2023-01-15",
            "status": "warning",
            "last_sync_time": "2023-01-15T10:00:00",
            "source_events_count": 1000,
            "target_events_count": 950,
            "sync_coverage_percent": 95.0,
            "latest_data_timestamp": "2023-01-15T09:55:00",
            "warnings": ["Sync coverage is only 95.0%"],
            "table_info": {
                "source_table": "test-project.test_dataset.source",
                "target_table": "test-project.test_dataset.target"
            }
        }
        mock_processor.get_sync_status_for_date.return_value = mock_status

        response = client.get("/sync-status?date=2023-01-15")
        assert response.status_code == 200
        assert response.get_json() == mock_status
        mock_processor.get_sync_status_for_date.assert_called_once_with(target_date)

    @pytest.mark.unit
    def test_sync_status_endpoint_invalid_date_format(self, client, mock_processor):
        """Test the /sync-status endpoint with invalid date format."""
        response = client.get("/sync-status?date=invalid-date")
        assert response.status_code == 400
        data = response.get_json()
        assert data["status"] == "error"
        assert "Invalid date format" in data["message"]
        mock_processor.get_sync_status_for_date.assert_not_called()

    @pytest.mark.unit
    def test_sync_status_endpoint_error(self, client, mock_processor):
        """Test error handling in the /sync-status endpoint."""
        mock_processor.get_sync_status_for_date.side_effect = Exception("BigQuery connection failed")
        response = client.get("/sync-status")
        assert response.status_code == 500
        data = response.get_json()
        assert data["status"] == "error"
        assert "BigQuery connection failed" in data["message"]


class TestApiEndpoints:
    """Tests for the main API endpoints (/start-sync, /process-segment)."""

    @pytest.mark.unit
    def test_start_sync_endpoint_no_dates(self, client, mock_processor):
        """Test the /start-sync coordinator endpoint without specific dates."""
        mock_processor.coordinate_sync.return_value = {"status": "success", "tasks_created": 5}
        response = client.post("/start-sync", json={})
        assert response.status_code == 200
        assert response.get_json() == {"status": "success", "tasks_created": 5}
        mock_processor.coordinate_sync.assert_called_once()
        call_args, call_kwargs = mock_processor.coordinate_sync.call_args
        assert call_args[0] is None  # start_date
        assert call_args[1] is None  # end_date
        assert call_args[2].endswith("/process-segment")  # worker_url

    @pytest.mark.unit
    def test_start_sync_endpoint_with_dates(self, client, mock_processor):
        """Test the /start-sync coordinator endpoint with specific dates."""
        start = "2023-01-01T00:00:00"
        end = "2023-01-02T00:00:00"
        mock_processor.coordinate_sync.return_value = {"status": "success", "tasks_created": 1}
        response = client.post("/start-sync", json={"start_date": start, "end_date": end})
        assert response.status_code == 200
        mock_processor.coordinate_sync.assert_called_once()

        # 修正：檢查 positional arguments 而非 keyword arguments
        call_args, call_kwargs = mock_processor.coordinate_sync.call_args
        assert call_args[0] == datetime.fromisoformat(start)
        assert call_args[1] == datetime.fromisoformat(end)


    @pytest.mark.unit
    def test_start_sync_endpoint_error(self, client, mock_processor):
        """Test error handling in the /start-sync endpoint."""
        mock_processor.coordinate_sync.side_effect = Exception("Coordinator Major Fail")
        response = client.post("/start-sync", json={})
        assert response.status_code == 500
        assert "Coordinator Major Fail" in response.get_json()["message"]

    @pytest.mark.unit
    def test_process_segment_endpoint(self, client, mock_processor):
        """Test the /process-segment worker endpoint."""
        start_dt = datetime(2023, 1, 1, 0, 0, 0)
        end_dt = datetime(2023, 1, 1, 4, 0, 0)

        # Simulate how Cloud Tasks sends the payload
        payload = {
            "start_time": start_dt.isoformat(),
            "end_time": end_dt.isoformat(),
        }

        mock_processor.sync_time_segment.return_value = {"status": "success", "synced_events": 100}

        response = client.post("/process-segment", json=payload)

        assert response.status_code == 200
        assert response.get_json() == {"status": "success", "synced_events": 100}
        mock_processor.sync_time_segment.assert_called_once_with(start_time=start_dt, end_time=end_dt)

    @pytest.mark.unit
    def test_process_segment_bad_request(self, client, mock_processor):
        """Test the /process-segment endpoint with a bad request."""
        # Test case 1: Empty JSON payload
        response = client.post("/process-segment", json={})
        assert response.status_code == 400
        assert "Invalid payload" in response.get_data(as_text=True)

        # Test case 2: Invalid date format
        response = client.post("/process-segment", json={"start_time": "invalid-date", "end_time": "2023-01-01T04:00:00"})
        assert response.status_code == 400
        assert "Invalid date format" in response.get_json()["message"]


class TestCoordinatorLogic:
    """Tests the internal logic of the coordinator."""

    @pytest.mark.unit
    def test_coordinate_sync_creates_tasks(self, mock_gcp_clients):
        """Verify that the coordinator generates segments and creates tasks."""
        # 由於 client factories 已被 mock, 我們可以安全地建立 processor 實例
        processor = LegacyEventSyncProcessor(
            source_table="s",
            target_table="t",
            task_queue_name="test-queue",
            worker_url="http://worker.test/process-segment"
        )

        # Mock 掉 processor 實例的方法，以隔離 coordinate_sync 的邏輯
        start_time = datetime(2023, 1, 10, 8, 0, 0)
        end_time = datetime(2023, 1, 10, 13, 0, 0)
        processor.get_last_sync_time = MagicMock(return_value=start_time)
        processor.set_last_sync_time = MagicMock() # Mock set_last_sync_time

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = end_time
            # fromisoformat 需要保持原樣
            mock_datetime.fromisoformat.side_effect = datetime.fromisoformat

            # 準備 tasks_client 的 mock 返回值
            tasks_client_mock = mock_gcp_clients["tasks"].return_value
            tasks_client_mock.queue_path.return_value = "projects/test-project/locations/test-location/queues/test-queue"

            # 執行被測函數
            # 在測試環境中，從 docker-compose.yml 讀取的 HOURS_PER_SEGMENT 是 1
            result = processor.coordinate_sync()

        # 斷言
        # 修正：從 8:00 到 13:00，每小時一個 segment，應該是 5 個
        assert result["tasks_created"] == 5
        assert tasks_client_mock.create_task.call_count == 5

        # 檢查第一個任務的內容
        first_call_args = tasks_client_mock.create_task.call_args_list[0][1]
        task_payload = json.loads(first_call_args['request']['task']['http_request']['body'])
        # 修正：第一個 segment 應該是 8:00 到 9:00
        assert task_payload['start_time'] == "2023-01-10T08:00:00"
        assert task_payload['end_time'] == "2023-01-10T09:00:00"

        # 驗證 set_last_sync_time 被正確呼叫
        processor.set_last_sync_time.assert_called_once_with(end_time)


class TestDataTransformation:
    """Tests for the data transformation logic."""

    @pytest.mark.unit
    def test_transform_event_data_logic(self, mock_gcp_clients):
        """Test the core data transformation logic with a sample event."""
        processor = LegacyEventSyncProcessor(source_table="dummy", target_table="dummy")

        # Sample input row (as a dictionary for simplicity in testing)
        input_event = {
            "permanent": "test_permanent",
            "ec_id": 123,
            "event_time": datetime(2023, 1, 1, 12, 0, 0),
            "link": "http://example.com",
            "event": {
                "name": "purchase",
                "value": 100.0,
                "currency": "USD",
                "custom_data": {"order_id": "order123"},
                "items": [{"id": "p1", "name": "product1", "price": 100.0, "quantity": 1}],
            },
            "user": {"em": "<EMAIL>", "ph": "1234567890"},
            "location": {
                "country_code": "TW",
                "region_name": "Taipei",
                "city_name": "Taipei",
            }
        }

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 5, 0)
            # 確保 fromisoformat 仍能正常工作
            mock_datetime.fromisoformat.side_effect = datetime.fromisoformat
            transformed = processor.transform_event_data(input_event)

        assert transformed["permanent"] == "test_permanent"
        assert transformed["ec_id"] == 123
        assert transformed["event"] == "purchase"
        assert transformed["value"] == 100.0
        assert transformed["order_id"] == "order123"
        assert transformed["user"]["em"] == "<EMAIL>"
        assert len(transformed["items"]) == 1
        assert transformed["items"][0]["id"] == "p1"

        # 新增欄位驗證
        assert transformed["partner_id"] is None
        assert transformed["page"] is None
        assert transformed["raw_json"] is None
        assert transformed["location"] == {
            "country_code": "TW",
            "region_name": "Taipei",
            "city_name": "Taipei",
        }


class TestSyncStatusLogic:
    """Tests for the sync status and monitoring logic."""

    @pytest.mark.unit
    def test_get_sync_status_for_date_healthy(self, mock_gcp_clients):
        """Test get_sync_status_for_date with healthy status."""
        processor = LegacyEventSyncProcessor(source_table="dummy", target_table="dummy")

        # Mock all the data retrieval methods with new format
        processor.get_last_sync_time = MagicMock(return_value=datetime(2023, 1, 1, 10, 0, 0))
        processor.get_source_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": 1024000,
                "tb_processed": 0.000001,
                "estimated_cost_usd": 0.005,
                "execution_time_seconds": 2.5
            }
        })
        processor.get_target_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": 512000,
                "tb_processed": 0.0000005,
                "estimated_cost_usd": 0.0025,
                "execution_time_seconds": 1.8
            }
        })
        processor.get_latest_data_timestamp = MagicMock(return_value=datetime(2023, 1, 1, 9, 55, 0))

        target_date = datetime(2023, 1, 1)

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 0, 0)  # 2 hours after last sync
            mock_datetime.strftime = datetime.strftime  # Keep strftime working

            result = processor.get_sync_status_for_date(target_date)

        assert result["status"] == "healthy"
        assert result["source_events_count"] == 1000
        assert result["target_events_count"] == 1000
        assert result["sync_coverage_percent"] == 100.0
        assert result["warnings"] == []

        # Check BigQuery analysis
        assert "bigquery_analysis" in result
        assert result["bigquery_analysis"]["total_cost_usd"] == 0.0075  # 0.005 + 0.0025
        assert result["bigquery_analysis"]["total_execution_time_seconds"] == 4.3  # 2.5 + 1.8
        assert isinstance(result["bigquery_analysis"]["optimization_suggestions"], list)

    @pytest.mark.unit
    def test_get_sync_status_for_date_with_warnings(self, mock_gcp_clients):
        """Test get_sync_status_for_date with warning conditions."""
        processor = LegacyEventSyncProcessor(source_table="dummy", target_table="dummy")

        # Mock data that would trigger warnings
        processor.get_last_sync_time = MagicMock(return_value=datetime(2023, 1, 1, 4, 0, 0))  # 8 hours ago
        processor.get_source_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": 1024000000,  # Higher cost
                "tb_processed": 0.001,
                "estimated_cost_usd": 0.3,  # High cost
                "execution_time_seconds": 15.0  # Slow query
            }
        })
        processor.get_target_table_count_for_date = MagicMock(return_value={
            "count": 900,  # Only 90% synced
            "query_stats": {
                "bytes_processed": 512000000,
                "tb_processed": 0.0005,
                "estimated_cost_usd": 0.25,
                "execution_time_seconds": 20.0  # Slow query
            }
        })
        processor.get_latest_data_timestamp = MagicMock(return_value=datetime(2023, 1, 1, 9, 55, 0))

        target_date = datetime(2023, 1, 1)

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 0, 0)  # 8 hours after last sync
            mock_datetime.strftime = datetime.strftime

            result = processor.get_sync_status_for_date(target_date)

        assert result["status"] == "warning"
        assert result["sync_coverage_percent"] == 90.0

        # Should have multiple warnings: coverage, old sync time, and high cost
        warnings = result["warnings"]
        assert len(warnings) >= 3
        assert any("coverage" in warning.lower() for warning in warnings)
        assert any("hours ago" in warning.lower() for warning in warnings)
        assert any("query cost" in warning.lower() for warning in warnings)

        # Check cost analysis
        assert result["bigquery_analysis"]["total_cost_usd"] == 0.55  # 0.3 + 0.25

    @pytest.mark.unit
    def test_get_sync_status_for_date_no_data(self, mock_gcp_clients):
        """Test get_sync_status_for_date with no source data."""
        processor = LegacyEventSyncProcessor(source_table="dummy", target_table="dummy")

        # Mock no data scenario
        processor.get_last_sync_time = MagicMock(return_value=datetime(2023, 1, 1, 10, 0, 0))
        processor.get_source_table_count_for_date = MagicMock(return_value={
            "count": 0,  # No source data
            "query_stats": {
                "bytes_processed": 0,
                "tb_processed": 0,
                "estimated_cost_usd": 0,
                "execution_time_seconds": 0.5
            }
        })
        processor.get_target_table_count_for_date = MagicMock(return_value={
            "count": 0,
            "query_stats": {
                "bytes_processed": 0,
                "tb_processed": 0,
                "estimated_cost_usd": 0,
                "execution_time_seconds": 0.3
            }
        })
        processor.get_latest_data_timestamp = MagicMock(return_value=None)

        target_date = datetime(2023, 1, 1)

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 0, 0)
            mock_datetime.strftime = datetime.strftime

            result = processor.get_sync_status_for_date(target_date)

        assert result["sync_coverage_percent"] == 0
        assert any("no source data" in warning.lower() for warning in result["warnings"])

        # Check that cost analysis is still present even with no data
        assert "bigquery_analysis" in result
        assert result["bigquery_analysis"]["total_cost_usd"] == 0.0

    @pytest.mark.unit
    def test_bigquery_cost_estimation(self, mock_gcp_clients):
        """Test BigQuery cost estimation functionality."""
        processor = LegacyEventSyncProcessor(source_table="dummy", target_table="dummy")

        # Mock BigQuery job for dry run
        mock_job = MagicMock()
        mock_job.total_bytes_processed = 1073741824  # 1 GB
        mock_job.slot_millis = 5000

        processor.bigquery_client.query.return_value = mock_job

        query = "SELECT COUNT(*) FROM test_table"
        job_config = bigquery.QueryJobConfig()

        result = processor.estimate_query_cost(query, job_config)

        assert result["bytes_processed"] == 1073741824
        # 1 GB = 1073741824 bytes, 1 TB = 1024^4 bytes = 1099511627776 bytes
        # So 1 GB = 1073741824 / 1099511627776 TB ≈ 0.0009765625 TB
        assert abs(result["tb_processed"] - 0.0009765625) < 0.00001  # Allow for floating point precision
        assert abs(result["estimated_cost_usd"] - 0.0048828125) < 0.0001  # $5 per TB * 0.0009765625 TB
        assert result["query_slots"] == 5  # 5000 millis / 1000


if __name__ == "__main__":
    pytest.main(["--color=yes", "-p", "no:warnings", __file__])
