"""
Tests for the legacy event sync service.

這個測試檔案的設計原則：
1. 完全隔離測試環境，不依賴真實的 GCP 服務
2. 正確 mock 全域 processor 實例和所有 GCP 客戶端
3. 測試業務邏輯而不是基礎設施連接
4. 確保測試結果反映實際的程式行為
"""

import json
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch

import pytest
from google.cloud import bigquery

# 確保測試不會嘗試連接真實的 GCP 服務
os.environ.update({
    "PROJECT_ID": "test-project",
    "ENVIRONMENT": "test",
    "GCP_LOCATION": "us-central1",
    "SOURCE_TABLE": "test-project.test_dataset.source_table",
    "TARGET_TABLE": "test-project.test_dataset.target_table",
    "TASK_QUEUE_NAME": "test-queue",
    "WORKER_URL": "http://test-worker",
    "BATCH_SIZE": "1000",
    "MINUTES_PER_SEGMENT": "5",
    "ENABLE_COST_TRACKING": "true"  # 啟用成本追踪進行測試
})


@pytest.fixture
def mock_gcp_clients():
    """Mock all GCP client factories to prevent real connections."""
    with patch("src.main.create_bigquery_client") as mock_bq, \
         patch("src.main.create_firestore_client") as mock_fs, \
         patch("src.main.create_tasks_client") as mock_tasks:

        # 設定 BigQuery client mock
        mock_bq_client = MagicMock()
        mock_bq_client.query.return_value = MagicMock()
        mock_bq.return_value = mock_bq_client

        # 設定 Firestore client mock
        mock_fs_client = MagicMock()
        mock_fs.return_value = mock_fs_client

        # 設定 Cloud Tasks client mock
        mock_tasks_client = MagicMock()
        mock_tasks_client.queue_path.return_value = "projects/test/locations/test/queues/test"
        mock_tasks.return_value = mock_tasks_client

        yield {
            "bigquery": mock_bq,
            "firestore": mock_fs,
            "tasks": mock_tasks,
            "bigquery_client": mock_bq_client,
            "firestore_client": mock_fs_client,
            "tasks_client": mock_tasks_client
        }


@pytest.fixture
def app_with_mocked_processor(mock_gcp_clients):
    """創建帶有完全 mock processor 的 Flask 應用程式"""
    # 重要：在導入 main.py 之前就要 mock 掉 GCP 客戶端
    with patch("src.main.processor") as mock_processor:
        # 設定 mock processor 的預設行為
        mock_processor.get_sync_status_for_date.return_value = {
            "status": "healthy",
            "date": datetime.utcnow().strftime("%Y-%m-%d"),
            "source_events_count": 1000,
            "target_events_count": 1000,
            "sync_coverage_percent": 100.0,
            "warnings": [],
            "last_sync_time": "2023-01-01T10:00:00",
            "latest_data_timestamp": "2023-01-01T11:55:00",
            "table_info": {
                "source_table": "test-source",
                "target_table": "test-target"
            },
            "bigquery_analysis": {
                "total_cost_usd": 0.0,
                "total_execution_time_seconds": 0.0,
                "optimization_suggestions": []
            }
        }

        mock_processor.coordinate_sync.return_value = {
            "status": "success",
            "tasks_created": 12,
            "total_segments": 12
        }

        mock_processor.sync_time_segment.return_value = {
            "status": "success",
            "total_events": 100,
            "synced_events": 100,
            "error_events": 0,
            "batches_processed": 1
        }

        # 現在安全地導入 Flask 應用
        from src.main import app
        app.config['TESTING'] = True
        yield app, mock_processor


@pytest.fixture
def client(app_with_mocked_processor):
    """Flask test client with mocked processor."""
    app, mock_processor = app_with_mocked_processor
    with app.test_client() as client:
        yield client


@pytest.fixture
def mock_processor(app_with_mocked_processor):
    """提供 mock processor 實例供測試使用"""
    app, mock_processor = app_with_mocked_processor
    yield mock_processor


class TestHealthCheck:
    """Tests for the /health endpoint."""

    @pytest.mark.unit
    def test_health_endpoint(self, client):
        """Ensure the health endpoint returns a healthy status."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.get_json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
        assert "commit" in data
        assert "timestamp" in data


class TestSyncStatusEndpoint:
    """Tests for the /sync-status endpoint."""

    @pytest.mark.unit
    def test_sync_status_endpoint_default_date(self, client, mock_processor):
        """Test the /sync-status endpoint with default date (today)."""
        response = client.get("/sync-status")
        assert response.status_code == 200

        data = response.get_json()
        assert data["status"] == "healthy"
        assert "source_events_count" in data
        assert "target_events_count" in data
        assert "sync_coverage_percent" in data

        # 驗證 mock 被正確呼叫
        mock_processor.get_sync_status_for_date.assert_called_once_with(None)

    @pytest.mark.unit
    def test_sync_status_endpoint_specific_date(self, client, mock_processor):
        """Test the /sync-status endpoint with a specific date."""
        # 設定特定日期的回傳值
        mock_processor.get_sync_status_for_date.return_value = {
            "status": "warning",
            "date": "2023-01-15",
            "source_events_count": 1000,
            "target_events_count": 950,
            "sync_coverage_percent": 95.0,
            "warnings": ["Sync coverage is only 95.0%"],
            "last_sync_time": None,
            "latest_data_timestamp": "2023-01-15T09:55:00",
            "table_info": {
                "source_table": "test-source",
                "target_table": "test-target"
            },
            "bigquery_analysis": {
                "total_cost_usd": 0.0,
                "total_execution_time_seconds": 0.0,
                "optimization_suggestions": []
            }
        }

        response = client.get("/sync-status?date=2023-01-15")
        assert response.status_code == 200

        data = response.get_json()
        assert data["date"] == "2023-01-15"
        assert data["status"] == "warning"
        assert data["source_events_count"] == 1000
        assert data["target_events_count"] == 950
        assert data["sync_coverage_percent"] == 95.0

        # 驗證 mock 被正確呼叫，且參數正確
        mock_processor.get_sync_status_for_date.assert_called_once()
        call_args = mock_processor.get_sync_status_for_date.call_args[0]
        assert call_args[0] == datetime(2023, 1, 15)

    @pytest.mark.unit
    def test_sync_status_endpoint_invalid_date_format(self, client, mock_processor):
        """Test the /sync-status endpoint with invalid date format."""
        response = client.get("/sync-status?date=invalid-date")
        assert response.status_code == 400
        data = response.get_json()
        assert data["status"] == "error"
        assert "Invalid date format" in data["message"]
        mock_processor.get_sync_status_for_date.assert_not_called()

    @pytest.mark.unit
    def test_sync_status_endpoint_error_handling(self, client, mock_processor):
        """Test error handling in the /sync-status endpoint."""
        mock_processor.get_sync_status_for_date.side_effect = Exception("BigQuery connection failed")

        response = client.get("/sync-status")
        assert response.status_code == 500
        data = response.get_json()
        assert data["status"] == "error"
        assert "BigQuery connection failed" in data["message"]


class TestApiEndpoints:
    """Tests for the main API endpoints (/start-sync, /process-segment)."""

    @pytest.mark.unit
    def test_start_sync_endpoint_no_dates(self, client, mock_processor):
        """Test the /start-sync coordinator endpoint without dates."""
        response = client.post("/start-sync", json={})
        assert response.status_code == 200

        data = response.get_json()
        assert data["status"] == "success"
        assert data["tasks_created"] == 12

        # 驗證 coordinate_sync 被正確呼叫
        mock_processor.coordinate_sync.assert_called_once()
        call_args = mock_processor.coordinate_sync.call_args[0]
        # start_date 和 end_date 應該是 None（使用預設值）
        assert call_args[0] is None  # start_date
        assert call_args[1] is None  # end_date
        # worker_url 應該是基於請求建立的
        assert isinstance(call_args[2], str)  # worker_url

    @pytest.mark.unit
    def test_start_sync_endpoint_with_dates(self, client, mock_processor):
        """Test the /start-sync coordinator endpoint with specific dates."""
        mock_processor.coordinate_sync.return_value = {"status": "success", "tasks_created": 48}

        start = "2023-01-01T00:00:00"
        end = "2023-01-01T04:00:00"

        response = client.post("/start-sync", json={"start_date": start, "end_date": end})
        assert response.status_code == 200

        data = response.get_json()
        assert data["status"] == "success"
        assert data["tasks_created"] == 48

        # 驗證呼叫參數
        mock_processor.coordinate_sync.assert_called_once()
        call_args = mock_processor.coordinate_sync.call_args[0]
        assert call_args[0] == datetime.fromisoformat(start)
        assert call_args[1] == datetime.fromisoformat(end)

    @pytest.mark.unit
    def test_start_sync_endpoint_error_handling(self, client, mock_processor):
        """Test error handling in the /start-sync endpoint."""
        mock_processor.coordinate_sync.side_effect = Exception("Task creation failed")

        response = client.post("/start-sync", json={})
        assert response.status_code == 500
        data = response.get_json()
        assert data["status"] == "error"
        assert "Task creation failed" in data["message"]

    @pytest.mark.unit
    def test_process_segment_endpoint(self, client, mock_processor):
        """Test the process segment endpoint"""
        # Mock processor methods
        mock_processor.sync_time_segment.return_value = {
            "status": "success",
            "total_events": 100,
            "synced_events": 95,
            "error_events": 5,
            "batches_processed": 2
        }

        # Mock the cost tracker and its methods to avoid formatting issues
        mock_cost_tracker = Mock()
        mock_cost_tracker.get_summary.return_value = {
            "cost_summary": {
                "total_cost_usd": 0.0012,
                "cost_efficiency_score": 85.5
            },
            "session_info": {
                "total_operations": 2
            }
        }
        mock_processor.cost_tracker = mock_cost_tracker
        mock_processor._enable_cost_tracking = True

        response = client.post('/process-segment', json={
            'start_time': '2023-01-01T00:00:00',
            'end_time': '2023-01-01T01:00:00'
        })

        assert response.status_code == 200
        data = response.get_json()
        assert data['status'] == 'success'
        assert data['synced_events'] == 95
        assert data['error_events'] == 5

        # 檢查 BigQuery 成本資訊是否包含在回應中
        assert 'bigquery_costs' in data
        assert data['bigquery_costs']['cost_summary']['total_cost_usd'] == 0.0012

        # Verify that sync_time_segment was called with correct arguments
        mock_processor.sync_time_segment.assert_called_once()
        args = mock_processor.sync_time_segment.call_args[0]
        assert len(args) == 2
        assert args[0].year == 2023
        assert args[0].month == 1
        assert args[0].day == 1
        assert args[0].hour == 0
        assert args[1].hour == 1

    @pytest.mark.unit
    def test_process_segment_bad_request(self, client, mock_processor):
        """Test the /process-segment endpoint with a bad request."""
        # Test case 1: Empty JSON payload
        response = client.post("/process-segment", json={})
        assert response.status_code == 400
        assert "Invalid payload" in response.get_data(as_text=True)

        # Test case 2: Invalid date format
        response = client.post("/process-segment", json={
            "start_time": "invalid-date",
            "end_time": "2023-01-01T04:00:00"
        })
        assert response.status_code == 400
        assert "Invalid date format" in response.get_json()["message"]


class TestLegacyEventSyncProcessor:
    """Tests for the LegacyEventSyncProcessor class logic."""

    @pytest.mark.unit
    def test_processor_initialization(self, mock_gcp_clients):
        """Test processor initialization with correct parameters."""
        from src.main import LegacyEventSyncProcessor

        processor = LegacyEventSyncProcessor(
            source_table="test-source",
            target_table="test-target",
            task_queue_name="test-queue",
            worker_url="http://test/worker"
        )

        assert processor.source_table == "test-source"
        assert processor.target_table == "test-target"
        assert processor.task_queue_name == "test-queue"
        assert processor.worker_url == "http://test/worker"

    @pytest.mark.unit
    def test_normalize_to_hour_boundary(self, mock_gcp_clients):
        """Test time normalization to hour boundary."""
        from src.main import LegacyEventSyncProcessor

        processor = LegacyEventSyncProcessor("source", "target")

        # 測試分鐘、秒、微秒都被歸零
        input_time = datetime(2023, 1, 1, 14, 30, 45, 123456)
        normalized = processor.normalize_to_hour_boundary(input_time)

        assert normalized == datetime(2023, 1, 1, 14, 0, 0, 0)

    @pytest.mark.unit
    def test_generate_time_segments(self, mock_gcp_clients):
        """Test time segment generation logic."""
        from src.main import LegacyEventSyncProcessor

        # Mock MINUTES_PER_SEGMENT 為 5 分鐘
        with patch("src.main.MINUTES_PER_SEGMENT", 5):
            processor = LegacyEventSyncProcessor("source", "target")

            start = datetime(2023, 1, 1, 10, 0, 0)
            end = datetime(2023, 1, 1, 11, 0, 0)  # 1 小時 = 12 個 5 分鐘段

            segments = processor.generate_time_segments(start, end)

            assert len(segments) == 12
            assert segments[0] == (datetime(2023, 1, 1, 10, 0), datetime(2023, 1, 1, 10, 5))
            assert segments[-1] == (datetime(2023, 1, 1, 10, 55), datetime(2023, 1, 1, 11, 0))

    @pytest.mark.unit
    def test_transform_event_data(self, mock_gcp_clients):
        """Test event data transformation logic."""
        from src.main import LegacyEventSyncProcessor

        processor = LegacyEventSyncProcessor("source", "target")

        # Mock BigQuery Row 物件
        mock_row = Mock()
        mock_row.get.side_effect = lambda key, default=None: {
            "permanent": "test_permanent",
            "ec_id": 123,
            "event_time": datetime(2023, 1, 1, 12, 0, 0),
            "link": "http://example.com",
            "event": {
                "name": "purchase",
                "value": 100.0,
                "currency": "USD",
                "custom_data": {"order_id": "order123"},
                "items": [{"id": "p1", "name": "product1", "price": 100.0, "quantity": 1}],
            },
            "user": {"em": "<EMAIL>", "ph": "1234567890"},
            "location": {
                "country_code": "TW",
                "region_name": "Taipei",
                "city_name": "Taipei",
            }
        }.get(key, default)

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 5, 0)

            result = processor.transform_event_data(mock_row)

            assert result["permanent"] == "test_permanent"
            assert result["ec_id"] == 123
            assert result["partner_source"] == "legacy-tagtoo-event"
            assert result["event"] == "purchase"
            assert result["value"] == 100.0
            assert result["currency"] == "USD"
            assert result["order_id"] == "order123"
            assert len(result["items"]) == 1
            assert result["user"]["em"] == "<EMAIL>"

    @pytest.mark.unit
    def test_coordinate_sync_logic(self, mock_gcp_clients):
        """Test coordinate_sync method logic without real Cloud Tasks."""
        from src.main import LegacyEventSyncProcessor

        # 完全 mock 掉 Cloud Tasks 相關操作
        with patch("src.main.MINUTES_PER_SEGMENT", 5), \
             patch("src.main.SCHEDULER_INTERVAL_MINUTES", 60):

            processor = LegacyEventSyncProcessor(
                source_table="source",
                target_table="target",
                task_queue_name="test-queue"
            )

            # Mock processor 的依賴方法
            processor.get_last_sync_time = MagicMock(return_value=None)
            processor.set_last_sync_time = MagicMock()

            # Mock tasks_client 完全避免真實連接
            mock_tasks_client = MagicMock()
            mock_tasks_client.queue_path.return_value = "projects/test/locations/test/queues/test"
            mock_tasks_client.create_task = MagicMock()
            processor._tasks_client = mock_tasks_client

            # 執行測試：處理 1 小時範圍 (12 個 5 分鐘段)
            start_time = datetime(2023, 1, 10, 8, 0, 0)
            end_time = datetime(2023, 1, 10, 9, 0, 0)

            result = processor.coordinate_sync(start_date=start_time, end_date=end_time)

            # 驗證結果
            assert result["status"] == "success"
            assert result["tasks_created"] == 12
            assert result["total_segments"] == 12

            # 驗證 Cloud Tasks 被正確呼叫
            assert mock_tasks_client.create_task.call_count == 12
            processor.set_last_sync_time.assert_called_once_with(end_time)

    def test_estimate_query_cost(self, mock_processor):
        """Test BigQuery cost estimation functionality"""
        # 建立一個真實的 processor 實例進行測試
        from src.main import LegacyEventSyncProcessor

        # 建立真實的 processor（將使用 mock 的 GCP clients）
        real_processor = LegacyEventSyncProcessor("source", "target")

        # Mock BigQuery client 和 job
        mock_job = Mock()
        mock_job.total_bytes_processed = 1024 * 1024 * 1024  # 1 GB
        mock_job.slot_millis = 5000

        # Mock bigquery client 的 query 方法
        mock_client = Mock()
        mock_client.query.return_value = mock_job
        real_processor._bigquery_client = mock_client

        query = "SELECT * FROM table"

        # 建立正確的 job_config Mock
        job_config = Mock()
        job_config.query_parameters = None  # 沒有查詢參數
        job_config.use_legacy_sql = None    # 沒有設定 legacy SQL

        # 執行真實的成本估算方法
        cost_estimate = real_processor.estimate_query_cost(query, job_config)

        # 驗證成本估算結果
        assert cost_estimate["bytes_processed"] == 1024 * 1024 * 1024
        assert cost_estimate["tb_processed"] == round((1024 * 1024 * 1024) / (1024**4), 6)  # 約 0.000001 TB
        assert cost_estimate["estimated_cost_usd"] > 0
        assert cost_estimate["query_slots"] == 5

        # 驗證 mock 被正確呼叫
        mock_client.query.assert_called_once()


class TestEventFiltering:
    """Tests for event filtering functionality."""

    @pytest.mark.unit
    def test_excluded_event_types_parsing(self, mock_gcp_clients):
        """Test parsing of excluded event types from environment variables."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': '["focus", "scroll"]'}):
            # Reload the module to pick up the new environment variable
            import importlib
            import src.main
            importlib.reload(src.main)

            assert src.main.EXCLUDED_EVENT_TYPES == ["focus", "scroll"]

    @pytest.mark.unit
    def test_excluded_event_types_invalid_json(self, mock_gcp_clients):
        """Test handling of invalid JSON in excluded event types."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': 'invalid-json'}):
            import importlib
            import src.main
            importlib.reload(src.main)

            # Should fall back to default
            assert src.main.EXCLUDED_EVENT_TYPES == ["focus"]

    @pytest.mark.unit
    def test_excluded_event_types_empty_list(self, mock_gcp_clients):
        """Test empty excluded event types list."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': '[]'}):
            import importlib
            import src.main
            importlib.reload(src.main)

            assert src.main.EXCLUDED_EVENT_TYPES == []

    @pytest.mark.unit
    def test_get_events_to_sync_with_filtering(self, mock_gcp_clients):
        """Test that get_events_to_sync includes event filtering in query."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': '["focus"]'}):
            import importlib
            import src.main
            importlib.reload(src.main)

            from src.main import LegacyEventSyncProcessor

            processor = LegacyEventSyncProcessor("source", "target")

            # 直接設置 mock BigQuery 客戶端，避免觸發真實連接
            mock_client = mock_gcp_clients["bigquery_client"]
            processor._bigquery_client = mock_client

            # Mock BigQuery query job
            mock_query_job = Mock()
            mock_query_job.job_id = "test-job-id"
            mock_result = Mock()
            mock_query_job.result.return_value = mock_result
            mock_client.query.return_value = mock_query_job

            # Mock cost estimation
            with patch.object(processor, 'estimate_query_cost') as mock_cost:
                mock_cost.return_value = {"estimated_cost_usd": 0.001}

                start_time = datetime(2023, 1, 1, 10, 0, 0)
                end_time = datetime(2023, 1, 1, 11, 0, 0)

                result = processor.get_events_to_sync(start_time, end_time)

                # Check that query was called
                assert mock_client.query.called

                # Get the actual query that was executed
                call_args = mock_client.query.call_args
                actual_query = call_args[0][0]

                # Verify that the query includes event filtering
                assert "AND event.name NOT IN ('focus')" in actual_query
                assert result == mock_result

    @pytest.mark.unit
    def test_get_source_table_count_with_filtering(self, mock_gcp_clients):
        """Test that source table count includes event filtering."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': '["focus", "scroll"]'}):
            import importlib
            import src.main
            importlib.reload(src.main)

            from src.main import LegacyEventSyncProcessor

            processor = LegacyEventSyncProcessor("source", "target")

            # 直接設置 mock BigQuery 客戶端，避免觸發真實連接
            mock_client = mock_gcp_clients["bigquery_client"]
            processor._bigquery_client = mock_client

            # Mock BigQuery result
            mock_row = Mock()
            mock_row.count = 1000
            mock_result = [mock_row]
            mock_client.query.return_value.result.return_value = mock_result

            # Mock cost estimation
            with patch.object(processor, 'estimate_query_cost') as mock_cost:
                mock_cost.return_value = {
                    "estimated_cost_usd": 0.001,
                    "tb_processed": 0.001,
                    "execution_time_seconds": 1.0
                }

                target_date = datetime(2023, 1, 1)
                result = processor.get_source_table_count_for_date(target_date)

                # Check that query was called
                assert mock_client.query.called

                # Get the actual query that was executed
                call_args = mock_client.query.call_args
                actual_query = call_args[0][0]

                # Verify that the query includes event filtering
                assert "AND event.name NOT IN ('focus', 'scroll')" in actual_query
                assert result["count"] == 1000

    @pytest.mark.unit
    def test_sync_status_includes_filtering_config(self, mock_gcp_clients):
        """Test that sync status includes filtering configuration."""
        with patch.dict('os.environ', {'EXCLUDED_EVENT_TYPES': '["focus"]'}):
            import importlib
            import src.main
            importlib.reload(src.main)

            from src.main import LegacyEventSyncProcessor

            processor = LegacyEventSyncProcessor("source", "target")

            # 直接設置 mock BigQuery 客戶端，避免觸發真實連接
            mock_client = mock_gcp_clients["bigquery_client"]
            processor._bigquery_client = mock_client

            # Mock all the required methods
            with patch.object(processor, 'get_last_sync_time') as mock_last_sync, \
                 patch.object(processor, 'get_source_table_count_for_date') as mock_source_count, \
                 patch.object(processor, 'get_target_table_count_for_date') as mock_target_count, \
                 patch.object(processor, 'get_latest_data_timestamp') as mock_latest_time:

                mock_last_sync.return_value = datetime(2023, 1, 1, 10, 0, 0)
                mock_source_count.return_value = {
                    "count": 1000,
                    "query_stats": {"estimated_cost_usd": 0.001, "tb_processed": 0.001, "execution_time_seconds": 1.0}
                }
                mock_target_count.return_value = {
                    "count": 950,
                    "query_stats": {"estimated_cost_usd": 0.001, "tb_processed": 0.001, "execution_time_seconds": 1.0}
                }
                mock_latest_time.return_value = datetime(2023, 1, 1, 9, 30, 0)

                target_date = datetime(2023, 1, 1)
                result = processor.get_sync_status_for_date(target_date)

                # Check that filtering config is included in response
                assert "filtering_config" in result
                filtering_config = result["filtering_config"]

                assert filtering_config["excluded_event_types"] == ["focus"]
                assert filtering_config["filtering_enabled"] == True
                assert "已過濾 1 種事件類型" in filtering_config["description"]


class TestBusinessLogic:
    """Tests for business logic and edge cases."""

    @pytest.mark.unit
    def test_sync_status_healthy_conditions(self, mock_gcp_clients):
        """Test sync status calculation for healthy conditions."""
        from src.main import LegacyEventSyncProcessor

        processor = LegacyEventSyncProcessor("source", "target")

        # Mock 所有依賴方法返回健康狀態
        processor.get_last_sync_time = MagicMock(
            return_value=datetime(2023, 1, 1, 11, 0, 0)
        )
        processor.get_source_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": 1024000,
                "estimated_cost_usd": 0.005,
                "execution_time_seconds": 1.5
            }
        })
        processor.get_target_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": 1024000,
                "estimated_cost_usd": 0.005,
                "execution_time_seconds": 1.5
            }
        })
        processor.get_latest_data_timestamp = MagicMock(
            return_value=datetime(2023, 1, 1, 11, 55, 0)
        )

        # Mock datetime.utcnow 確保時間計算正確
        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 0, 0)
            mock_datetime.strftime = datetime.strftime

            result = processor.get_sync_status_for_date(datetime(2023, 1, 1))

            # 驗證健康狀態的條件
            assert result["status"] == "healthy"
            assert result["source_events_count"] == 1000
            assert result["target_events_count"] == 1000
            assert result["sync_coverage_percent"] == 100.0
            assert result["warnings"] == []  # 健康狀態應該沒有警告

    @pytest.mark.unit
    def test_sync_status_warning_conditions(self, mock_gcp_clients):
        """Test sync status calculation for warning conditions."""
        from src.main import LegacyEventSyncProcessor

        processor = LegacyEventSyncProcessor("source", "target")

        # Mock 條件觸發警告狀態
        processor.get_last_sync_time = MagicMock(
            return_value=datetime(2023, 1, 1, 4, 0, 0)  # 8 小時前
        )
        processor.get_source_table_count_for_date = MagicMock(return_value={
            "count": 1000,
            "query_stats": {
                "bytes_processed": **********,  # 高成本
                "estimated_cost_usd": 0.6,  # 超過警告閾值
                "execution_time_seconds": 35.0  # 慢查詢
            }
        })
        processor.get_target_table_count_for_date = MagicMock(return_value={
            "count": 900,  # 只有 90% 同步
            "query_stats": {
                "bytes_processed": 512000000,
                "estimated_cost_usd": 0.3,
                "execution_time_seconds": 20.0
            }
        })
        processor.get_latest_data_timestamp = MagicMock(
            return_value=datetime(2023, 1, 1, 11, 55, 0)
        )

        with patch("src.main.datetime") as mock_datetime:
            mock_datetime.utcnow.return_value = datetime(2023, 1, 1, 12, 0, 0)
            mock_datetime.strftime = datetime.strftime

            result = processor.get_sync_status_for_date(datetime(2023, 1, 1))

            # 驗證警告狀態的條件
            assert result["status"] == "warning"
            assert result["sync_coverage_percent"] == 90.0

            # 應該有多個警告
            warnings = result["warnings"]
            assert len(warnings) >= 3
            assert any("coverage" in warning.lower() for warning in warnings)
            assert any("hours ago" in warning.lower() for warning in warnings)
            assert any("query cost" in warning.lower() for warning in warnings)

            # 檢查成本分析
            assert result["bigquery_analysis"]["total_cost_usd"] == 0.9


if __name__ == "__main__":
    pytest.main(["--color=yes", "-p", "no:warnings", __file__])
