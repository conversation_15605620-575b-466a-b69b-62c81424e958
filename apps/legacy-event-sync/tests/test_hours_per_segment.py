import os
import importlib
import sys
import pytest

MODULE_PATH = "src.main"

def reload_main_with_env(val):
    os.environ["MINUTES_PER_SEGMENT"] = val
    # 確保 src/ 在 sys.path
    import pathlib
    sys.path.insert(0, str(pathlib.Path(__file__).parent.parent / "src"))
    if MODULE_PATH in sys.modules:
        del sys.modules[MODULE_PATH]
    main = importlib.import_module(MODULE_PATH)
    return main.MINUTES_PER_SEGMENT

def test_minutes_per_segment_int():
    assert reload_main_with_env("3") == 3.0

def test_minutes_per_segment_float():
    assert reload_main_with_env("2.5") == 2.5
    assert reload_main_with_env("0.05") == 0.05
    assert reload_main_with_env("0.5") == 0.5

def test_minutes_per_segment_zero_or_negative():
    assert reload_main_with_env("0") == 5.0
    assert reload_main_with_env("-1") == 5.0

def test_minutes_per_segment_invalid():
    assert reload_main_with_env("abc") == 5.0
    assert reload_main_with_env("") == 5.0

def test_minutes_per_segment_default(monkeypatch):
    monkeypatch.delenv("MINUTES_PER_SEGMENT", raising=False)
    if MODULE_PATH in sys.modules:
        del sys.modules[MODULE_PATH]
    main = importlib.import_module(MODULE_PATH)
    assert main.MINUTES_PER_SEGMENT == 5.0
