# Legacy Event Sync Pre-commit Hooks
# 在 commit 前自動執行程式碼品質檢查和 Terraform 驗證

repos:
  # 通用 pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        exclude: ^apps/_template/
      - id: check-json
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-case-conflict

  # Python 程式碼格式化和檢查
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        files: ^(src/|tests/).+\.py$
        args: [--line-length=88]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^(src/|tests/).+\.py$
        args: [--profile=black]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        files: ^(src/|tests/).+\.py$
        args: [--max-line-length=88, --extend-ignore=E203, W503]

  # Terraform 相關檢查
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.83.2
    hooks:
      # Terraform 格式化
      - id: terraform_fmt
        files: ^terraform/.+\.tf$
        args:
          - --args=-recursive

      # Terraform 驗證（最重要！）
      - id: terraform_validate
        files: ^terraform/.+\.tf$
        args:
          - --hook-config=--retry-once-with-cleanup=true

      # Terraform 文檔生成
      - id: terraform_docs
        files: ^terraform/.+\.tf$
        args:
          - --hook-config=--path-to-file=README.md
          - --hook-config=--add-to-existing-file=true
          - --hook-config=--create-file-if-not-exist=true

      # Terraform 安全檢查（可選）
      - id: terraform_tfsec
        files: ^terraform/.+\.tf$
        args:
          - --args=--minimum-severity=MEDIUM

  # JSON/YAML 格式檢查
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: \.(json|yaml|yml|md)$
        exclude: ^(terraform/tfplan-.*|\.terraform/.*|.*\.tfstate.*|apps/_template/.*|.*\.template\..*|.*{{.*}}.*)

  # 自訂 Terraform 初始化檢查
  - repo: local
    hooks:
      - id: terraform-init-check
        name: Terraform Init Check
        entry: bash
        language: system
        files: ^terraform/.+\.tf$
        args:
          - -c
          - |
            echo "🔍 檢查 Terraform 初始化..."
            cd terraform
            if [ ! -d ".terraform" ]; then
              echo "⚠️  Terraform 未初始化，正在執行 terraform init..."
              terraform init -backend=false > /dev/null 2>&1 || {
                echo "❌ Terraform init 失敗！請檢查配置。"
                exit 1
              }
              echo "✅ Terraform 初始化完成"
            fi
            echo "✅ Terraform 初始化檢查通過"

      # 自訂環境變數檢查
      - id: env-vars-check
        name: Environment Variables Check
        entry: bash
        language: system
        files: ^(terraform/.*\.tf|\.env.*|docker-compose.*\.yml)$
        args:
          - -c
          - |
            echo "🔍 檢查環境變數配置..."
            if [ -f ".env" ] && grep -q "changeme\|example\|replace" .env; then
              echo "❌ 發現未替換的範例環境變數，請檢查 .env 檔案"
              exit 1
            fi
            echo "✅ 環境變數檢查通過"

# Pre-commit 配置
default_stages: [pre-commit, pre-push]
minimum_pre_commit_version: "3.0.0"

# CI 配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ""
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit autoupdate"
  autoupdate_schedule: weekly
  skip: []
  submodules: false
