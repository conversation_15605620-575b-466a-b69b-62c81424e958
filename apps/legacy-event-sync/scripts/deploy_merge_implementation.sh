#!/bin/bash

# MERGE 實作部署與驗證腳本（生產環境版）
# 🎯 目的：透過 GitHub Actions 部署新的 MERGE 實作並驗證其正確性

set -e

echo "🚀 準備透過 GitHub Actions 部署 MERGE 冪等性實作..."

# 1. 檢查 GCP 配置
echo "📋 檢查 GCP 配置..."
make check-gcloud-config

# 2. 執行本地測試
echo "🧪 執行本地測試..."
cd /Users/<USER>/tagtoo/integrated-event/apps/legacy-event-sync
python -m pytest tests/test_merge_idempotency.py -v || echo "⚠️  本地測試失敗，請檢查實作"

# 3. 檢查 Git 狀態
echo "� 檢查 Git 狀態..."
git status

echo ""
echo "🔧 建議的部署流程："
echo ""
echo "1. 提交變更到 Git："
echo "   git add ."
echo "   git commit -m 'feat: 實作 MERGE 冪等性邏輯以防止資料重複'"
echo ""
echo "2. 推送到 GitHub 觸發 CI/CD："
echo "   git push origin main"
echo ""
echo "3. 監控 GitHub Actions 部署："
echo "   - 前往 GitHub repository"
echo "   - 查看 Actions 標籤頁"
echo "   - 確認 legacy-event-sync 服務部署成功"
echo ""
echo "4. 部署後驗證："
echo "   make logs              # 查看部署後日誌"
echo "   make status            # 檢查服務狀態"
echo ""
echo "🎯 建議的 GitHub Actions 工作流程："
echo "   1. 自動檢測 apps/legacy-event-sync/ 的變更"
echo "   2. 建置新的 Docker 映像"
echo "   3. 部署到 Cloud Run"
echo "   4. 執行健康檢查"
echo ""
echo "� 重要監控點："
echo "   1. 查看日誌中的 'MERGE operation completed' 訊息"
echo "   2. 確認 'rows inserted' 數量合理"
echo "   3. 監控整體重複率變化"
echo "   4. 確認沒有新的錯誤產生"
echo ""
echo "📈 預期結果："
echo "   - 重複率應維持在 1.07% 左右"
echo "   - 不應該再產生新的重複記錄"
echo "   - 同步效能不應該顯著下降"
echo "   - 服務應該保持穩定運行"
