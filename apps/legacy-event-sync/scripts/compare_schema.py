#!/usr/bin/env python3
"""
BigQuery Schema Comparison Tool
比對本地 schema 文件與實際 BigQuery table schema
"""

import json
import sys
from pathlib import Path


def load_json_schema(file_path):
    """載入 JSON schema 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading {file_path}: {e}")
        return None


def compare_schemas(local_schema, remote_schema, table_name):
    """比對兩個 schema"""
    print(f"   🔍 Detailed field comparison for {table_name}:")

    # 建立欄位字典
    local_fields = {f['name']: f for f in local_schema}
    remote_fields = {f['name']: f for f in remote_schema}

    # 收集所有欄位名稱
    all_fields = sorted(set(local_fields.keys()) | set(remote_fields.keys()))

    matches = 0
    mismatches = 0

    for name in all_fields:
        if name not in local_fields:
            print(f"      ❌ Missing in local: {name}")
            mismatches += 1
        elif name not in remote_fields:
            print(f"      ❌ Missing in BigQuery: {name}")
            mismatches += 1
        else:
            local_field = local_fields[name]
            remote_field = remote_fields[name]

            # 比對類型
            local_type = local_field.get('type')
            remote_type = remote_field.get('type')

            # 比對模式 (預設為 NULLABLE)
            local_mode = local_field.get('mode', 'NULLABLE')
            remote_mode = remote_field.get('mode', 'NULLABLE')

            if local_type != remote_type:
                print(f"      ⚠️  Type mismatch {name}: local={local_type} vs BigQuery={remote_type}")
                mismatches += 1
            elif local_mode != remote_mode:
                print(f"      ⚠️  Mode mismatch {name}: local={local_mode} vs BigQuery={remote_mode}")
                mismatches += 1
            else:
                print(f"      ✅ {name}: {local_type} ({local_mode})")
                matches += 1

    print(f"   📊 Summary: {matches} matches, {mismatches} mismatches")
    return mismatches == 0


def main():
    if len(sys.argv) != 3:
        print("Usage: python3 compare_schema.py <local_schema.json> <remote_schema.json>")
        sys.exit(1)

    local_file = sys.argv[1]
    remote_file = sys.argv[2]

    # 載入 schema
    local_schema = load_json_schema(local_file)
    remote_schema = load_json_schema(remote_file)

    if local_schema is None or remote_schema is None:
        sys.exit(1)

    # 提取表格名稱
    table_name = Path(local_file).stem

    # 比對 schema
    is_match = compare_schemas(local_schema, remote_schema, table_name)

    if is_match:
        print(f"   🎉 All fields match perfectly!")
    else:
        print(f"   ⚠️  Schema differences found!")

    sys.exit(0 if is_match else 1)


if __name__ == "__main__":
    main()
