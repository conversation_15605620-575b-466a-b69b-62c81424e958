#!/usr/bin/env python3
"""
BigQuery Streaming Buffer 狀況檢查腳本

🎯 目的：
1. 檢查 tagtoo_event 表格的當前真實 Streaming Buffer 狀況
2. 提供基於真實 Streaming Buffer 延遲的同步時間偏移建議
3. 監控 Streaming Buffer 的健康狀態

🔍 重要說明：
- 只能檢查當前時刻的 Streaming Buffer 延遲
- 無法取得歷史 Streaming Buffer 延遲資料
- BigQuery API 只提供當前 oldest_entry_time 資訊

用法：
    # 檢查當前 Streaming Buffer 狀況
    python scripts/check_streaming_buffer.py

    # 獲得基於真實 Streaming Buffer 的同步偏移建議
    python scripts/check_streaming_buffer.py --recommend-offset
"""

import os
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from google.cloud import bigquery
import pytz


class StreamingBufferMonitor:
    """BigQuery Streaming Buffer 監控器"""

    def __init__(self):
        self.bigquery_client = bigquery.Client(location="asia-east1")
        self.source_table = "tagtoo-tracking.event_prod.tagtoo_event"
        self.taipei_tz = pytz.timezone("Asia/Taipei")

    def get_streaming_buffer_status(self) -> Dict[str, Any]:
        """獲取當前真正的 BigQuery Streaming Buffer 狀況"""
        try:
            table = self.bigquery_client.get_table(self.source_table)

            if table.streaming_buffer is None:
                return {
                    "has_streaming_buffer": False,
                    "message": "表格沒有 Streaming Buffer 資料（所有資料已完全寫入）"
                }

            buffer = table.streaming_buffer
            current_time = datetime.now(pytz.utc)

            # 計算真正的 Streaming Buffer 延遲時間
            oldest_entry = buffer.oldest_entry_time
            if oldest_entry:
                # 這是真正的 Streaming Buffer 延遲
                real_streaming_delay_seconds = (current_time - oldest_entry).total_seconds()
                real_streaming_delay_minutes = real_streaming_delay_seconds / 60
            else:
                real_streaming_delay_seconds = real_streaming_delay_minutes = 0

            return {
                "has_streaming_buffer": True,
                "estimated_bytes": buffer.estimated_bytes,
                "estimated_rows": buffer.estimated_rows,
                "oldest_entry_time_utc": oldest_entry,
                "oldest_entry_time_taipei": oldest_entry.astimezone(self.taipei_tz) if oldest_entry else None,
                "current_time_utc": current_time,
                "current_time_taipei": current_time.astimezone(self.taipei_tz),
                "real_streaming_delay_seconds": real_streaming_delay_seconds,
                "real_streaming_delay_minutes": real_streaming_delay_minutes,
                "estimated_size_mb": buffer.estimated_bytes / (1024 * 1024) if buffer.estimated_bytes else 0,
            }

        except Exception as e:
            return {
                "has_streaming_buffer": False,
                "error": str(e)
            }

    def recommend_sync_offset(self) -> Dict[str, Any]:
        """基於真正的 Streaming Buffer 延遲建議同步偏移時間"""
        current_status = self.get_streaming_buffer_status()

        if not current_status.get("has_streaming_buffer"):
            return {
                "recommended_offset_minutes": 30,
                "reason": "沒有 Streaming Buffer 資料，建議保守的 30 分鐘偏移",
                "confidence": "low",
                "current_streaming_delay": 0
            }

        # 當前真正的 Streaming Buffer 延遲
        current_streaming_delay_minutes = current_status.get("real_streaming_delay_minutes", 0)

        # 基於真正的 Streaming Buffer 延遲計算建議偏移
        # 建議偏移 = 當前 Streaming Buffer 延遲 × 2 (安全係數) + 15 分鐘 (處理緩衝)
        # 最少 30 分鐘，最多 120 分鐘
        safety_factor = 2.0
        processing_buffer = 15  # 分鐘

        recommended_offset = max(30, min(120,
            current_streaming_delay_minutes * safety_factor + processing_buffer))

        # 決定信心等級
        if current_streaming_delay_minutes > 0:
            confidence = "high"
            reason_parts = [
                f"基於當前真實 Streaming Buffer 延遲 {current_streaming_delay_minutes:.1f} 分鐘",
                f"套用 {safety_factor}x 安全係數 + {processing_buffer} 分鐘處理緩衝"
            ]
        else:
            confidence = "medium"
            reason_parts = [
                "當前沒有 Streaming Buffer 延遲",
                "建議保守的最小偏移時間"
            ]

        return {
            "recommended_offset_minutes": int(recommended_offset),
            "current_streaming_delay_minutes": current_streaming_delay_minutes,
            "reason": "；".join(reason_parts),
            "confidence": confidence,
            "current_setting_minutes": 60,   # 1 小時 = 60 分鐘
            "potential_improvement": self._calculate_improvement(60, int(recommended_offset))
        }

    def _calculate_improvement(self, current_offset: int, recommended_offset: int) -> str:
        """計算潛在改進效果"""
        if recommended_offset < current_offset:
            saved_minutes = current_offset - recommended_offset
            return f"可將同步延遲從 {current_offset} 分鐘縮短至 {recommended_offset} 分鐘，減少 {saved_minutes} 分鐘延遲"
        elif recommended_offset > current_offset:
            added_minutes = recommended_offset - current_offset
            return f"建議增加同步延遲至 {recommended_offset} 分鐘，增加 {added_minutes} 分鐘安全緩衝"
        else:
            return "建議維持當前設定"

    def print_current_status(self):
        """印出當前真正的 Streaming Buffer 狀況"""
        print("🔍 BigQuery Streaming Buffer 狀況檢查")
        print("=" * 50)

        status = self.get_streaming_buffer_status()

        if not status.get("has_streaming_buffer"):
            if "error" in status:
                print(f"❌ 錯誤: {status['error']}")
            else:
                print(f"✅ {status['message']}")
                print("📊 所有事件都已完全寫入 BigQuery，沒有延遲")
            return

        print(f"📊 Streaming Buffer 資料量:")
        print(f"   - 預估大小: {status['estimated_size_mb']:.2f} MB")
        print(f"   - 預估行數: {status['estimated_rows']:,} 筆")

        print(f"\n⏰ 時間資訊:")
        print(f"   - 當前時間 (台北): {status['current_time_taipei'].strftime('%Y-%m-%d %H:%M:%S')}")
        if status['oldest_entry_time_taipei']:
            print(f"   - Streaming Buffer 最早資料 (台北): {status['oldest_entry_time_taipei'].strftime('%Y-%m-%d %H:%M:%S')}")

        print(f"\n🕐 真正的 Streaming Buffer 延遲分析:")
        print(f"   - Streaming Buffer 延遲: {status['real_streaming_delay_minutes']:.1f} 分鐘")
        print(f"   - 延遲秒數: {status['real_streaming_delay_seconds']:.0f} 秒")

        # 延遲等級評估（基於真正的 Streaming Buffer 延遲）
        delay_mins = status['real_streaming_delay_minutes']
        if delay_mins <= 15:
            level = "🟢 極佳"
            suggestion = "Streaming Buffer 延遲很低，可使用較短的同步偏移"
        elif delay_mins <= 30:
            level = "🟡 良好"
            suggestion = "Streaming Buffer 延遲正常，建議 60-90 分鐘同步偏移"
        elif delay_mins <= 60:
            level = "🟠 需注意"
            suggestion = "Streaming Buffer 延遲偏高，建議 120 分鐘以上同步偏移"
        else:
            level = "🔴 異常"
            suggestion = "Streaming Buffer 延遲異常，需要調查原因"

        print(f"   - 延遲等級: {level}")
        print(f"   - 建議: {suggestion}")

        print(f"\n💡 重要說明:")
        print(f"   - 這是真正的 BigQuery Streaming Buffer 延遲")
        print(f"   - 不包含我們的同步處理時間偏移")
        print(f"   - 我們的總處理延遲 = Streaming Buffer 延遲 + 同步偏移時間")

    def print_recommendation(self):
        """印出基於真正 Streaming Buffer 延遲的同步偏移建議"""
        print(f"\n🎯 智能同步偏移建議（基於真實 Streaming Buffer 延遲）")
        print("=" * 50)

        recommendation = self.recommend_sync_offset()

        print(f"📋 當前 Streaming Buffer 狀況:")
        print(f"   - 真正 Streaming Buffer 延遲: {recommendation['current_streaming_delay_minutes']:.1f} 分鐘")
        print(f"   - 目前同步偏移設定: {recommendation['current_setting_minutes']} 分鐘 (1 小時)")

        print(f"\n💡 建議配置:")
        print(f"   - 建議同步偏移: {recommendation['recommended_offset_minutes']} 分鐘")
        print(f"   - 信心等級: {recommendation['confidence']}")
        print(f"   - 計算依據: {recommendation['reason']}")

        print(f"\n📈 預期效果:")
        print(f"   - {recommendation['potential_improvement']}")

        # 提供環境變數配置建議
        current_hours = recommendation['current_setting_minutes'] / 60
        recommended_hours = recommendation['recommended_offset_minutes'] / 60

        if abs(recommended_hours - current_hours) > 0.25:  # 超過 15 分鐘差異才建議調整
            print(f"\n🔧 配置建議:")
            print(f"   在 Terraform 變數中設定:")
            print(f"   streaming_buffer_offset_hours = {recommended_hours:.1f}")
            print(f"   或環境變數:")
            print(f"   STREAMING_BUFFER_OFFSET_HOURS={recommended_hours:.1f}")
        else:
            print(f"\n✅ 當前設定已經很合適，無需調整")


def main():
    parser = argparse.ArgumentParser(description="檢查真正的 BigQuery Streaming Buffer 狀況")
    parser.add_argument('--recommend-offset', action='store_true',
                       help='基於真正的 Streaming Buffer 延遲建議最佳同步偏移時間')

    args = parser.parse_args()

    monitor = StreamingBufferMonitor()

    # 總是顯示當前真正的 Streaming Buffer 狀況
    monitor.print_current_status()

    if args.recommend_offset:
        monitor.print_recommendation()

    # 如果沒有指定其他選項，提供簡單建議
    if not args.recommend_offset:
        print(f"\n💡 快速建議:")
        print(f"   使用 --recommend-offset 獲得基於真實 Streaming Buffer 的智能建議")


if __name__ == "__main__":
    main()
