#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legacy Event Sync - 手動同步腳本 (重構版)

此腳本直接使用 src/main.py 中的 LegacyEventSyncProcessor 類別，
確保資料轉換、MERGE 寫入、錯誤處理邏輯與正式服務完全一致。

功能特色：
- 🔄 直接共用正式同步邏輯，確保資料一致性
- 🧪 支援 dry-run 模式，僅查詢不寫入
- 💰 BigQuery 費用估算功能
- 📊 即時顯示同步進度與批次處理狀況
- ⚙️  靈活的 CLI 參數設定

使用範例：
  # 一般同步
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 23:59:59"

  # 測試模式（不寫入）
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --dry_run

  # 費用估算
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --estimate_cost
"""

import argparse
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any

import pytz
from google.cloud import bigquery

# --- 本地模組匯入 ---
# 將 src 目錄加入 Python 路徑，以便直接匯入 main.py 的類別
current_dir = os.path.dirname(__file__)
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from main import LegacyEventSyncProcessor
    # 從環境變數或預設值取得設定
    BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "5000"))
    PROJECT_ID = os.environ.get("PROJECT_ID", "tagtoo-tracking")
    # 安全起見：優先從環境變數讀取目標表格，若無則使用測試表格作為預設值
    TARGET_TABLE_DEFAULT = os.environ.get("TARGET_TABLE", f"{PROJECT_ID}.event_test.integrated_event")
    SOURCE_TABLE_DEFAULT = os.environ.get("SOURCE_TABLE", f"{PROJECT_ID}.event_prod.tagtoo_event")
except ImportError as e:
    print(f"❌ 無法匯入 LegacyEventSyncProcessor: {e}")
    print("請確認此腳本位於 scripts/ 目錄下，且 src/main.py 存在")
    print(f"當前查找路徑: {src_dir}")
    sys.exit(1)

# --- 日誌設定 ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)


class ManualSyncRunner:
    """手動同步執行器，負責協調整個同步過程"""

    def __init__(self,
                 project_id: str,
                 source_table: str,
                 target_table: str,
                 batch_size: int = BATCH_SIZE,
                 enable_deduplication: bool = False,
                 auto_patch_legacy: bool = True,
                 split_interval_minutes: int = 60):
        """
        初始化手動同步執行器

        Args:
            project_id: GCP 專案 ID
            source_table: 來源表格完整路徑 (如: project.dataset.table)
            target_table: 目標表格完整路徑
            batch_size: 每批次處理的事件數量
            enable_deduplication: 是否啟用去重複邏輯 (預設: False)
            auto_patch_legacy: 是否自動修正早於 2025-07-22 的來源/目標表格 (預設: True)
        """
        self.project_id = project_id
        self.batch_size = batch_size
        self.display_timezone = pytz.timezone("Asia/Taipei")
        self.source_table = source_table
        self.target_table = target_table
        self.auto_patch_legacy = auto_patch_legacy
        self.enable_deduplication = enable_deduplication
        self.split_interval_minutes = split_interval_minutes
        self.processor = LegacyEventSyncProcessor(
            source_table=self.source_table,
            target_table=self.target_table,
            task_queue_name=None,
            worker_url=None,
            enable_deduplication=enable_deduplication
        )
        logger.info(f"🚀 初始化手動同步執行器")
        logger.info(f"   來源表格: {self.source_table}")
        logger.info(f"   目標表格: {self.target_table}")
        logger.info(f"   批次大小: {batch_size:,} 筆")
        logger.info(f"   去重複邏輯: {'啟用' if enable_deduplication else '停用'}")
        logger.info(f"   切分區間: {split_interval_minutes} 分鐘")

    def parse_time_input(self, time_str: str) -> datetime:
        """
        解析時間輸入字串，轉換為 UTC 時間

        Args:
            time_str: 時間字串，格式為 'YYYY-MM-DD HH:MM:SS' (Asia/Taipei 時區)

        Returns:
            datetime: UTC 時間

        Raises:
            ValueError: 當時間格式不正確時
        """
        try:
            # 👉 解析為 Asia/Taipei 時區
            local_time = self.display_timezone.localize(
                datetime.fromisoformat(time_str)
            )

            # 👉 轉換為 UTC 時間
            utc_time = local_time.astimezone(pytz.utc)

            logger.debug(f"時間轉換: {time_str} (Asia/Taipei) -> {utc_time} (UTC)")
            return utc_time

        except ValueError as e:
            logger.error(f"❌ 時間格式錯誤: {time_str}")
            logger.error("請使用格式: 'YYYY-MM-DD HH:MM:SS' (Asia/Taipei 時區)")
            raise ValueError(f"時間格式錯誤: {e}")

    def _auto_patch_legacy_tables(self, start_utc: datetime, end_utc: datetime):
        """
        自動修正 legacy event 的來源/目標表格
        若同步區間早於 2025-07-22 00:00:00（台灣時間），自動 patch 表格名稱
        """
        if self.auto_patch_legacy:
            tw_cutoff = self.display_timezone.localize(datetime(2025, 7, 22, 0, 0, 0))
            utc_cutoff = tw_cutoff.astimezone(pytz.utc)
            # 若結束時間早於 cutoff，或開始時間早於 cutoff
            if end_utc <= utc_cutoff or start_utc < utc_cutoff:
                # 預設 legacy event 的來源表
                legacy_source = f"{self.project_id}.event_prod.tagtoo_event"
                legacy_target = f"{self.project_id}.event_prod.integrated_event"
                logger.info("🕰️  偵測到同步區間早於 2025-07-22，將自動切換 legacy event 表格:")
                logger.info(f"   來源表格: {legacy_source}")
                logger.info(f"   目標表格: {legacy_target}")
                self.source_table = legacy_source
                self.target_table = legacy_target
                # 重新建立 processor
                self.processor = LegacyEventSyncProcessor(
                    source_table=self.source_table,
                    target_table=self.target_table,
                    task_queue_name=None,
                    worker_url=None,
                    enable_deduplication=self.enable_deduplication
                )

    def estimate_sync_cost(self, start_utc: datetime, end_utc: datetime) -> Dict[str, Any]:
        """
        估算 BigQuery 查詢費用

        Args:
            start_utc: 開始時間 (UTC)
            end_utc: 結束時間 (UTC)

        Returns:
            費用估算資訊字典
        """
        logger.info("💰 正在估算 BigQuery 查詢費用...")

        # 📊 建立查詢設定
        query = f"""
            SELECT *
            FROM `{self.source_table}`
            WHERE event_time >= @start_time AND event_time < @end_time
            ORDER BY event_time
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("start_time", "TIMESTAMP", start_utc),
                bigquery.ScalarQueryParameter("end_time", "TIMESTAMP", end_utc),
            ]
        )

        # 🧮 呼叫處理器的費用估算功能
        cost_info = self.processor.estimate_query_cost(query, job_config)

        if cost_info.get("error"):
            logger.warning(f"⚠️  費用估算失敗: {cost_info['error']}")
        else:
            logger.info(f"📈 預計處理資料量: {cost_info['tb_processed']:.6f} TB")
            logger.info(f"💵 預估費用: ${cost_info['estimated_cost_usd']:.4f} USD")

        return cost_info

    def run_sync(
        self,
        start_time_str: str,
        end_time_str: str,
        dry_run: bool = False,
        estimate_cost: bool = False,
        max_workers: int = 4
    ) -> Dict[str, Any]:
        """
        執行手動同步作業

        Args:
            start_time_str: 開始時間字串 (Asia/Taipei)
            end_time_str: 結束時間字串 (Asia/Taipei)
            dry_run: 是否只測試不實際寫入
            estimate_cost: 是否顯示費用估算

        Returns:
            同步結果摘要
        """
        # 🕐 解析時間參數
        try:
            start_utc = self.parse_time_input(start_time_str)
            end_utc = self.parse_time_input(end_time_str)
        except ValueError:
            return {"status": "error", "message": "時間格式錯誤"}

        # 🔧 自動修正 legacy 表格 (如果需要)
        self._auto_patch_legacy_tables(start_utc, end_utc)

        # ⏱️ 顯示同步資訊
        start_local = start_utc.astimezone(self.display_timezone)
        end_local = end_utc.astimezone(self.display_timezone)

        mode_desc = "🧪 測試模式 (不寫入)" if dry_run else "🔄 正式同步"
        logger.info(f"{mode_desc} - 時間範圍 (Asia/Taipei):")
        logger.info(f"   開始: {start_local.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"   結束: {end_local.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"   來源表格: {self.source_table}")
        logger.info(f"   目標表格: {self.target_table}")

        # 💰 費用估算 (如果需要)
        cost_info = None
        if estimate_cost:
            cost_info = self.estimate_sync_cost(start_utc, end_utc)

        # 分段切分
        segs = []
        seg = start_utc
        delta = timedelta(minutes=self.split_interval_minutes)
        while seg < end_utc:
            next_seg = min(seg + delta, end_utc)
            segs.append((seg, next_seg))
            seg = next_seg

        try:
            # 🔄 統一的同步邏輯：透過 dry_run 參數控制行為
            import concurrent.futures
            mode_desc = "🧪 Dry-run 模式" if dry_run else "🚀 正式同步模式"
            logger.info(f"{mode_desc}：分段平行處理，每段 {self.split_interval_minutes} 分鐘，max_workers={max_workers}")
            logger.info(f"📋 將處理 {len(segs)} 個時間段")

            results = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_seg = {executor.submit(self._sync_segment_unified, seg_pair, dry_run): seg_pair for seg_pair in segs}
                for future in concurrent.futures.as_completed(future_to_seg):
                    res = future.result()
                    results.append(res)
            # 📊 匯總所有分段的結果
            logger.info("📊 正在匯總各分段結果...")
            total_events = sum(r.get("total_events", 0) for r in results)
            synced_events = sum(r.get("synced_events", 0) for r in results)
            error_events = sum(r.get("error_events", 0) for r in results)
            batches_processed = sum(r.get("batches_processed", 0) for r in results)

            # 累加所有分段的費用估算
            total_cost_usd = 0.0
            total_tb_processed = 0.0
            for r in results:
                ce = r.get("cost_estimate")
                if ce and not ce.get("error"):
                    total_cost_usd += ce.get("estimated_cost_usd", 0.0)
                    total_tb_processed += ce.get("tb_processed", 0.0)

            # 決定狀態
            if dry_run:
                status = "dry_run_success" if error_events == 0 else "partial_success"
            else:
                status = ("success" if error_events == 0 else ("partial_success" if error_events < total_events else "error"))

            result = {
                "status": status,
                "total_events": total_events,
                "synced_events": synced_events,
                "error_events": error_events,
                "batches_processed": batches_processed,
            }

            # 加入費用資訊
            if total_cost_usd > 0 or total_tb_processed > 0:
                result["cost_estimate"] = {
                    "estimated_cost_usd": total_cost_usd,
                    "tb_processed": total_tb_processed
                }
            elif cost_info:
                result["cost_estimate"] = cost_info

            # ✅ 顯示最終摘要
            self._display_sync_summary(result, dry_run)
            return result

        except Exception as e:
            logger.error(f"❌ 同步過程發生錯誤: {e}", exc_info=True)
            return {
                "status": "error",
                "message": str(e),
                "cost_estimate": cost_info
            }

    def _sync_segment_unified(self, seg_pair, dry_run: bool = False):
        """統一的分段處理方法，支援 dry_run 和實際同步"""
        seg, next_seg = seg_pair
        segment_id = f"{seg.strftime('%H:%M')}-{next_seg.strftime('%H:%M')}"
        mode_desc = "[Dry-run]" if dry_run else "[Sync]"

        try:
            logger.info(f"🔎 {mode_desc} Worker 處理區間 {segment_id}: {seg.strftime('%Y-%m-%d %H:%M:%S')} ~ {next_seg.strftime('%Y-%m-%d %H:%M:%S')}")

            # 使用統一的 sync_time_segment 方法，透過 dry_run 參數控制行為
            result = self.processor.sync_time_segment(seg, next_seg, dry_run=dry_run)

            # 標準化結果格式
            total_events = result.get("total_events", result.get("synced_count", 0) + result.get("error_count", 0))
            synced_events = result.get("synced_count", 0)
            error_events = result.get("error_count", 0)
            batches_processed = result.get("batches_processed", 1)

            logger.info(f"✅ {mode_desc} Worker 區間 {segment_id} 完成: {total_events:,} 筆事件")

            return {
                "status": "success" if error_events == 0 else "partial_success",
                "total_events": total_events,
                "synced_events": synced_events,
                "error_events": error_events,
                "batches_processed": batches_processed,
                "segment_id": segment_id
            }
        except Exception as e:
            logger.error(f"❌ {mode_desc} Worker 區間 {segment_id} 失敗: {e}")
            return {
                "status": "error",
                "total_events": 0,
                "synced_events": 0,
                "error_events": 0,
                "batches_processed": 0,
                "segment_id": segment_id
            }

    def _run_dry_sync(self, start_utc: datetime, end_utc: datetime) -> Dict[str, Any]:
        """
        執行 dry-run 同步：只查詢和轉換資料，不寫入 BigQuery，並自動以自訂分鐘數分段查詢，避免回傳過大。
        支援每分段平行查詢與費用累積。
        """
        import concurrent.futures

        logger.info(f"🧪 開始 Dry-run 模式 - 僅查詢資料，不寫入 (自動每 {self.split_interval_minutes} 分鐘分段，支援平行查詢)")

        total_events = 0
        batch_count = 0
        sample_events = []
        error_events = 0
        total_cost_usd = 0.0
        total_tb_processed = 0.0
        segs = []
        seg = start_utc
        delta = timedelta(minutes=self.split_interval_minutes)
        while seg < end_utc:
            next_seg = min(seg + delta, end_utc)
            segs.append((seg, next_seg))
            seg = next_seg

        logger.info(f"📋 將處理 {len(segs)} 個時間段，每段 {self.split_interval_minutes} 分鐘")

        def process_segment(seg_pair):
            seg, next_seg = seg_pair
            local_total = 0
            local_sample = []
            local_cost = 0.0
            local_tb = 0.0
            segment_id = f"{seg.strftime('%H:%M')}-{next_seg.strftime('%H:%M')}"

            try:
                logger.info(f"🔎 [Worker] 處理區間 {segment_id}: {seg.strftime('%Y-%m-%d %H:%M:%S')} ~ {next_seg.strftime('%Y-%m-%d %H:%M:%S')}")

                # 費用估算
                cost_info = self.estimate_sync_cost(seg, next_seg)
                if cost_info and not cost_info.get("error"):
                    local_cost = cost_info["estimated_cost_usd"]
                    local_tb = cost_info["tb_processed"]

                # 查詢資料
                events_iterator = self.processor.get_events_to_sync(seg, next_seg)
                for event in events_iterator:
                    local_total += 1
                    if len(local_sample) < 3:
                        transformed = self.processor.transform_event_data(event)
                        local_sample.append({
                            "permanent": transformed.get("permanent"),
                            "event": transformed.get("event"),
                            "event_time": transformed.get("event_time"),
                            "link": transformed.get("link")[:80] + "..." if transformed.get("link") and len(transformed.get("link", "")) > 80 else transformed.get("link")
                        })

                logger.info(f"✅ [Worker] 區間 {segment_id} 完成: {local_total:,} 筆事件, ${local_cost:.4f} USD")

                return {
                    "segment_id": segment_id,
                    "total": local_total,
                    "batch": 1 if local_total > 0 else 0,
                    "sample": local_sample,
                    "error": 0,
                    "cost": local_cost,
                    "tb": local_tb
                }
            except Exception as e:
                logger.error(f"❌ [Worker] 區間 {segment_id} 查詢失敗: {e}")
                return {
                    "segment_id": segment_id,
                    "total": 0,
                    "batch": 0,
                    "sample": [],
                    "error": 1,
                    "cost": 0.0,
                    "tb": 0.0
                }

        # 平行處理所有分段
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(process_segment, seg_pair) for seg_pair in segs]
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())

        # 匯總結果
        logger.info("📊 正在匯總各分段結果...")
        for r in results:
            total_events += r["total"]
            batch_count += r["batch"]
            error_events += r["error"]
            total_cost_usd += r["cost"]
            total_tb_processed += r["tb"]
            for s in r["sample"]:
                if len(sample_events) < 3:
                    sample_events.append(s)

        if sample_events:
            logger.info("📊 樣本事件資料:")
            for i, sample in enumerate(sample_events, 1):
                logger.info(f"   [{i}] {sample['event']} | {sample['permanent']} | {sample['event_time']}")
                if sample['link']:
                    logger.info(f"       Link: {sample['link']}")

        # 注意：費用資訊移到最終摘要中統一顯示

        return {
            "status": "dry_run_success" if error_events == 0 else "partial_success",
            "total_events": total_events,
            "synced_events": 0,
            "error_events": error_events,
            "batches_processed": batch_count,
            "sample_events": sample_events,
            "cost_estimate": {
                "estimated_cost_usd": total_cost_usd,
                "tb_processed": total_tb_processed
            }
        }

    def _display_sync_summary(self, result: Dict[str, Any], dry_run: bool):
        """
        顯示同步結果摘要

        Args:
            result: 同步結果字典
            dry_run: 是否為測試模式
        """
        status = result.get("status", "unknown")
        total_events = result.get("total_events", 0)
        synced_events = result.get("synced_events", 0)
        error_events = result.get("error_events", 0)
        batches = result.get("batches_processed", 0)

        logger.info("=" * 60)

        if dry_run:
            logger.info("🧪 Dry-run 模式完成")
            logger.info(f"📊 查詢到事件總數: {total_events:,} 筆")
            logger.info(f"📦 批次數量: {batches:,} 個")
            logger.info("💡 如需實際同步，請移除 --dry_run 參數")
        else:
            logger.info("🔄 手動同步完成")
            logger.info(f"📊 處理事件總數: {total_events:,} 筆")
            logger.info(f"✅ 成功同步: {synced_events:,} 筆")
            if error_events > 0:
                logger.info(f"❌ 錯誤事件: {error_events:,} 筆")
            logger.info(f"📦 批次數量: {batches:,} 個")

            # 🎯 狀態說明
            if status == "success":
                logger.info("🎉 同步狀態: 完全成功")
            elif status == "partial_success":
                logger.info("⚠️  同步狀態: 部分成功 (有錯誤事件)")
            elif status == "error":
                logger.info("❌ 同步狀態: 失敗")

        # 💰 費用資訊
        cost_estimate = result.get("cost_estimate")
        if cost_estimate and not cost_estimate.get("error"):
            logger.info(f"💰 BigQuery 費用: ${cost_estimate['estimated_cost_usd']:.4f} USD ({cost_estimate['tb_processed']:.6f} TB)")

        logger.info("=" * 60)


def main():
    """主程式入口點"""

    parser = argparse.ArgumentParser(
        description="Legacy Event Sync - 手動同步腳本 (重構版)",
        epilog="""
範例用法:
  # 一般同步
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 23:59:59"

  # 測試模式 (不寫入)
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --dry_run

  # 費用估算
  python manual_sync.py --start "2024-01-01 00:00:00" --end "2024-01-01 01:00:00" --estimate_cost

注意: 所有時間參數均以 Asia/Taipei (UTC+8) 時區為準。
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        '--split-interval-minutes',
        type=int,
        default=60,
        help="每次查詢的切分區間（分鐘），預設 60 (每小時分段)。建議 15~60 視資料量調整"
    )

    parser.add_argument(
        '--start',
        required=True,
        help="開始時間 (Asia/Taipei)，格式: 'YYYY-MM-DD HH:MM:SS'"
    )
    parser.add_argument(
        '--end',
        required=True,
        help="結束時間 (Asia/Taipei)，格式: 'YYYY-MM-DD HH:MM:SS'"
    )
    parser.add_argument(
        '--batch_size',
        type=int,
        default=5000,  # 使用 terraform 中的設定值
        help="每批次處理的事件數量 (預設: 5000，可測試不同值：5000/7500/10000)"
    )
    parser.add_argument(
        '--no-auto-patch-legacy',
        action='store_true',
        help="停用自動 legacy event 表格修正（預設啟用，僅限進階用途）"
    )
    parser.add_argument(
        '--project_id',
        default=PROJECT_ID,
        help=f"GCP 專案 ID (預設: {PROJECT_ID})"
    )
    parser.add_argument(
        '--source_table',
        default=SOURCE_TABLE_DEFAULT,
        help=f"來源資料表 (預設: {SOURCE_TABLE_DEFAULT})"
    )
    parser.add_argument(
        '--production',
        action='store_true',
        help='寫入正式表 event_prod.integrated_event（預設為 event_test.integrated_event）'
    )
    parser.add_argument(
        '--target_table',
        default=None,
        help="目標資料表（預設依 --production 決定，除非手動指定）"
    )
    parser.add_argument(
        '--dry_run',
        action='store_true',
        help="測試模式：僅查詢資料與顯示內容，不實際寫入"
    )
    parser.add_argument(
        '--no-estimate-cost',
        action='store_true',
        help="停用 BigQuery 查詢費用估算 (預設啟用)"
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help="顯示詳細日誌訊息"
    )
    parser.add_argument(
        '--enable_deduplication',
        action='store_true',
        help="🎛️ 啟用去重複邏輯 (預設停用去重複)"
    )

    args = parser.parse_args()

    # 🔧 設定日誌等級
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 🏗️ 建構完整表格路徑
    source_table_full = args.source_table
    if args.target_table:
        target_table_full = args.target_table
        logger.info(f"⚠️  已手動指定 target_table，將寫入 {target_table_full}")
    else:
        if args.production:
            target_table_full = f"{args.project_id}.event_prod.integrated_event"
            logger.info("⚠️  已啟用 --production，將寫入正式表 event_prod.integrated_event")
        else:
            target_table_full = f"{args.project_id}.event_test.integrated_event"
            logger.info("（預設）將寫入測試表 event_test.integrated_event。如需寫入正式表請加上 --production 參數")

    # 預設 estimate_cost 為 True，除非 --no-estimate-cost
    estimate_cost = not args.no_estimate_cost

    # 🚀 建立並執行同步器
    import time
    try:
        runner = ManualSyncRunner(
            project_id=args.project_id,
            source_table=source_table_full,
            target_table=target_table_full,
            batch_size=args.batch_size,
            enable_deduplication=args.enable_deduplication,  # 🎛️ Feature Toggle
            auto_patch_legacy=not args.no_auto_patch_legacy,
            split_interval_minutes=args.split_interval_minutes
        )

        start_time = time.time()
        result = runner.run_sync(
            start_time_str=args.start,
            end_time_str=args.end,
            dry_run=args.dry_run,
            estimate_cost=estimate_cost
        )
        elapsed = time.time() - start_time
        logger.info(f"⏱️ 同步腳本總執行時間: {elapsed:.2f} 秒 ({elapsed/60:.2f} 分鐘)")

        # 📊 根據結果設定退出碼
        if result.get("status") in ["success", "dry_run_success"]:
            sys.exit(0)
        elif result.get("status") == "partial_success":
            sys.exit(1)  # 部分成功
        else:
            sys.exit(2)  # 完全失敗

    except KeyboardInterrupt:
        logger.info("\n👋 使用者中斷同步作業")
        sys.exit(130)
    except Exception as e:
        logger.error(f"❌ 程式執行失敗: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
