#!/bin/bash

# ReURL BigQuery Scheduler 部署腳本
# 用途：部署 BigQuery Data Transfer Service 到指定環境
# 支援動態目標表設定

set -e  # 遇到錯誤時停止執行

# 預設值
ENVIRONMENT="dev"
ACTION="apply"

# 解析命令列參數
while [[ $# -gt 0 ]]; do
  case $1 in
    -e|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    -a|--action)
      ACTION="$2"
      shift 2
      ;;
    -h|--help)
      echo "使用方法: $0 [選項]"
      echo ""
      echo "選項:"
      echo "  -e, --environment ENV    目標環境 (dev|staging|prod) [預設: dev]"
      echo "  -a, --action ACTION      Terraform 動作 (plan|apply|destroy) [預設: apply]"
      echo "  -h, --help              顯示此說明"
      echo ""
      echo "環境對應表:"
      echo "  • dev:     event_staging.integrated_event (19:00)"
      echo "  • staging: event_staging.integrated_event (18:00)"
      echo "  • prod:    event.integrated_event (07:00)"
      echo ""
      echo "範例:"
      echo "  $0 -e dev -a plan       # 規劃部署到 dev 環境"
      echo "  $0 -e dev -a apply      # 部署到 dev 環境"
      echo "  $0 -e prod -a plan      # 規劃部署到 prod 環境"
      exit 0
      ;;
    *)
      echo "未知選項: $1"
      echo "使用 -h 或 --help 查看使用說明"
      exit 1
      ;;
  esac
done

# 驗證環境參數
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
  echo "❌ 錯誤: 環境必須是 dev、staging 或 prod"
  exit 1
fi

# 驗證動作參數
if [[ ! "$ACTION" =~ ^(plan|apply|destroy)$ ]]; then
  echo "❌ 錯誤: 動作必須是 plan、apply 或 destroy"
  exit 1
fi

# 設定環境特定的變數文件
TFVARS_FILE="environments/${ENVIRONMENT}.tfvars"

if [[ ! -f "$TFVARS_FILE" ]]; then
  echo "❌ 錯誤: 找不到環境變數文件: $TFVARS_FILE"
  exit 1
fi

echo "=== ReURL BigQuery Scheduler 部署 ==="
echo "環境: $ENVIRONMENT"
echo "動作: $ACTION"
echo "變數文件: $TFVARS_FILE"
echo ""

# 顯示目標表資訊
case $ENVIRONMENT in
  "dev")
    echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
    echo "⏰ 排程: 每天 19:00"
    ;;
  "staging")
    echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
    echo "⏰ 排程: 每天 19:00"
    ;;
  "prod")
    echo "🎯 目標表: tagtoo-tracking.event_prod.integrated_event"
    echo "⏰ 排程: 每天 19:00"
    ;;
esac

echo ""

# 檢查 Terraform 是否已初始化
if [[ ! -d ".terraform" ]]; then
  echo "🔧 初始化 Terraform..."
  terraform init
  echo ""
fi

# 執行 Terraform 動作
echo "🚀 執行 terraform $ACTION..."
case $ACTION in
  "plan")
    terraform plan -var-file="$TFVARS_FILE"
    ;;
  "apply")
    terraform apply -var-file="$TFVARS_FILE" -auto-approve
    ;;
  "destroy")
    echo "⚠️  警告: 即將刪除 $ENVIRONMENT 環境的資源"
    read -p "確定要繼續嗎? (yes/no): " confirm
    if [[ "$confirm" == "yes" ]]; then
      terraform destroy -var-file="$TFVARS_FILE" -auto-approve
    else
      echo "取消刪除操作"
      exit 0
    fi
    ;;
esac

echo ""
echo "✅ 部署完成！"

# 顯示輸出資訊
if [[ "$ACTION" == "apply" ]]; then
  echo ""
  echo "📊 部署資訊:"
  terraform output
fi
