# ReURL BigQuery Scheduled Query Service
# 使用 MERGE 語句將 reurl 資料整合到 integrated_event 表

terraform {
  required_version = ">= 1.5.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }

  backend "gcs" {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/reurl"
  }
}

# 取得共用基礎設施的輸出
data "terraform_remote_state" "shared" {
  backend = "gcs"
  config = {
    bucket = "tagtoo-tracking-terraform-state"
    prefix = "integrated-event/shared"
  }
}

# 變數定義
variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be dev, staging, or prod."
  }
}

variable "schedule" {
  description = "Schedule for the data transfer (cron format)"
  type        = string
  default     = "every day 19:00"
}

# 本地變數
locals {
  project_id = data.terraform_remote_state.shared.outputs.project_id
  region     = data.terraform_remote_state.shared.outputs.region

  service_name = "reurl-${var.environment}"

  # BigQuery 相關
  dataset_id = data.terraform_remote_state.shared.outputs.bigquery_dataset_id

  # 根據環境設定目標表 - 改進的邏輯
  target_table   = "integrated_event"
  target_dataset = var.environment == "prod" ? "event_prod" : "event_staging"

  # 完整的目標表名稱
  target_table_full_name = "tagtoo-tracking.${local.target_dataset}.${local.target_table}"

  # 使用 templatefile 函數動態生成 SQL
  processed_sql = templatefile("../sql/reurl_daily_query.sql.tpl", {
    target_table_full_name = local.target_table_full_name
    environment            = var.environment
    project_id             = local.project_id
    target_dataset         = local.target_dataset
    target_table           = local.target_table
  })
}

# Google Provider 配置
provider "google" {
  project = local.project_id
  region  = local.region
}

# 確保 BigQuery Data Transfer API 已啟用
resource "google_project_service" "bigquery_datatransfer" {
  project = local.project_id
  service = "bigquerydatatransfer.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy         = false
}

# 確保 BigQuery API 已啟用
resource "google_project_service" "bigquery" {
  project = local.project_id
  service = "bigquery.googleapis.com"

  disable_dependent_services = false
  disable_on_destroy         = false
}

# 確保現有 Service Account 有 BigQuery 權限
resource "google_project_iam_member" "existing_sa_bigquery_job_user" {
  project = local.project_id
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:<EMAIL>"
}

resource "google_bigquery_dataset_iam_member" "existing_sa_dataset_editor" {
  dataset_id = local.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:<EMAIL>"
}

# BigQuery 排程查詢配置
resource "google_bigquery_data_transfer_config" "reurl_daily_transfer" {
  depends_on = [
    google_project_service.bigquery_datatransfer,
    google_project_service.bigquery
  ]

  display_name   = "ReURL Daily Data Integration - ${upper(var.environment)}"
  location       = local.region
  data_source_id = "scheduled_query"

  schedule = var.schedule

  # 注意：MERGE 操作不需要 destination_dataset_id
  # 因為目標表已經在 SQL 中指定

  params = {
    query = local.processed_sql
    # MERGE 操作不需要 destination_table_name_template 和 write_disposition
  }

  # 通知配置 - 暫時停用
  # notification_pubsub_topic = try("projects/${local.project_id}/topics/${data.terraform_remote_state.shared.outputs.data_processing_topic}", null)

  # 使用現有的 Service Account
  service_account_name = "<EMAIL>"
}

# 輸出
output "transfer_config_name" {
  description = "BigQuery Data Transfer Config name"
  value       = google_bigquery_data_transfer_config.reurl_daily_transfer.name
}

output "transfer_config_id" {
  description = "BigQuery Data Transfer Config ID"
  value       = google_bigquery_data_transfer_config.reurl_daily_transfer.id
}

output "target_table" {
  description = "Target table for data integration"
  value       = local.target_table_full_name
}

output "environment_info" {
  description = "Environment configuration details"
  value = {
    environment     = var.environment
    target_dataset  = local.target_dataset
    target_table    = local.target_table
    full_table_name = local.target_table_full_name
    project_id      = local.project_id
    region          = local.region
  }
}

output "schedule_info" {
  description = "Schedule information"
  value = {
    schedule    = var.schedule
    environment = var.environment
  }
}
