#!/bin/bash

# SQL 生成測試腳本
# 驗證不同環境的 SQL 模板生成是否正確

set -e

echo "=== ReURL SQL 模板生成測試 ==="
echo ""

# 測試環境列表
ENVIRONMENTS=("dev" "staging" "prod")

# 檢查模板文件是否存在
TEMPLATE_FILE="../sql/reurl_daily_query.sql.tpl"
if [[ ! -f "$TEMPLATE_FILE" ]]; then
  echo "❌ 錯誤: 找不到模板文件: $TEMPLATE_FILE"
  exit 1
fi

echo "📄 模板文件: $TEMPLATE_FILE"
echo ""

# 為每個環境生成 SQL
for env in "${ENVIRONMENTS[@]}"; do
  echo "🔧 測試環境: $env"

  # 設定環境特定的變數
  case $env in
    "dev")
      target_dataset="event_staging"
      target_table="integrated_event"
      ;;
    "staging")
      target_dataset="event_staging"
      target_table="integrated_event"
      ;;
    "prod")
      target_dataset="event_prod"
      target_table="integrated_event"
      ;;
  esac

  target_table_full_name="tagtoo-tracking.${target_dataset}.${target_table}"

  echo "  📊 目標表: $target_table_full_name"

  # 生成 SQL（模擬 Terraform templatefile 函數）
  output_file="test_output_${env}.sql"

  # 使用 sed 進行簡單的變數替換
  sed -e "s/\${target_table_full_name}/$target_table_full_name/g" \
      -e "s/\${environment}/$env/g" \
      -e "s/\${project_id}/tagtoo-tracking/g" \
      -e "s/\${target_dataset}/$target_dataset/g" \
      -e "s/\${target_table}/$target_table/g" \
      "$TEMPLATE_FILE" > "$output_file"

  # 檢查生成的 SQL
  if grep -q "MERGE \`$target_table_full_name\`" "$output_file"; then
    echo "  ✅ SQL 生成成功"
  else
    echo "  ❌ SQL 生成失敗：找不到正確的目標表"
    exit 1
  fi

  # 檢查是否還有未替換的變數
  if grep -q '\${' "$output_file"; then
    echo "  ⚠️  警告：發現未替換的變數："
    grep -n '\${' "$output_file" | head -3
  fi

  echo "  💾 輸出文件: $output_file"
  echo ""
done

echo "🎉 所有環境的 SQL 生成測試通過！"
echo ""
echo "📁 生成的測試文件："
ls -la test_output_*.sql

echo ""
echo "🧹 清理測試文件..."
rm -f test_output_*.sql
echo "✅ 清理完成"
