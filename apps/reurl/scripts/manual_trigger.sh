#!/bin/bash

# BigQuery Data Transfer 手動觸發腳本
# 用途：手動觸發 ReURL 資料整合任務

set -e

TRANSFER_CONFIG_ID="6859635c-0000-2750-9f7a-582429ace874"
PROJECT_ID="tagtoo-tracking"
LOCATION="asia-east1"

echo "=== BigQuery Data Transfer 手動觸發 ==="
echo "Transfer Config ID: $TRANSFER_CONFIG_ID"
echo "Project: $PROJECT_ID"
echo "Location: $LOCATION"
echo ""

# 方法 1: 立即執行 (Run Now)
echo "選項 1: 立即執行 (Run Now)"
echo "curl -X POST \\"
echo "  \"https://bigquerydatatransfer.googleapis.com/v1/projects/$PROJECT_ID/locations/$LOCATION/transferConfigs/$TRANSFER_CONFIG_ID:startManualRuns\" \\"
echo "  -H \"Authorization: Bearer \$(gcloud auth print-access-token)\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"requestedRunTime\": \"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}'"
echo ""

# 方法 2: 回填特定日期
echo "選項 2: 回填特定日期 (Schedule Backfill)"
echo "curl -X POST \\"
echo "  \"https://bigquerydatatransfer.googleapis.com/v1/projects/$PROJECT_ID/locations/$LOCATION/transferConfigs/$TRANSFER_CONFIG_ID:scheduleRuns\" \\"
echo "  -H \"Authorization: Bearer \$(gcloud auth print-access-token)\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"startTime\": \"2024-12-24T00:00:00Z\","
echo "    \"endTime\": \"2024-12-24T23:59:59Z\""
echo "  }'"
echo ""

# 查看執行狀態
echo "查看執行狀態："
echo "curl -X GET \\"
echo "  \"https://bigquerydatatransfer.googleapis.com/v1/projects/$PROJECT_ID/locations/$LOCATION/transferConfigs/$TRANSFER_CONFIG_ID/runs\" \\"
echo "  -H \"Authorization: Bearer \$(gcloud auth print-access-token)\""

echo ""
echo "=== 建議使用 BigQuery Console 進行手動觸發 ==="
echo "URL: https://console.cloud.google.com/bigquery/transfers?project=$PROJECT_ID"
