# 動態目標表配置說明

## 概述

ReURL BigQuery Scheduler 現在支援根據部署環境動態調整目標表，實現更好的環境隔離和資料管理。

## 🎯 環境對應表

| 環境        | Dataset         | 完整表名                                         | 排程時間 | 說明           |
| ----------- | --------------- | ------------------------------------------------ | -------- | -------------- |
| **dev**     | `event_staging` | `tagtoo-tracking.event_staging.integrated_event` | 19:00    | 開發環境測試   |
| **staging** | `event_staging` | `tagtoo-tracking.event_staging.integrated_event` | 19:00    | 預發布環境驗證 |
| **prod**    | `event_prod`    | `tagtoo-tracking.event_prod.integrated_event`    | 19:00    | 生產環境       |

## 🏗️ 架構設計

### 1. SQL 模板系統

```sql
-- 模板文件: sql/reurl_daily_query.sql.tpl
MERGE `${target_table_full_name}` AS target
-- 環境: ${environment}
-- 目標表: ${target_table_full_name}
```

### 2. Terraform 動態配置

```hcl
# terraform/main.tf
locals {
  target_table = "integrated_event"
  target_dataset = var.environment == "prod" ? "event_prod" : "event_staging"
  target_table_full_name = "tagtoo-tracking.${local.target_dataset}.${local.target_table}"

  processed_sql = templatefile("../sql/reurl_daily_query.sql.tpl", {
    target_table_full_name = local.target_table_full_name
    environment           = var.environment
  })
}
```

### 3. 環境變數文件

```bash
# terraform/environments/dev.tfvars
environment = "dev"
schedule    = "every day 19:00"

# terraform/environments/prod.tfvars
environment = "prod"
schedule    = "every day 07:00"
```

## 🚀 部署命令

### 基本部署

```bash
# 部署到 dev 環境
make apply-dev

# 部署到 staging 環境
make apply-staging

# 部署到 prod 環境
make apply-prod
```

### 詳細部署

```bash
# 使用部署腳本
cd terraform
./deploy.sh -e dev -a apply
./deploy.sh -e staging -a plan
./deploy.sh -e prod -a apply
```

## 🧪 測試和驗證

### 1. SQL 模板生成測試

```bash
# 測試所有環境的 SQL 生成
make test-sql

# 或直接執行測試腳本
cd scripts
./test_sql_generation.sh
```

### 2. Terraform 配置驗證

```bash
# 驗證配置和測試 SQL 生成
make validate
```

## 📁 文件結構

```
apps/reurl/
├── sql/
│   ├── reurl_daily_query.sql          # 原始 SQL（已棄用）
│   └── reurl_daily_query.sql.tpl      # SQL 模板文件
├── terraform/
│   ├── main.tf                        # 主要 Terraform 配置
│   ├── deploy.sh                      # 部署腳本
│   └── environments/                  # 環境變數文件
│       ├── dev.tfvars
│       ├── staging.tfvars
│       └── prod.tfvars
├── scripts/
│   └── test_sql_generation.sh         # SQL 生成測試腳本
└── Makefile                           # 簡化的管理命令
```

## 🔧 技術細節

### 1. 模板變數

SQL 模板支援以下變數：

- `${target_table_full_name}`: 完整的目標表名稱
- `${environment}`: 環境名稱（dev/staging/prod）
- `${project_id}`: GCP 專案 ID
- `${target_dataset}`: 目標 dataset
- `${target_table}`: 目標表名稱

### 2. 環境邏輯

```hcl
# 根據環境選擇 dataset
target_dataset = var.environment == "prod" ? "event_prod" : "event_staging"
```

### 3. 安全性考量

- 生產環境使用獨立的 dataset (`event_prod`)
- 開發和 staging 環境共用 dataset (`event_staging`) 但有不同的排程
- 使用 Terraform state 管理確保部署一致性

## 🛠️ 維護和監控

### 1. 檢查部署狀態

```bash
# 查看 Terraform 輸出
cd terraform
terraform output

# 檢查 Data Transfer 執行狀態
make check-runs
```

### 2. 手動觸發

```bash
# 立即觸發執行
make trigger-now

# 查看執行記錄
make logs
```

### 3. 故障排除

1. **SQL 生成錯誤**：執行 `make test-sql` 檢查模板
2. **部署失敗**：檢查 Terraform 變數文件和權限
3. **執行失敗**：查看 BigQuery Console 中的錯誤記錄

## 📈 最佳實務

1. **環境隔離**：確保不同環境使用不同的 dataset
2. **版本控制**：所有配置文件納入 Git 管理
3. **測試優先**：部署前執行 `make validate`
4. **監控**：定期檢查執行狀態和資料品質
5. **文檔更新**：配置變更時更新相關文檔

## 🔄 升級路徑

從舊版本升級到動態表格配置：

1. 備份現有配置
2. 更新 Terraform 配置
3. 建立環境變數文件
4. 執行測試驗證
5. 重新部署服務

## 📞 支援

如有問題，請參考：

1. Terraform 輸出：`terraform output`
2. 測試腳本：`make test-sql`
3. 部署記錄：BigQuery Console > Data transfers
4. 錯誤記錄：Cloud Logging
