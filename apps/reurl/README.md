# ReURL BigQuery Scheduled Query Service

> Integrate reurl data from reurl-scraper with BigQuery scheduler

## Overview

This service provides a simple integration of ReURL data into the integrated event platform using BigQuery's native scheduled query feature (Data Transfer Service). It runs daily SQL queries to transform and load ReURL data into the unified `integrated_event` table.

## Architecture

```
BigQuery Data Transfer Service → Execute SQL Daily → Write Results to BigQuery Table
```

**Components:**

- **BigQuery Data Transfer Config**: Native BigQuery scheduling service
- **SQL Query**: Daily transformation logic (`sql/reurl_daily_query.sql`)
- **Destination Table**: `reurl_daily_{run_date}` pattern

## Configuration

### Schedule

- **Default**: Daily at 7:00 AM (Asia/Taipei)
- **Configurable**: Via Terraform variable `schedule`

### Data Flow

1. Reads data from reurl-scraper source tables
2. Transforms data to match `integrated_event` schema
3. Writes results to destination table with date suffix
4. Automatically handles retries and error notifications

## Files Structure

```
apps/reurl/
├── terraform/
│   └── main.tf              # BigQuery Data Transfer configuration
├── sql/
│   └── reurl_daily_query.sql # SQL transformation logic
└── README.md                # This documentation
```

## Deployment

### Prerequisites

- Shared infrastructure must be deployed first
- BigQuery dataset and permissions configured
- ReURL source data available

### Deploy to Development

```bash
cd apps/reurl/terraform
terraform init
terraform plan -var="environment=dev"
terraform apply -var="environment=dev"
```

### Deploy to Production

```bash
cd apps/reurl/terraform
terraform plan -var="environment=prod"
terraform apply -var="environment=prod"
```

## Configuration Variables

| Variable                   | Description                               | Default           | Required |
| -------------------------- | ----------------------------------------- | ----------------- | -------- |
| `environment`              | Deployment environment (dev/staging/prod) | `dev`             | No       |
| `schedule`                 | Schedule for data transfer                | `every day 07:00` | No       |
| `destination_table_suffix` | Table name suffix                         | `reurl_daily`     | No       |

## SQL Query Customization

Edit `sql/reurl_daily_query.sql` to:

1. **Update Source Tables**: Replace placeholder table references with actual reurl-scraper tables

   ```sql
   FROM `your-project.reurl_dataset.reurl_data`  -- Update this
   ```

2. **Modify Field Mappings**: Adjust field mappings based on actual reurl data schema

   ```sql
   user_id as partner_user_id,  -- Update field names
   url as link,
   title as page_title,
   ```

3. **Add Business Logic**: Include any specific transformation logic for ReURL data

4. **Update Filters**: Modify date filters and data quality checks as needed

## Monitoring

The service automatically integrates with the platform's monitoring:

- **Notifications**: Failures sent to configured Pub/Sub topic
- **Logs**: Available in Cloud Logging under BigQuery Data Transfer Service
- **Metrics**: Transfer success/failure metrics in Cloud Monitoring

## Data Schema

Output data matches the `integrated_event` table schema:

- **User Identification**: `permanent`, `partner_user_id`, `ec_id`
- **Event Data**: `event`, `event_time`, `create_time`
- **Page Information**: `link`, `page_title`
- **Metadata**: `source_metadata`, `raw_data`

## Troubleshooting

### Common Issues

1. **Transfer Failed**: Check Cloud Logging for BigQuery Data Transfer Service
2. **SQL Errors**: Validate SQL syntax in BigQuery console
3. **Permission Errors**: Ensure proper IAM roles for Data Transfer Service
4. **Source Data Missing**: Verify reurl-scraper data availability

### Manual Execution

```bash
# Test SQL query manually in BigQuery console
# Copy content from sql/reurl_daily_query.sql
```

### View Transfer History

```bash
# In Google Cloud Console
# Go to BigQuery > Data transfers > [Your transfer config]
```

## Development

This service is intentionally minimal - it contains only:

- Terraform configuration for BigQuery Data Transfer
- SQL query for data transformation
- Basic documentation

No additional code, containers, or complex infrastructure required.

## Related Services

- **Legacy Event Sync**: Similar pattern for existing event data
- **Shared Infrastructure**: Provides BigQuery dataset and permissions
- **ReURL Scraper**: Source of the data being integrated

---

**Service Type**: BigQuery Scheduled Query
**Deployment**: Terraform
**Runtime**: BigQuery Data Transfer Service
**Schedule**: Daily
**Maintenance**: Low
