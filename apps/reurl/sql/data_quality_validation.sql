-- ReURL Data Quality Validation
-- 驗證 MERGE 操作後的資料品質和轉換正確性
-- 建議在每次執行 MERGE 後運行此驗證腳本

-- 設定驗證日期範圍
DECLARE validation_date DATE DEFAULT DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY);

-- =============================================================================
-- 1. 基本資料統計檢查
-- =============================================================================
SELECT
  '1. 基本資料統計' as check_category,
  '資料筆數統計' as check_name,
  COUNT(*) as total_records,
  COUNT(DISTINCT permanent) as unique_users,
  COUNT(DISTINCT link) as unique_links,
  MIN(event_time) as earliest_event,
  MAX(event_time) as latest_event,
  '✅ 檢查數據是否合理' as validation_note
FROM `tagtoo-tracking.event_test.integrated_event`
WHERE partner_source = 'reurl'
  AND DATE(event_time) = validation_date;

-- =============================================================================
-- 2. 必要欄位完整性檢查
-- =============================================================================
WITH completeness_check AS (
  SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN permanent IS NULL OR permanent = '' THEN 1 END) as missing_permanent,
    COUNT(CASE WHEN partner_id IS NULL THEN 1 END) as missing_partner_id,
    COUNT(CASE WHEN event_time IS NULL THEN 1 END) as missing_event_time,
    COUNT(CASE WHEN link IS NULL OR link = '' THEN 1 END) as missing_link,
    COUNT(CASE WHEN partner_source IS NULL OR partner_source != 'reurl' THEN 1 END) as wrong_partner_source,
    COUNT(CASE WHEN event IS NULL OR event != 'page_view' THEN 1 END) as wrong_event_type
  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = validation_date
)
SELECT
  '2. 資料完整性' as check_category,
  'missing_permanent' as check_name,
  missing_permanent as issue_count,
  ROUND(missing_permanent / total_records * 100, 2) as issue_percentage,
  CASE WHEN missing_permanent = 0 THEN '✅ 通過' ELSE '❌ 有缺失' END as status
FROM completeness_check

UNION ALL

SELECT
  '2. 資料完整性',
  'missing_partner_id',
  missing_partner_id,
  ROUND(missing_partner_id / total_records * 100, 2),
  CASE WHEN missing_partner_id = 0 THEN '✅ 通過' ELSE '❌ 有缺失' END
FROM completeness_check

UNION ALL

SELECT
  '2. 資料完整性',
  'missing_event_time',
  missing_event_time,
  ROUND(missing_event_time / total_records * 100, 2),
  CASE WHEN missing_event_time = 0 THEN '✅ 通過' ELSE '❌ 有缺失' END
FROM completeness_check

UNION ALL

SELECT
  '2. 資料完整性',
  'missing_link',
  missing_link,
  ROUND(missing_link / total_records * 100, 2),
  CASE WHEN missing_link = 0 THEN '✅ 通過' ELSE '❌ 有缺失' END
FROM completeness_check

UNION ALL

SELECT
  '2. 資料完整性',
  'wrong_partner_source',
  wrong_partner_source,
  ROUND(wrong_partner_source / total_records * 100, 2),
  CASE WHEN wrong_partner_source = 0 THEN '✅ 通過' ELSE '❌ 資料錯誤' END
FROM completeness_check

UNION ALL

SELECT
  '2. 資料完整性',
  'wrong_event_type',
  wrong_event_type,
  ROUND(wrong_event_type / total_records * 100, 2),
  CASE WHEN wrong_event_type = 0 THEN '✅ 通過' ELSE '❌ 資料錯誤' END
FROM completeness_check;

-- =============================================================================
-- 3. 資料型別和格式檢查
-- =============================================================================
SELECT
  '3. 資料格式' as check_category,
  'URL格式檢查' as check_name,
  COUNT(*) as total_links,
  COUNT(CASE WHEN link LIKE 'http%' THEN 1 END) as valid_http_links,
  COUNT(CASE WHEN link NOT LIKE 'http%' THEN 1 END) as invalid_links,
  ROUND(COUNT(CASE WHEN link LIKE 'http%' THEN 1 END) / COUNT(*) * 100, 2) as valid_percentage,
  CASE
    WHEN COUNT(CASE WHEN link NOT LIKE 'http%' THEN 1 END) = 0 THEN '✅ 所有URL格式正確'
    ELSE CONCAT('⚠️ 有 ', COUNT(CASE WHEN link NOT LIKE 'http%' THEN 1 END), ' 個無效URL')
  END as status
FROM `tagtoo-tracking.event_test.integrated_event`
WHERE partner_source = 'reurl'
  AND DATE(event_time) = validation_date;

-- =============================================================================
-- 4. 時間戳檢查
-- =============================================================================
WITH time_validation AS (
  SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN event_time > create_time THEN 1 END) as future_events,
    COUNT(CASE WHEN DATE(event_time) != validation_date THEN 1 END) as wrong_date_events,
    COUNT(CASE WHEN TIMESTAMP_DIFF(create_time, event_time, HOUR) > 48 THEN 1 END) as old_events
  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = validation_date
)
SELECT
  '4. 時間戳驗證' as check_category,
  'event_time_vs_create_time' as check_name,
  future_events as issue_count,
  CASE WHEN future_events = 0 THEN '✅ 事件時間正常' ELSE '❌ 有未來事件' END as status,
  'event_time 應該 <= create_time' as note
FROM time_validation

UNION ALL

SELECT
  '4. 時間戳驗證',
  'event_date_correctness',
  wrong_date_events,
  CASE WHEN wrong_date_events = 0 THEN '✅ 事件日期正確' ELSE '❌ 事件日期錯誤' END,
  CONCAT('應該都是 ', validation_date, ' 的事件')
FROM time_validation

UNION ALL

SELECT
  '4. 時間戳驗證',
  'processing_delay',
  old_events,
  CASE WHEN old_events = 0 THEN '✅ 處理延遲正常' ELSE '⚠️ 有處理延遲' END,
  '處理時間與事件時間差距 > 48小時'
FROM time_validation;

-- =============================================================================
-- 5. 重複資料檢查
-- =============================================================================
WITH duplicate_check AS (
  SELECT
    permanent,
    partner_id,
    event_time,
    link,
    page.title,
    page.description,
    COUNT(*) as duplicate_count
  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = validation_date
  GROUP BY permanent, partner_id, event_time, link, page.title, page.description
  HAVING COUNT(*) > 1
)
SELECT
  '5. 重複資料檢查' as check_category,
  '重複記錄統計' as check_name,
  COUNT(*) as duplicate_groups,
  SUM(duplicate_count) as total_duplicates,
  CASE
    WHEN COUNT(*) = 0 THEN '✅ 沒有重複資料'
    ELSE CONCAT('❌ 發現 ', COUNT(*), ' 組重複資料')
  END as status,
  'MERGE 語句應該避免重複資料' as note
FROM duplicate_check;

-- =============================================================================
-- 6. 資料來源追蹤檢查
-- =============================================================================
WITH source_comparison AS (
  -- 比較來源資料與目標資料的筆數
  SELECT
    'source_data' as data_source,
    COUNT(*) as record_count
  FROM `gothic-province-823.tagtooad.partner_uid_mapping`
  WHERE DATE(created) = validation_date
    AND partner_user_id IS NOT NULL
    AND tagtoo_user_id IS NOT NULL
    AND JSON_EXTRACT_SCALAR(arg, '$.link') IS NOT NULL

  UNION ALL

  SELECT
    'integrated_data' as data_source,
    COUNT(*) as record_count
  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = validation_date
)
SELECT
  '6. 資料來源對比' as check_category,
  data_source,
  record_count,
  CASE
    WHEN data_source = 'source_data' THEN '原始資料筆數'
    WHEN data_source = 'integrated_data' THEN '整合後資料筆數'
  END as description,
  CASE
    WHEN data_source = 'integrated_data' AND record_count > 0 THEN '✅ 有資料寫入'
    WHEN data_source = 'source_data' AND record_count = 0 THEN '⚠️ 來源資料為空'
    ELSE '📊 資料統計'
  END as status
FROM source_comparison
ORDER BY data_source;

-- =============================================================================
-- 7. 抽樣資料檢查
-- =============================================================================
SELECT
  '7. 抽樣資料檢查' as check_category,
  '隨機抽樣 5 筆資料' as check_name,
  permanent,
  partner_id,
  event_time,
  link,
  page.title as page_title,
  page.description as page_description,
  user.partner_user_id,
  create_time,
  '👀 人工檢查這些資料是否合理' as note
FROM `tagtoo-tracking.event_test.integrated_event`
WHERE partner_source = 'reurl'
  AND DATE(event_time) = validation_date
ORDER BY RAND()
LIMIT 5;

-- =============================================================================
-- 8. URL 媒合成功率檢查
-- =============================================================================
WITH url_matching AS (
  SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN page.title IS NOT NULL AND page.title != '' THEN 1 END) as with_title,
    COUNT(CASE WHEN page.description IS NOT NULL AND page.description != '' THEN 1 END) as with_description
  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = validation_date
)
SELECT
  '8. URL媒合檢查' as check_category,
  'title_matching_rate' as check_name,
  with_title as matched_count,
  total_records,
  ROUND(with_title / total_records * 100, 2) as matching_percentage,
  CASE
    WHEN with_title / total_records > 0.8 THEN '✅ 媒合率良好 (>80%)'
    WHEN with_title / total_records > 0.5 THEN '⚠️ 媒合率中等 (50-80%)'
    ELSE '❌ 媒合率偏低 (<50%)'
  END as status
FROM url_matching

UNION ALL

SELECT
  '8. URL媒合檢查',
  'description_matching_rate',
  with_description,
  total_records,
  ROUND(with_description / total_records * 100, 2),
  CASE
    WHEN with_description / total_records > 0.8 THEN '✅ 媒合率良好 (>80%)'
    WHEN with_description / total_records > 0.5 THEN '⚠️ 媒合率中等 (50-80%)'
    ELSE '❌ 媒合率偏低 (<50%)'
  END
FROM url_matching;

-- =============================================================================
-- 9. 異常值檢查
-- =============================================================================
SELECT
  '9. 異常值檢查' as check_category,
  '異常長度URL' as check_name,
  COUNT(*) as count,
  MIN(LENGTH(link)) as min_length,
  MAX(LENGTH(link)) as max_length,
  AVG(LENGTH(link)) as avg_length,
  CASE
    WHEN MAX(LENGTH(link)) > 2000 THEN '⚠️ 有超長URL'
    WHEN MIN(LENGTH(link)) < 10 THEN '⚠️ 有超短URL'
    ELSE '✅ URL長度正常'
  END as status
FROM `tagtoo-tracking.event_test.integrated_event`
WHERE partner_source = 'reurl'
  AND DATE(event_time) = validation_date;

-- =============================================================================
-- 使用說明
-- =============================================================================
-- 1. 在每次執行 MERGE 後運行此驗證腳本
-- 2. 重點關注標記為 ❌ 或 ⚠️ 的檢查項目
-- 3. 抽樣資料檢查需要人工確認資料合理性
-- 4. URL媒合率如果偏低，可能需要檢查 reurl_link_metadata 表的資料完整性
-- 5. 如果發現重複資料，需要檢查 MERGE 語句的匹配條件
