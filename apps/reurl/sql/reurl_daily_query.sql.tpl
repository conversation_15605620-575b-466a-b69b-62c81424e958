-- ReURL Daily Data Integration Query
-- 將 reurl_link_metadata 中的 URL 資料與 partner_uid_mapping 中的使用者資料媒合
-- 使用 MERGE 語句寫入 ${target_table_full_name}
-- 每日透過 BigQuery Data Transfer Service 執行
-- 環境: ${environment}

-- 宣告日期參數，可動態調整查詢範圍
DECLARE start_date DATE DEFAULT DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY);
DECLARE end_date DATE DEFAULT DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY);

-- 使用 MERGE 語句進行 upsert 操作
MERGE `${target_table_full_name}` AS target
USING (
  -- 解析使用者造訪記錄，從 arg JSON 欄位中提取 URL 資訊
  WITH user_visits AS (
    SELECT
      partner_id,
      partner_user_id,
      tagtoo_user_id,
      user_ip,
      created as visit_time,
      -- 從 arg JSON 欄位中提取 link 資訊，格式為 {"link": "https://..."}
      JSON_EXTRACT_SCALAR(arg, '$.link') as visited_url,
      arg as raw_args
    FROM `gothic-province-823.tagtooad.partner_uid_mapping`
    WHERE DATE(created) >= start_date
      AND DATE(created) <= end_date
      AND partner_user_id IS NOT NULL
      AND tagtoo_user_id IS NOT NULL
      AND JSON_EXTRACT_SCALAR(arg, '$.link') IS NOT NULL  -- 確保有 link 資訊
  ),

  -- 媒合 URL 內容資訊
  matched_data AS (
    SELECT
      uv.partner_id,
      uv.partner_user_id,
      uv.tagtoo_user_id,
      uv.user_ip,
      uv.visit_time,
      uv.visited_url,
      uv.raw_args,
      -- URL 內容資訊
      rlm.link,
      rlm.title,
      rlm.description as url_description,
      rlm.created as url_created_time,
      rlm.updated_at as url_updated_time
    FROM user_visits uv
    LEFT JOIN `tagtoo-tracking.tagtooad.reurl_link_metadata` rlm
      ON uv.visited_url = rlm.link
  ),

  -- 轉換為 integrated_event 表結構
  transformed_data AS (
    SELECT
      -- 核心識別欄位
      COALESCE(tagtoo_user_id, '') as permanent,  -- Tagtoo User ID
      SAFE_CAST(partner_id AS INT64) as partner_id,  -- Partner ID (轉換為 INT64)
      CAST(NULL AS INT64) as ec_id,  -- E-commerce ID (暫設為 NULL)
      'reurl' as partner_source,  -- 固定為 reurl

      -- 事件資訊
      'page_view' as event,  -- 固定為 page_view 事件
      visit_time as event_time,  -- 使用者造訪時間
      CURRENT_TIMESTAMP() as create_time,  -- 資料整合時間

      -- 交易相關欄位 (頁面瀏覽事件通常為 NULL)
      CAST(NULL AS FLOAT64) as value,
      CAST(NULL AS STRING) as currency,
      CAST(NULL AS STRING) as order_id,

      -- 頁面資訊
      COALESCE(link, visited_url) as link,  -- 優先使用 metadata 中的 link，否則使用 visited_url

      -- 新的 page 結構欄位
      STRUCT(
        title as title,           -- 頁面標題
        url_description as description  -- 頁面描述
      ) as page,

      -- 使用者資訊結構
      STRUCT(
        partner_user_id,  -- Partner 平台的使用者 ID
        CAST(NULL AS STRING) as em,  -- Email (暫無資料)
        CAST(NULL AS STRING) as ph   -- Phone (暫無資料)
      ) as user,

      -- 商品資訊陣列 (頁面瀏覽事件通常為空)
      CAST([] AS ARRAY<STRUCT<
        id STRING,
        name STRING,
        description STRING,
        price FLOAT64,
        quantity FLOAT64
      >>) as items

    FROM matched_data
    WHERE visit_time IS NOT NULL  -- 確保有造訪時間
      AND (link IS NOT NULL OR visited_url IS NOT NULL)  -- 確保有 URL 資訊
  )

  SELECT
    permanent,
    partner_id,
    ec_id,
    partner_source,
    event,
    event_time,
    create_time,
    value,
    currency,
    order_id,
    link,
    page,  -- 新的 page 結構欄位
    user,
    items
  FROM transformed_data
) AS source

-- 定義匹配條件：避免重複資料的關鍵欄位組合
ON target.permanent = source.permanent
   AND target.partner_id = source.partner_id
   AND target.event_time = source.event_time
   AND target.link = source.link
   AND COALESCE(target.page.title, '') = COALESCE(source.page.title, '')
   AND COALESCE(target.page.description, '') = COALESCE(source.page.description, '')

-- 當沒有匹配時，插入新記錄
WHEN NOT MATCHED THEN
  INSERT (
    permanent,
    partner_id,
    ec_id,
    partner_source,
    event,
    event_time,
    create_time,
    value,
    currency,
    order_id,
    link,
    page,
    user,
    items
  )
  VALUES (
    source.permanent,
    source.partner_id,
    source.ec_id,
    source.partner_source,
    source.event,
    source.event_time,
    source.create_time,
    source.value,
    source.currency,
    source.order_id,
    source.link,
    source.page,
    source.user,
    source.items
  );

-- 動態配置說明：
-- 環境: ${environment}
-- 目標表: ${target_table_full_name}
--
-- 環境對應表：
-- • dev: tagtoo-tracking.event_staging.integrated_event
-- • staging: tagtoo-tracking.event_staging.integrated_event
-- • prod: tagtoo-tracking.event_prod.integrated_event
--
-- 使用說明：
-- 1. MERGE 語句提供更好的效能和原子性操作
-- 2. 匹配條件基於 permanent, partner_id, event_time, link, page.title, page.description
-- 3. 只有不存在的記錄會被插入，避免重複資料
-- 4. 可透過修改 start_date 和 end_date 變數來調整查詢的日期範圍
-- 5. 預設查詢昨天的資料：start_date = 昨天, end_date = 昨天

-- MERGE 語句的優點：
-- 1. 更好的效能：BigQuery 針對 MERGE 做了優化
-- 2. 原子性操作：整個操作是原子的，避免競態條件
-- 3. 語義清晰：明確表達 upsert 的意圖
-- 4. 更好的並發控制：支援更高的並發寫入

-- 注意事項：
-- 1. MERGE 語句會自動處理重複資料檢查
-- 2. 如果需要更新現有記錄，可以加入 WHEN MATCHED THEN UPDATE 子句
-- 3. 匹配條件中使用 COALESCE 來正確處理 NULL 值比較
