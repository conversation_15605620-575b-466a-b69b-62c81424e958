-- 快速資料品質檢查
-- 用於日常驗證 MERGE 操作後的資料正確性

DECLARE check_date DATE DEFAULT DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY);

-- =============================================================================
-- 一站式資料品質檢查儀表板
-- =============================================================================
WITH data_quality_summary AS (
  SELECT
    -- 基本統計
    COUNT(*) as total_records,
    COUNT(DISTINCT permanent) as unique_users,
    COUNT(DISTINCT link) as unique_urls,

    -- 完整性檢查
    COUNT(CASE WHEN permanent IS NULL OR permanent = '' THEN 1 END) as missing_permanent,
    COUNT(CASE WHEN partner_id IS NULL THEN 1 END) as missing_partner_id,
    COUNT(CASE WHEN link IS NULL OR link = '' THEN 1 END) as missing_link,
    COUNT(CASE WHEN event_time IS NULL THEN 1 END) as missing_event_time,

    -- 格式檢查
    COUNT(CASE WHEN link NOT LIKE 'http%' THEN 1 END) as invalid_urls,
    COUNT(CASE WHEN partner_source != 'reurl' THEN 1 END) as wrong_source,
    COUNT(CASE WHEN event != 'page_view' THEN 1 END) as wrong_event_type,

    -- 時間檢查
    COUNT(CASE WHEN event_time > create_time THEN 1 END) as future_events,
    COUNT(CASE WHEN DATE(event_time) != check_date THEN 1 END) as wrong_date_events,

    -- 媒合檢查
    COUNT(CASE WHEN page.title IS NOT NULL AND page.title != '' THEN 1 END) as with_title,
    COUNT(CASE WHEN page.description IS NOT NULL AND page.description != '' THEN 1 END) as with_description,

    -- 時間範圍
    MIN(event_time) as earliest_event,
    MAX(event_time) as latest_event

  FROM `tagtoo-tracking.event_test.integrated_event`
  WHERE partner_source = 'reurl'
    AND DATE(event_time) = check_date
),

-- 重複資料檢查
duplicate_summary AS (
  SELECT COUNT(*) as duplicate_groups
  FROM (
    SELECT
      permanent, partner_id, event_time, link, page.title, page.description,
      COUNT(*) as cnt
    FROM `tagtoo-tracking.event_test.integrated_event`
    WHERE partner_source = 'reurl'
      AND DATE(event_time) = check_date
    GROUP BY permanent, partner_id, event_time, link, page.title, page.description
    HAVING COUNT(*) > 1
  )
),

-- 來源資料對比
source_count AS (
  SELECT COUNT(*) as source_records
  FROM `gothic-province-823.tagtooad.partner_uid_mapping`
  WHERE DATE(created) = check_date
    AND partner_user_id IS NOT NULL
    AND tagtoo_user_id IS NOT NULL
    AND JSON_EXTRACT_SCALAR(arg, '$.link') IS NOT NULL
)

-- 整合報告
SELECT
  '📊 資料品質檢查報告' as report_title,
  check_date as check_date,

  -- 基本統計
  CONCAT('總筆數: ', total_records, ' | 使用者: ', unique_users, ' | URL: ', unique_urls) as basic_stats,

  -- 完整性分數 (滿分100)
  ROUND(
    (1 - (missing_permanent + missing_partner_id + missing_link + missing_event_time) /
     GREATEST(total_records * 4, 1)) * 100, 1
  ) as completeness_score,

  -- 格式正確性分數 (滿分100)
  ROUND(
    (1 - (invalid_urls + wrong_source + wrong_event_type) /
     GREATEST(total_records * 3, 1)) * 100, 1
  ) as format_score,

  -- 時間正確性分數 (滿分100)
  ROUND(
    (1 - (future_events + wrong_date_events) /
     GREATEST(total_records * 2, 1)) * 100, 1
  ) as time_score,

  -- URL媒合率
  ROUND(with_title / GREATEST(total_records, 1) * 100, 1) as title_match_rate,
  ROUND(with_description / GREATEST(total_records, 1) * 100, 1) as desc_match_rate,

  -- 重複資料檢查
  duplicate_groups as duplicate_issues,

  -- 資料轉換率
  ROUND(total_records / GREATEST(source_records, 1) * 100, 1) as conversion_rate,

  -- 時間範圍
  earliest_event,
  latest_event,

  -- 整體評分 (滿分100)
  ROUND(
    (ROUND((1 - (missing_permanent + missing_partner_id + missing_link + missing_event_time) /
           GREATEST(total_records * 4, 1)) * 100, 1) * 0.3 +
     ROUND((1 - (invalid_urls + wrong_source + wrong_event_type) /
           GREATEST(total_records * 3, 1)) * 100, 1) * 0.3 +
     ROUND((1 - (future_events + wrong_date_events) /
           GREATEST(total_records * 2, 1)) * 100, 1) * 0.2 +
     ROUND(with_title / GREATEST(total_records, 1) * 100, 1) * 0.2), 1
  ) as overall_score,

  -- 狀態評估
  CASE
    WHEN total_records = 0 THEN '❌ 沒有資料'
    WHEN duplicate_groups > 0 THEN '❌ 有重複資料'
    WHEN (missing_permanent + missing_partner_id + missing_link + missing_event_time) > 0 THEN '❌ 有缺失資料'
    WHEN (invalid_urls + wrong_source + wrong_event_type) > 0 THEN '❌ 有格式錯誤'
    WHEN (future_events + wrong_date_events) > 0 THEN '❌ 有時間錯誤'
    WHEN with_title / GREATEST(total_records, 1) < 0.5 THEN '⚠️ URL媒合率偏低'
    WHEN total_records / GREATEST(source_records, 1) < 0.8 THEN '⚠️ 資料轉換率偏低'
    ELSE '✅ 資料品質良好'
  END as status_summary

FROM data_quality_summary, duplicate_summary, source_count;

-- =============================================================================
-- 問題詳情查詢（如果上面檢查發現問題，可以執行以下查詢）
-- =============================================================================

-- 如果有重複資料，顯示詳情
-- SELECT
--   '重複資料詳情' as issue_type,
--   permanent, partner_id, event_time, link, page.title, page.description, COUNT(*) as count
-- FROM `tagtoo-tracking.event_test.integrated_event`
-- WHERE partner_source = 'reurl' AND DATE(event_time) = check_date
-- GROUP BY permanent, partner_id, event_time, link, page.title, page.description
-- HAVING COUNT(*) > 1
-- ORDER BY count DESC;

-- 如果有格式錯誤，顯示詳情
-- SELECT
--   '格式錯誤詳情' as issue_type,
--   link, partner_source, event,
--   CASE
--     WHEN link NOT LIKE 'http%' THEN 'Invalid URL'
--     WHEN partner_source != 'reurl' THEN 'Wrong Source'
--     WHEN event != 'page_view' THEN 'Wrong Event Type'
--   END as error_type
-- FROM `tagtoo-tracking.event_test.integrated_event`
-- WHERE partner_source = 'reurl' AND DATE(event_time) = check_date
--   AND (link NOT LIKE 'http%' OR partner_source != 'reurl' OR event != 'page_view')
-- LIMIT 10;

-- 如果URL媒合率偏低，檢查原因
-- SELECT
--   'URL媒合分析' as analysis_type,
--   COUNT(*) as total_urls,
--   COUNT(DISTINCT link) as unique_urls,
--   COUNT(CASE WHEN page.title IS NULL OR page.title = '' THEN 1 END) as no_title_count,
--   -- 檢查是否存在於 metadata 表中
--   COUNT(CASE WHEN link IN (
--     SELECT link FROM `tagtoo-tracking.tagtooad.reurl_link_metadata`
--   ) THEN 1 END) as found_in_metadata
-- FROM `tagtoo-tracking.event_test.integrated_event`
-- WHERE partner_source = 'reurl' AND DATE(event_time) = check_date;
