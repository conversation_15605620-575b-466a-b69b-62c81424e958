# ReURL BigQuery Scheduler Makefile
# 簡化部署和管理操作
# 支援動態目標表設定

.PHONY: help plan-dev apply-dev plan-staging apply-staging plan-prod apply-prod destroy-dev destroy-staging destroy-prod validate clean

# 預設目標
help:
	@echo "ReURL BigQuery Scheduler 管理命令"
	@echo ""
	@echo "🚀 部署命令:"
	@echo "  plan-dev       規劃部署到 dev 環境 (event_staging.integrated_event)"
	@echo "  apply-dev      部署到 dev 環境"
	@echo "  plan-staging   規劃部署到 staging 環境 (event_staging.integrated_event)"
	@echo "  apply-staging  部署到 staging 環境"
	@echo "  plan-prod      規劃部署到 prod 環境 (event.integrated_event)"
	@echo "  apply-prod     部署到 prod 環境"
	@echo ""
	@echo "🗑️  清理命令:"
	@echo "  destroy-dev     刪除 dev 環境的資源"
	@echo "  destroy-staging 刪除 staging 環境的資源"
	@echo "  destroy-prod    刪除 prod 環境的資源"
	@echo ""
	@echo "🔧 工具命令:"
	@echo "  validate       驗證 Terraform 配置和測試 SQL 生成"
	@echo "  test-sql       測試 SQL 模板生成"
	@echo "  clean          清理 Terraform 暫存文件"
	@echo "  logs           查看 BigQuery Data Transfer 執行記錄"
	@echo "  trigger-now    立即觸發 BigQuery Data Transfer 執行"
	@echo "  check-runs     查看 Data Transfer 執行狀態"
	@echo "  manual-help    顯示手動觸發的詳細說明"
	@echo ""
	@echo "📊 環境對應表:"
	@echo "  • dev:     event_staging.integrated_event (19:00)"
	@echo "  • staging: event_staging.integrated_event (19:00)"
	@echo "  • prod:    event_prod.integrated_event (19:00)"
	@echo ""

# 開發環境操作
plan-dev:
	@echo "📋 規劃部署到 dev 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && ./deploy.sh -e dev -a plan

apply-dev:
	@echo "🚀 部署到 dev 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && ./deploy.sh -e dev -a apply

destroy-dev:
	@echo "🗑️  刪除 dev 環境資源..."
	@cd terraform && ./deploy.sh -e dev -a destroy

# Staging 環境操作
plan-staging:
	@echo "📋 規劃部署到 staging 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && ./deploy.sh -e staging -a plan

apply-staging:
	@echo "🚀 部署到 staging 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && ./deploy.sh -e staging -a apply

destroy-staging:
	@echo "🗑️  刪除 staging 環境資源..."
	@cd terraform && ./deploy.sh -e staging -a destroy

# 生產環境操作
plan-prod:
	@echo "📋 規劃部署到 prod 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_prod.integrated_event"
	@cd terraform && ./deploy.sh -e prod -a plan

apply-prod:
	@echo "🚀 部署到 prod 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_prod.integrated_event"
	@cd terraform && ./deploy.sh -e prod -a apply

destroy-prod:
	@echo "🗑️  刪除 prod 環境資源..."
	@cd terraform && ./deploy.sh -e prod -a destroy

# 驗證和清理
validate:
	@echo "🔍 驗證 Terraform 配置..."
	@cd terraform && terraform validate
	@echo "✅ Terraform 配置驗證通過"
	@echo ""
	@echo "🧪 測試 SQL 模板生成..."
	@cd scripts && ./test_sql_generation.sh
	@echo "✅ SQL 模板生成測試通過"

clean:
	@echo "🧹 清理 Terraform 暫存文件..."
	@cd terraform && rm -rf .terraform.lock.hcl tfplan-* terraform.tfstate.backup
	@echo "✅ 清理完成"

# 監控和記錄
logs:
	@echo "查看 BigQuery Data Transfer 執行記錄..."
	@echo "請前往 Google Cloud Console:"
	@echo "1. BigQuery > Data transfers"
	@echo "2. 找到 'ReURL Daily Data Integration' 配置"
	@echo "3. 查看執行歷史和記錄"
	@echo ""
	@echo "或使用 gcloud 命令:"
	@echo "gcloud logging read 'resource.type=\"bigquery_dts_config\"' --limit=50"

# 測試命令
test-sql:
	@echo "🧪 測試 SQL 模板生成..."
	@cd scripts && ./test_sql_generation.sh

# 資料品質檢查
check-data:
	@echo "執行資料品質檢查..."
	@echo "請在 BigQuery Console 中執行以下檢查查詢:"
	@echo ""
	@echo "1. 快速資料檢查:"
	@cat sql/quick_data_check.sql
	@echo ""
	@echo "2. 詳細資料品質驗證:"
	@cat sql/data_quality_validation.sql

# 手動觸發和監控
# 使用方式:
#   make trigger-now                           # 使用預設值
#   make trigger-now REGION=us-central1        # 指定地區
#   make trigger-now AUTH=service CRED_PATH=/path/to/key.json  # 使用 service account
#   make trigger-now REGION=asia-east1 AUTH=service CRED_PATH=./sa-key.json
trigger-now:
	@echo "=== BigQuery Data Transfer 手動觸發 ==="
	@echo "參數設定:"
	@echo "  地區 (REGION): $(or $(REGION),asia-east1)"
	@echo "  認證方式 (AUTH): $(or $(AUTH),default)"
	@if [ "$(AUTH)" = "service" ]; then \
		echo "  Service Account Key: $(or $(CRED_PATH),未指定)"; \
		if [ -z "$(CRED_PATH)" ]; then \
			echo "❌ 錯誤: 使用 service account 認證時必須提供 CRED_PATH"; \
			echo "範例: make trigger-now AUTH=service CRED_PATH=/path/to/key.json"; \
			exit 1; \
		fi; \
		if [ ! -f "$(CRED_PATH)" ]; then \
			echo "❌ 錯誤: Service Account Key 文件不存在: $(CRED_PATH)"; \
			exit 1; \
		fi; \
	fi
	@echo ""
	@echo "正在觸發執行..."
	@if [ "$(AUTH)" = "service" ]; then \
		export GOOGLE_APPLICATION_CREDENTIALS="$(CRED_PATH)"; \
		ACCESS_TOKEN=$$(gcloud auth application-default print-access-token 2>/dev/null || \
			(echo "❌ 無法取得 service account access token" && exit 1)); \
	else \
		ACCESS_TOKEN=$$(gcloud auth print-access-token 2>/dev/null || \
			(echo "❌ 無法取得 access token，請先執行 gcloud auth login" && exit 1)); \
	fi; \
	curl -X POST \
		"https://bigquerydatatransfer.googleapis.com/v1/projects/tagtoo-tracking/locations/$(or $(REGION),asia-east1)/transferConfigs/6859635c-0000-2750-9f7a-582429ace874:startManualRuns" \
		-H "Authorization: Bearer $$ACCESS_TOKEN" \
		-H "Content-Type: application/json" \
		-d '{"requestedRunTime": "'$$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}' \
		-s | jq '.' || echo "❌ 觸發失敗，請檢查網路連線和權限設定"

check-runs:
	@echo "=== 查看 Data Transfer 執行狀態 ==="
	@echo "參數設定:"
	@echo "  地區 (REGION): $(or $(REGION),asia-east1)"
	@echo "  認證方式 (AUTH): $(or $(AUTH),default)"
	@if [ "$(AUTH)" = "service" ]; then \
		echo "  Service Account Key: $(or $(CRED_PATH),未指定)"; \
		if [ -z "$(CRED_PATH)" ]; then \
			echo "❌ 錯誤: 使用 service account 認證時必須提供 CRED_PATH"; \
			exit 1; \
		fi; \
	fi
	@echo ""
	@if [ "$(AUTH)" = "service" ]; then \
		export GOOGLE_APPLICATION_CREDENTIALS="$(CRED_PATH)"; \
		ACCESS_TOKEN=$$(gcloud auth application-default print-access-token 2>/dev/null || \
			(echo "❌ 無法取得 service account access token" && exit 1)); \
	else \
		ACCESS_TOKEN=$$(gcloud auth print-access-token 2>/dev/null || \
			(echo "❌ 無法取得 access token" && exit 1)); \
	fi; \
	curl -X GET \
		"https://bigquerydatatransfer.googleapis.com/v1/projects/tagtoo-tracking/locations/$(or $(REGION),asia-east1)/transferConfigs/6859635c-0000-2750-9f7a-582429ace874/runs" \
		-H "Authorization: Bearer $$ACCESS_TOKEN" \
		-s | jq '.transferRuns[]? | {name: .name, state: .state, startTime: .startTime, endTime: .endTime, errorStatus: .errorStatus}' || echo "❌ 查詢失敗"

manual-help:
	@echo "=== 手動觸發 BigQuery Data Transfer 指南 ==="
	@echo ""
	@echo "方法 1: 使用 BigQuery Console (推薦)"
	@echo "  URL: https://console.cloud.google.com/bigquery/transfers?project=tagtoo-tracking"
	@echo "  1. 找到 'ReURL Daily Data Integration - DEV'"
	@echo "  2. 點擊 'RUN NOW' 立即執行"
	@echo "  3. 或點擊 'SCHEDULE BACKFILL' 回填特定日期"
	@echo ""
	@echo "方法 2: 使用 Makefile 命令"
	@echo "  基本用法:"
	@echo "    make trigger-now                    # 使用預設設定 (asia-east1, default auth)"
	@echo "    make check-runs                     # 查看執行狀態"
	@echo ""
	@echo "  進階參數:"
	@echo "    REGION=<region>                     # 指定地區 (預設: asia-east1)"
	@echo "    AUTH=<auth_type>                    # 認證方式: default 或 service (預設: default)"
	@echo "    CRED_PATH=<path>                    # Service Account Key 路徑 (AUTH=service 時必需)"
	@echo ""
	@echo "  範例:"
	@echo "    make trigger-now REGION=us-central1"
	@echo "    make trigger-now AUTH=service CRED_PATH=./sa-key.json"
	@echo "    make trigger-now REGION=asia-east1 AUTH=service CRED_PATH=/path/to/key.json"
	@echo "    make check-runs REGION=us-central1 AUTH=service CRED_PATH=./sa-key.json"
	@echo ""
	@echo "方法 3: 使用腳本"
	@echo "  ./scripts/manual_trigger.sh         # 顯示詳細的 API 命令"
	@echo ""
	@echo "📝 認證方式說明:"
	@echo "  • default: 使用 gcloud auth login 的用戶認證"
	@echo "  • service: 使用 Service Account Key 文件認證"
	@echo ""
	@echo "🌍 支援的地區:"
	@echo "  • asia-east1 (台灣)"
	@echo "  • us-central1 (美國中部)"
	@echo "  • europe-west1 (歐洲西部)"
	@echo "  • 其他 BigQuery 支援的地區"
	@echo ""
