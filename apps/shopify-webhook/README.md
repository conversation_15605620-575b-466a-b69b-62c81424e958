# Shopify Webhook Data Partner

> 透過 Shopify webhook 整合電商資料的資料合作夥伴服務

## 概述

此服務處理無法在 Shopify 電商平台埋追蹤碼的客戶（如 Mercella），透過 Shopify 後台設定的 webhook 來獲取使用者行為資料。

### 目標

- 接收 Shopify webhook 資料
- 處理並標準化資料格式
- 整合到 integrated_event BigQuery 表格

### 資料夾架構

```
shopify-webhook/
├── src/                    # 應用程式碼（同事自行實作）
├── tests/                  # 測試程式碼（同事自行實作）
├── terraform/              # 服務特定基礎設施（DevOps 團隊負責）
└── README.md               # 此檔案
```

### 開發規範

1. **測試**: 確保 `make test` 可以運行所有測試
2. **部署**: 確保 `make deploy-dev` 和 `make deploy-prod` 可以正常部署
3. **監控**: 服務需要提供健康檢查端點 `/health`

### 負責範圍

- **應用開發團隊**: 實作 `src/` 和 `tests/` 的內容
- **DevOps 團隊**: 負責 `terraform/` 和跨服務整合

---

**維護人員**: <EMAIL>
**最後更新**: 2025-06-05
**狀態**: 架構規劃完成，等待實作 🏗️
