# Shopify Webhook Data Partner

> 透過 Shopify webhook 整合電商資料的資料合作夥伴服務

## 概述

本服務專為無法於 Shopify 電商平台埋設追蹤碼的客戶（如 Mercella）設計，透過 Shopify 後台 webhook 將訂單與用戶行為資料同步至 Google Firestore，並每日自動整合至 BigQuery，供後續資料分析與整合。

## 架構與功能

- **API 端點**
  - `GET /health`：健康檢查，回傳服務狀態、版本與時間戳記
  - `POST /sync`：手動觸發指定日期的 Firestore → BigQuery 同步（預設為昨日資料）

- **資料流**
  1. Shopify webhook 將訂單資料寫入 Firestore
  2. 服務每日自動（或手動）將 Firestore 資料同步至 BigQuery
  3. BigQuery 以 SQL 腳本進行資料整合與查詢

- **主要程式**
  - `src/main.py`：Flask 應用，負責 API 端點、Firestore 讀取、BigQuery 寫入
  - `tests/test_main.py`：涵蓋健康檢查、資料同步等單元測試
  - `sql/shopify_daily_query.sql`：BigQuery 資料整合查詢腳本

- **基礎設施**
  - `terraform/main.tf`：Cloud Run 服務、Cloud Scheduler 定時觸發、BigQuery Data Transfer 設定
  - 支援 dev/prod/staging 多環境部署

## 開發與部署

- **測試**：`make test` 執行所有單元測試
- **部署**：
  - `make deploy-dev` 部署至 dev 環境
  - `make deploy-prod` 部署至 prod 環境
- **健康檢查**：Cloud Run 會自動監控 `/health` 端點
- **自動同步**：Cloud Scheduler 每日自動呼叫 `/sync` 端點

## 目錄結構

```
shopify-webhook/
├── src/                    # Flask 應用主程式
├── tests/                  # 測試程式
├── sql/                    # BigQuery 查詢腳本
├── terraform/              # 基礎設施 IaC
├── requirements.txt        # Python 依賴
├── Dockerfile              # 容器化設定
├── docker-compose.yml      # 本地開發用
└── README.md               # 本說明文件
```

## 參數與環境變數

- `PROJECT_ID`、`BQ_PROJECT`、`DATASET_ID`、`TABLE_ID`、`COLLECTION_NAME`、`GOOGLE_APPLICATION_CREDENTIALS` 等，詳見 `src/main.py` 及 terraform 變數