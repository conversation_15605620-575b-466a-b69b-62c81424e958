# Shopify Webhook Makefile
# 簡化部署和管理操作
# 支援 Firestore 到 BigQuery 的資料同步

.PHONY: help plan-dev apply-dev plan-staging apply-staging plan-prod apply-prod destroy-dev destroy-staging destroy-prod validate clean build test logs trigger-sync check-sync manual-help

# 預設目標
help:
	@echo "Shopify Webhook 管理命令"
	@echo ""
	@echo "🚀 部署命令:"
	@echo "  plan-dev       規劃部署到 dev 環境"
	@echo "  apply-dev      部署到 dev 環境"
	@echo "  plan-staging   規劃部署到 staging 環境"
	@echo "  apply-staging  部署到 staging 環境"
	@echo "  plan-prod      規劃部署到 prod 環境"
	@echo "  apply-prod     部署到 prod 環境"
	@echo ""
	@echo "🗑️  清理命令:"
	@echo "  destroy-dev     刪除 dev 環境的資源"
	@echo "  destroy-staging 刪除 staging 環境的資源"
	@echo "  destroy-prod    刪除 prod 環境的資源"
	@echo ""
	@echo "🔧 開發命令:"
	@echo "  build          建置 Docker 映像"
	@echo "  test           執行單元測試"
	@echo "  validate       驗證 Terraform 配置"
	@echo "  tf-fmt         格式化 Terraform 文件"
	@echo "  tf-fmt-check   檢查 Terraform 格式"
	@echo "  clean          清理暫存文件"
	@echo ""
	@echo "📊 監控命令:"
	@echo "  logs           查看 Cloud Run 日誌"
	@echo "  trigger-sync   手動觸發資料同步"
	@echo "  check-sync     檢查同步狀態"
	@echo "  manual-help    顯示手動操作的詳細說明"
	@echo ""
	@echo "📊 環境對應表:"
	@echo "  • dev:     tagtoo-tracking.event_staging.integrated_event"
	@echo "  • staging: tagtoo-tracking.event_staging.integrated_event"
	@echo "  • prod:    tagtoo-tracking.event.integrated_event"
	@echo ""

# 開發環境操作
plan-dev:
	@echo "📋 規劃部署到 dev 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && terraform plan -var-file="dev.tfvars" -out=tfplan-dev

apply-dev:
	@echo "🚀 部署到 dev 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && terraform apply tfplan-dev

destroy-dev:
	@echo "🗑️  刪除 dev 環境資源..."
	@cd terraform && terraform destroy -var-file="dev.tfvars"

# Staging 環境操作
plan-staging:
	@echo "📋 規劃部署到 staging 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && terraform plan -var-file="staging.tfvars" -out=tfplan-staging

apply-staging:
	@echo "🚀 部署到 staging 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event_staging.integrated_event"
	@cd terraform && terraform apply tfplan-staging

destroy-staging:
	@echo "🗑️  刪除 staging 環境資源..."
	@cd terraform && terraform destroy -var-file="staging.tfvars"

# 生產環境操作
plan-prod:
	@echo "📋 規劃部署到 prod 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event.integrated_event"
	@cd terraform && terraform plan -var-file="prod.tfvars" -out=tfplan-prod

apply-prod:
	@echo "🚀 部署到 prod 環境..."
	@echo "🎯 目標表: tagtoo-tracking.event.integrated_event"
	@cd terraform && terraform apply tfplan-prod

destroy-prod:
	@echo "🗑️  刪除 prod 環境資源..."
	@cd terraform && terraform destroy -var-file="prod.tfvars"

# 開發工具
build:
	@echo "🔨 建置 Docker 映像..."
	docker build -t shopify-webhook:latest .
	@echo "✅ Docker 映像建置完成"

test:
	@echo "🧪 執行單元測試..."
	@echo "📦 檢查並安裝測試依賴..."
	@python -c "import pytest" 2>/dev/null || pip install pytest pytest-cov pytest-mock
	@echo "📦 安裝應用程式依賴..."
	@pip install -r requirements.txt
	@echo "🧪 執行測試..."
	TESTING=1 python -m pytest tests/ -v
	@echo "✅ 測試完成"

validate:
	@echo "🔍 驗證 Terraform 配置..."
	@cd terraform && terraform validate
	@echo "✅ Terraform 配置驗證通過"
	@echo ""
	@echo "🔍 驗證 Python 程式碼..."
	@python -c "import src.main; print('✅ Python 程式碼語法檢查通過')"
	@echo "✅ 程式碼驗證完成"

tf-fmt:
	@echo "📝 格式化 Terraform 文件..."
	@cd terraform && terraform fmt -recursive
	@echo "✅ Terraform 格式化完成"

tf-fmt-check:
	@echo "📝 檢查 Terraform 格式..."
	@cd terraform && terraform fmt -check -recursive
	@echo "✅ Terraform 格式檢查通過"

clean:
	@echo "🧹 清理暫存文件..."
	@cd terraform && rm -rf .terraform.lock.hcl tfplan-* terraform.tfstate.backup
	@echo "✅ 清理完成"

# 監控和記錄
logs:
	@echo "📋 查看 Cloud Run 日誌..."
	@echo "請前往 Google Cloud Console:"
	@echo "1. Cloud Run > shopify-webhook-[環境]"
	@echo "2. 點擊 'LOGS' 標籤"
	@echo "3. 查看執行記錄"
	@echo ""
	@echo "或使用 gcloud 命令:"
	@echo "gcloud logging read 'resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"shopify-webhook-[環境]\"' --limit=50"

# 手動觸發同步
# 使用方式:
#   make trigger-sync                           # 使用預設值 (昨天)
#   make trigger-sync DATE=2024-01-15           # 指定日期
#   make trigger-sync ENV=dev                   # 指定環境
trigger-sync:
	@echo "=== Shopify Webhook 手動觸發同步 ==="
	@echo "參數設定:"
	@echo "  環境 (ENV): $(or $(ENV),prod)"
	@echo "  日期 (DATE): $(or $(DATE),$(shell date -d 'yesterday' +%Y-%m-%d))"
	@echo ""
	@echo "正在觸發同步..."
	@SERVICE_URL=$$(cd terraform && terraform output -raw service_url 2>/dev/null || echo "https://shopify-webhook-$(or $(ENV),prod)-[hash]-[region].run.app"); \
	curl -X POST \
		"$$SERVICE_URL/sync" \
		-H "Content-Type: application/json" \
		-d '{"date": "$(or $(DATE),$(shell date -d 'yesterday' +%Y-%m-%d))"}' \
		-s | jq '.' || echo "❌ 觸發失敗，請檢查服務狀態和網路連線"

# 檢查同步狀態
check-sync:
	@echo "=== 檢查同步狀態 ==="
	@echo "參數設定:"
	@echo "  環境 (ENV): $(or $(ENV),prod)"
	@echo ""
	@echo "正在檢查服務狀態..."
	@SERVICE_URL=$$(cd terraform && terraform output -raw service_url 2>/dev/null || echo "https://shopify-webhook-$(or $(ENV),prod)-[hash]-[region].run.app"); \
	curl -X GET \
		"$$SERVICE_URL/health" \
		-s | jq '.' || echo "❌ 服務檢查失敗"

# 本地開發
dev:
	@echo "🚀 啟動本地開發伺服器..."
	@echo "請確保已設定以下環境變數:"
	@echo "  - GOOGLE_APPLICATION_CREDENTIALS"
	@echo "  - PROJECT_ID"
	@echo "  - BQ_PROJECT"
	@echo ""
	@echo "啟動伺服器於 http://localhost:8080"
	@python src/main.py

# 健康檢查
health:
	@echo "🏥 執行健康檢查..."
	@curl -f http://localhost:8080/health || echo "❌ 健康檢查失敗"

# 手動操作說明
manual-help:
	@echo "=== Shopify Webhook 手動操作指南 ==="
	@echo ""
	@echo "方法 1: 使用 Cloud Run Console (推薦)"
	@echo "  URL: https://console.cloud.google.com/run?project=tagtoo-tracking"
	@echo "  1. 找到 'shopify-webhook-[環境]' 服務"
	@echo "  2. 點擊 'LOGS' 查看執行記錄"
	@echo "  3. 或使用 'TESTING' 標籤測試 API"
	@echo ""
	@echo "方法 2: 使用 Makefile 命令"
	@echo "  基本用法:"
	@echo "    make trigger-sync                    # 同步昨天的資料"
	@echo "    make check-sync                      # 檢查服務狀態"
	@echo "    make logs                            # 查看日誌"
	@echo ""
	@echo "  進階參數:"
	@echo "    ENV=<environment>                    # 指定環境: dev, staging, prod (預設: prod)"
	@echo "    DATE=<YYYY-MM-DD>                    # 指定同步日期 (預設: 昨天)"
	@echo ""
	@echo "  範例:"
	@echo "    make trigger-sync ENV=dev"
	@echo "    make trigger-sync DATE=2024-01-15"
	@echo "    make trigger-sync ENV=staging DATE=2024-01-15"
	@echo "    make check-sync ENV=prod"
	@echo ""
	@echo "方法 3: 直接 API 調用"
	@echo "  健康檢查:"
	@echo "    curl -X GET [SERVICE_URL]/health"
	@echo ""
	@echo "  手動同步:"
	@echo "    curl -X POST [SERVICE_URL]/sync \\"
	@echo "      -H 'Content-Type: application/json' \\"
	@echo "      -d '{\"date\": \"2024-01-15\"}'"
	@echo ""
	@echo "📝 環境說明:"
	@echo "  • dev:     開發環境，用於測試"
	@echo "  • staging: 預發布環境，用於驗證"
	@echo "  • prod:    生產環境，正式運行"
	@echo ""
	@echo "🕐 排程說明:"
	@echo "  • Cloud Scheduler: 每天 00:05 (台灣時間) 自動同步"
	@echo "  • BigQuery Scheduled Query: 每天 00:03 (台灣時間) 執行 SQL"
	@echo ""
	@echo "📊 資料流程:"
	@echo "  Firestore → Python 處理 → BigQuery"
	@echo ""

# 快速部署 (開發用)
quick-deploy:
	@echo "⚡ 快速部署到開發環境..."
	@make build
	@make plan-dev
	@make apply-dev
	@echo "✅ 快速部署完成"

# 回滾到上一個版本
rollback:
	@echo "🔄 回滾到上一個版本..."
	@cd terraform && terraform apply -var-file="$(or $(ENV),prod).tfvars" -target=google_cloud_run_service.main
	@echo "✅ 回滾完成" 