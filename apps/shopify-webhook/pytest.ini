# shopify-webhook pytest 配置

[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
minversion = 7.0
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=70
    --durations=10
markers =
    unit: 單元測試
    integration: 整合測試
    e2e: 端到端測試
    slow: 執行時間較長的測試
    external: 需要外部服務的測試
    mock: 使用 mock 的測試
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    venv
    env 