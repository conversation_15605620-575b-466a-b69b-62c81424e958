# shopify-webhook 本地開發環境
# 使用 docker-compose up -d 啟動開發環境

version: '3.8'

services:
  shopify-webhook:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=dev
      - PROJECT_ID=tagtoo-tracking-dev
      - FIRESTORE_EMULATOR_HOST=firestore:8080
      - BIGQUERY_EMULATOR_HOST=bigquery:9050
    volumes:
      - ./src:/app/src
      - ./tests:/app/tests
    depends_on:
      - firestore
      - bigquery
    networks:
      - shopify-webhook-network
    restart: unless-stopped

  firestore:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    ports:
      - "8081:8080"
    command: >
      sh -c "gcloud emulators firestore start \
             --host-port=0.0.0.0:8080 \
             --project=tagtoo-tracking-dev"
    networks:
      - shopify-webhook-network
    restart: unless-stopped

  bigquery:
    image: ghcr.io/goccy/bigquery-emulator:latest
    ports:
      - "9050:9050"
    environment:
      - PROJECT_ID=tagtoo-tracking-dev
      - DATASET_ID=integrated_event
    volumes:
      - ./terraform/schema:/schema
    networks:
      - shopify-webhook-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - shopify-webhook-network
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - shopify-webhook-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
    networks:
      - shopify-webhook-network
    restart: unless-stopped

networks:
  shopify-webhook-network:
    driver: bridge

volumes:
  grafana-storage: 